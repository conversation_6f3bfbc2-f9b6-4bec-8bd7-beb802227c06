/**
 * Recompute correct usage time for a user by reading actual MP3 files
 *
 * This script:
 * 1. Finds all documents for a user
 * 2. For each page, reads the actual MP3 file(s) from storage (S3 or local)
 * 3. Calculates real duration from the audio files
 * 4. Updates PageAudio.duration with correct values
 * 5. Updates the user's usage record for the current billing cycle
 *
 * Supports both:
 * - Full audio.mp3 files
 * - Chunked audio (chunk-1.mp3, chunk-2.mp3, etc.)
 *
 * Usage:
 *   npx tsx scripts/recompute-usage-from-audio.ts <email>
 *   npx tsx scripts/recompute-usage-from-audio.ts <email> --dry-run
 *   npx tsx scripts/recompute-usage-from-audio.ts <email> --fix-durations
 */

import { prisma } from '../lib/prisma';
import { fileExistsFlexible, readBufferFlexible, readTextFlexible } from '../lib/file-service';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';
import os from 'os';

const execAsync = promisify(exec);

interface AudioFileInfo {
  path: string;
  duration: number;
  exists: boolean;
}

/**
 * Get duration of an MP3 file in seconds using ffprobe
 */
async function getMp3Duration(filePath: string): Promise<number | null> {
  try {
    const exists = await fileExistsFlexible(filePath);
    if (!exists) {
      return null;
    }

    // Download file to temp location for ffprobe
    const buffer = await readBufferFlexible(filePath);
    const tempFile = path.join(os.tmpdir(), `audio-${Date.now()}-${Math.random().toString(36).slice(2)}.mp3`);

    try {
      await fs.writeFile(tempFile, buffer);

      // Use ffprobe to get duration
      const { stdout } = await execAsync(
        `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${tempFile}"`
      );

      const duration = parseFloat(stdout.trim());

      // Clean up temp file
      await fs.unlink(tempFile).catch(() => {});

      if (isNaN(duration) || duration <= 0) {
        return null;
      }

      return duration;
    } catch (error) {
      // Clean up temp file on error
      await fs.unlink(tempFile).catch(() => {});
      throw error;
    }
  } catch (error) {
    console.error(`   ⚠️  Error reading ${filePath}:`, (error as Error).message);
    return null;
  }
}

/**
 * Get total duration for a page's audio (either full audio.mp3 or sum of chunks)
 */
async function getPageAudioDuration(
  documentId: string,
  pageNumber: number,
  language: string,
  voice: string
): Promise<{ duration: number; files: AudioFileInfo[]; source: 'full' | 'chunks' | 'none' }> {
  const audioDir = [
    'documents',
    documentId,
    'pages',
    `page-${pageNumber}`,
    'audio',
    language,
    voice,
  ].join('/');

  const files: AudioFileInfo[] = [];

  // First, try to get the full audio.mp3
  const fullAudioPath = path.posix.join(audioDir, 'audio.mp3');
  const fullDuration = await getMp3Duration(fullAudioPath);

  if (fullDuration !== null) {
    files.push({
      path: fullAudioPath,
      duration: fullDuration,
      exists: true,
    });
    return { duration: fullDuration, files, source: 'full' };
  }

  // If no full audio, check for chunks
  const chunksDir = path.posix.join(audioDir, 'chunks');
  const chunksMetaPath = path.posix.join(chunksDir, 'meta.json');

  try {
    const metaExists = await fileExistsFlexible(chunksMetaPath);
    if (metaExists) {
      const metaRaw = await readTextFlexible(chunksMetaPath, 'utf-8');
      const meta = JSON.parse(metaRaw);
      const totalChunks = Number(meta?.total || 0);

      if (totalChunks > 0) {
        let totalDuration = 0;

        for (let i = 1; i <= totalChunks; i++) {
          const chunkPath = path.posix.join(chunksDir, `chunk-${i}.mp3`);
          const chunkDuration = await getMp3Duration(chunkPath);

          if (chunkDuration !== null) {
            files.push({
              path: chunkPath,
              duration: chunkDuration,
              exists: true,
            });
            totalDuration += chunkDuration;
          } else {
            files.push({
              path: chunkPath,
              duration: 0,
              exists: false,
            });
          }
        }

        if (files.some(f => f.exists)) {
          return { duration: totalDuration, files, source: 'chunks' };
        }
      }
    }
  } catch (error) {
    // Chunks metadata not found or error reading
  }

  return { duration: 0, files: [], source: 'none' };
}

async function recomputeUsageFromAudio(
  email: string,
  dryRun: boolean = false,
  fixDurations: boolean = false
) {
  console.log(`Looking up user: ${email}`);

  const user = await prisma.user.findFirst({
    where: { email },
    include: {
      plan: {
        select: {
          name: true,
          includedListeningTime: true,
        },
      },
    },
  });

  if (!user) {
    console.error(`❌ User not found: ${email}`);
    process.exit(1);
  }

  console.log(`\n✅ Found user: ${user.id}`);
  console.log(`   Name: ${user.name || '(none)'}`);
  console.log(`   Email: ${user.email || '(none)'}`);
  console.log(`   Plan: ${user.plan.name} (${user.plan.includedListeningTime ? formatTime(user.plan.includedListeningTime) : 'unlimited'})`);

  // Get current billing cycle
  const now = new Date();
  const y = now.getUTCFullYear();
  const m = now.getUTCMonth();
  const cycleStart = new Date(Date.UTC(y, m, 1, 0, 0, 0));
  const cycleEnd = new Date(Date.UTC(y, m + 1, 1, 0, 0, 0));

  console.log(`\n📅 Current billing cycle: ${cycleStart.toISOString().split('T')[0]} to ${cycleEnd.toISOString().split('T')[0]}`);

  // Get all documents for this user
  const documents = await prisma.document.findMany({
    where: { userId: user.id },
    select: {
      id: true,
      originalFilename: true,
      targetLanguage: true,
      voice: true,
      pageCount: true,
      createdAt: true,
    },
    orderBy: { createdAt: 'desc' },
  });

  console.log(`\n📚 Found ${documents.length} document(s)`);
  console.log(`\n${'='.repeat(70)}`);

  let totalCorrectDuration = 0;
  const documentDetails: Array<{
    filename: string;
    pages: number;
    duration: number;
    language: string;
    voice: string;
    audioUpdates: Array<{ pageAudioId: string; oldDuration: number; newDuration: number }>;
  }> = [];

  for (const doc of documents) {
    console.log(`\n📄 ${doc.originalFilename}`);
    console.log(`   Document ID: ${doc.id}`);
    console.log(`   Language: ${doc.targetLanguage || 'not set'}`);
    console.log(`   Voice: ${doc.voice || 'not set'}`);

    // Get all pages for this document
    const pages = await prisma.page.findMany({
      where: { documentId: doc.id },
      select: {
        id: true,
        pageNumber: true,
        pageAudios: {
          where: {
            language: doc.targetLanguage || undefined,
            voice: doc.voice || undefined,
            s3KeyAudioMp3: { not: null },
          },
          select: {
            id: true,
            duration: true,
            language: true,
            voice: true,
          },
        },
      },
      orderBy: { pageNumber: 'asc' },
    });

    console.log(`   Pages: ${pages.length}`);

    let docDuration = 0;
    let audioPages = 0;
    const audioUpdates: Array<{ pageAudioId: string; oldDuration: number; newDuration: number }> = [];

    for (const page of pages) {
      if (!page.pageAudios.length) {
        console.log(`   ○ Page ${page.pageNumber}: no audio record`);
        continue;
      }

      const audio = page.pageAudios[0];
      const dbDuration = audio.duration || 0;

      // Get actual duration from audio files
      const result = await getPageAudioDuration(
        doc.id,
        page.pageNumber,
        audio.language,
        audio.voice
      );

      const actualDuration = Math.round(result.duration);

      if (result.source === 'none') {
        console.log(`   ○ Page ${page.pageNumber}: no audio files found`);
        continue;
      }

      const symbol = actualDuration === dbDuration ? '✓' : '⚠️';
      const mismatch = actualDuration !== dbDuration ? ` (DB: ${formatTime(dbDuration)})` : '';

      console.log(`   ${symbol} Page ${page.pageNumber}: ${formatTime(actualDuration)}${mismatch} [${result.source}]`);

      if (result.files.length > 0 && result.source === 'chunks') {
        result.files.forEach((f, i) => {
          const status = f.exists ? '✓' : '✗';
          console.log(`      ${status} chunk-${i + 1}.mp3: ${f.exists ? formatTime(f.duration) : 'missing'}`);
        });
      }

      docDuration += actualDuration;
      audioPages++;

      if (actualDuration !== dbDuration) {
        audioUpdates.push({
          pageAudioId: audio.id,
          oldDuration: dbDuration,
          newDuration: actualDuration,
        });
      }
    }

    if (docDuration > 0) {
      totalCorrectDuration += docDuration;
      documentDetails.push({
        filename: doc.originalFilename,
        pages: audioPages,
        duration: docDuration,
        language: doc.targetLanguage || 'unknown',
        voice: doc.voice || 'unknown',
        audioUpdates,
      });
      console.log(`   📊 Document total: ${formatTime(docDuration)} (${audioPages} pages with audio)`);
    } else {
      console.log(`   ⚠️  No audio found for this document`);
    }
  }

  console.log(`\n${'='.repeat(70)}`);
  console.log(`📊 SUMMARY`);
  console.log(`${'='.repeat(70)}`);

  if (documentDetails.length > 0) {
    console.log(`\nDocuments with audio:`);
    documentDetails.forEach((d, i) => {
      console.log(`${i + 1}. ${d.filename}`);
      console.log(`   ${d.pages} pages, ${formatTime(d.duration)} (${d.language}/${d.voice})`);
      if (d.audioUpdates.length > 0) {
        console.log(`   ⚠️  ${d.audioUpdates.length} page(s) with duration mismatch`);
      }
    });
  }

  console.log(`\n✅ Calculated correct total from audio files: ${formatTime(totalCorrectDuration)} (${totalCorrectDuration}s)`);

  // Get current usage record
  const currentUsage = await prisma.usage.findUnique({
    where: {
      userId_cycleStart: {
        userId: user.id,
        cycleStart,
      },
    },
  });

  if (currentUsage) {
    console.log(`\n📈 Current usage record:`);
    console.log(`   Listening time: ${formatTime(currentUsage.listeningTime)} (${currentUsage.listeningTime}s)`);
    console.log(`   Documents processed: ${currentUsage.documentsProcessed}`);
    console.log(`   Pages processed: ${currentUsage.pagesProcessed}`);

    const difference = currentUsage.listeningTime - totalCorrectDuration;

    if (difference !== 0) {
      console.log(`\n⚠️  DISCREPANCY DETECTED!`);
      console.log(`   Current DB: ${formatTime(currentUsage.listeningTime)} (${currentUsage.listeningTime}s)`);
      console.log(`   Actual files: ${formatTime(totalCorrectDuration)} (${totalCorrectDuration}s)`);
      console.log(`   Difference: ${formatTime(Math.abs(difference))} ${difference > 0 ? 'OVER' : 'UNDER'}`);
    } else {
      console.log(`\n✅ Usage matches actual audio duration!`);
    }
  } else {
    console.log(`\n⚠️  No usage record found for current cycle`);
    if (totalCorrectDuration > 0) {
      console.log(`   User has ${formatTime(totalCorrectDuration)} of audio but no usage tracked`);
    }
  }

  // Check for PageAudio duration mismatches
  const totalAudioUpdates = documentDetails.reduce((sum, d) => sum + d.audioUpdates.length, 0);

  if (totalAudioUpdates > 0) {
    console.log(`\n⚠️  PAGEAUDIO DURATION MISMATCHES FOUND!`);
    console.log(`   ${totalAudioUpdates} page(s) have incorrect duration in database`);

    documentDetails.forEach(d => {
      if (d.audioUpdates.length > 0) {
        console.log(`\n   ${d.filename}:`);
        d.audioUpdates.forEach(u => {
          console.log(`      PageAudio ${u.pageAudioId}: ${formatTime(u.oldDuration)} → ${formatTime(u.newDuration)}`);
        });
      }
    });
  }

  if (dryRun) {
    console.log(`\n${'='.repeat(70)}`);
    console.log(`🔍 DRY RUN - No changes made`);
    console.log(`${'='.repeat(70)}`);
    console.log(`\nTo apply fixes, run with:`);
    if (totalAudioUpdates > 0) {
      console.log(`  --fix-durations    Update PageAudio.duration values`);
    }
    if (!currentUsage || currentUsage.listeningTime !== totalCorrectDuration) {
      console.log(`  (without --dry-run) Update Usage.listeningTime`);
    }
    return;
  }

  // Fix PageAudio durations if requested
  if (fixDurations && totalAudioUpdates > 0) {
    console.log(`\n${'='.repeat(70)}`);
    console.log(`🔧 Fixing PageAudio durations...`);
    console.log(`${'='.repeat(70)}`);

    for (const doc of documentDetails) {
      for (const update of doc.audioUpdates) {
        await prisma.pageAudio.update({
          where: { id: update.pageAudioId },
          data: { duration: update.newDuration },
        });
        console.log(`✅ Updated PageAudio ${update.pageAudioId}: ${formatTime(update.oldDuration)} → ${formatTime(update.newDuration)}`);
      }
    }
  }

  // Update usage
  const usageDifference = (currentUsage?.listeningTime || 0) - totalCorrectDuration;

  if (usageDifference !== 0) {
    console.log(`\n${'='.repeat(70)}`);
    console.log(`🔧 Fixing usage record...`);
    console.log(`${'='.repeat(70)}`);

    if (currentUsage) {
      await prisma.usage.update({
        where: {
          userId_cycleStart: {
            userId: user.id,
            cycleStart,
          },
        },
        data: {
          listeningTime: totalCorrectDuration,
        },
      });
      console.log(`✅ Updated usage: ${formatTime(currentUsage.listeningTime)} → ${formatTime(totalCorrectDuration)}`);
    } else if (totalCorrectDuration > 0) {
      await prisma.usage.create({
        data: {
          userId: user.id,
          cycleStart,
          cycleEnd,
          listeningTime: totalCorrectDuration,
          documentsProcessed: documentDetails.length,
          pagesProcessed: documentDetails.reduce((sum, d) => sum + d.pages, 0),
        },
      });
      console.log(`✅ Created usage record: ${formatTime(totalCorrectDuration)}`);
    }
  }

  // Show remaining time
  if (user.plan.includedListeningTime) {
    const remaining = user.plan.includedListeningTime - totalCorrectDuration;
    console.log(`\n⏱️  Remaining time: ${formatTime(Math.max(0, remaining))} of ${formatTime(user.plan.includedListeningTime)}`);

    if (remaining < 0) {
      console.log(`   ⚠️  User has exceeded their plan limit by ${formatTime(Math.abs(remaining))}`);
    }
  }

  console.log(`\n${'='.repeat(70)}`);
  console.log(`✅ All fixes applied!`);
  console.log(`${'='.repeat(70)}`);
}

function formatTime(seconds: number): string {
  if (!Number.isFinite(seconds) || seconds < 0) return '0:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// Parse command line arguments
const args = process.argv.slice(2);
const email = args.find(arg => !arg.startsWith('--'));
const dryRun = args.includes('--dry-run');
const fixDurations = args.includes('--fix-durations');

if (!email) {
  console.error('Usage: npx tsx scripts/recompute-usage-from-audio.ts <email> [options]');
  console.error('');
  console.error('Examples:');
  console.error('  npx tsx scripts/recompute-usage-from-audio.ts <EMAIL> --dry-run');
  console.error('  npx tsx scripts/recompute-usage-from-audio.ts <EMAIL>');
  console.error('  npx tsx scripts/recompute-usage-from-audio.ts <EMAIL> --fix-durations');
  console.error('');
  console.error('Options:');
  console.error('  --dry-run        Show what would be changed without making any updates');
  console.error('  --fix-durations  Also update PageAudio.duration values (not just Usage)');
  process.exit(1);
}

recomputeUsageFromAudio(email, dryRun, fixDurations)
  .then(() => {
    console.log('\n✨ Done!\n');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error:', error);
    console.error(error.stack);
    process.exit(1);
  });
