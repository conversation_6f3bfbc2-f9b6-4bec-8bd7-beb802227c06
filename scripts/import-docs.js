#!/usr/bin/env node
/*
  Import existing local documents from data/documents into the database.

  - Scans directories under ./data/documents
  - For each directory containing original.pdf, creates a Document row with id equal to the folder name
  - Derives basic metadata (sizeBytes, pageCount) using pdf-lib
  - Seeds Page rows (status=pending) for pageCount
  - Skips docs already present unless --force is passed

  Usage:
    node scripts/import-docs.js [--force]
*/

const fs = require('fs');
const fsp = fs.promises;
const path = require('path');
const { PrismaClient } = require('@prisma/client');

async function loadPdfLib() {
  try {
    // Support both CJS and ESM builds
    return require('pdf-lib');
  } catch (_) {
    return await import('pdf-lib');
  }
}

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `[${year}-${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

function log(...args) {
  console.log(getTimestamp(), '[import-docs]', ...args);
}
function warn(...args) {
  console.warn(getTimestamp(), '[import-docs]', ...args);
}
function err(...args) {
  console.error(getTimestamp(), '[import-docs]', ...args);
}

async function listDocDirs(baseDir) {
  const out = [];
  const entries = await fsp
    .readdir(baseDir, { withFileTypes: true })
    .catch(() => []);
  for (const e of entries) {
    if (e.isDirectory()) out.push(path.join(baseDir, e.name));
  }
  return out;
}

async function fileExists(p) {
  try {
    await fsp.stat(p);
    return true;
  } catch {
    return false;
  }
}

async function main() {
  const force = process.argv.includes('--force');
  const prisma = new PrismaClient();
  const DATA_DIR = path.join(process.cwd(), 'data', 'documents');
  const { PDFDocument } = await loadPdfLib();

  if (!(await fileExists(DATA_DIR))) {
    err('No ./data/documents directory found. Nothing to import.');
    process.exit(1);
  }

  const dirs = await listDocDirs(DATA_DIR);
  log(`Found ${dirs.length} document directories`);

  let imported = 0,
    skipped = 0,
    failed = 0;
  for (const dir of dirs) {
    const id = path.basename(dir);
    const orig = path.join(dir, 'original.pdf');
    if (!(await fileExists(orig))) {
      warn(`Skipping ${id}: missing original.pdf`);
      skipped++;
      continue;
    }
    try {
      const existing = await prisma.document.findUnique({ where: { id } });
      if (existing && !force) {
        log(`Skipping ${id}: already exists (use --force to re-import)`);
        skipped++;
        continue;
      }

      // Read metadata
      const stat = await fsp.stat(orig);
      const buf = await fsp.readFile(orig);
      let pageCount = null;
      try {
        const pdf = await PDFDocument.load(buf);
        pageCount = pdf.getPageCount();
      } catch (e) {
        warn(`Could not parse page count for ${id}: ${e?.message || e}`);
      }

      if (existing && force) {
        await prisma.page.deleteMany({ where: { documentId: id } });
        await prisma.chapter.deleteMany({ where: { documentId: id } });
        await prisma.document.delete({ where: { id } });
      }

      const doc = await prisma.document.create({
        data: {
          id,
          userId: null,
          sessionId: null,
          originalFilename: 'original.pdf',
          authors: null,
          contentType: 'application/pdf',
          sizeBytes: stat.size,
          s3KeyOriginal: orig,
          pageCount: pageCount ?? undefined,
          hasFullAudio: false,
          targetLanguage: null,
          voice: null,
          status: 'uploaded',
          tocSource: 'none',
        },
      });

      if (pageCount && pageCount > 0) {
        const data = Array.from({ length: pageCount }).map((_, i) => ({
          documentId: doc.id,
          pageNumber: i + 1,
          status: 'pending',
        }));
        await prisma.page.createMany({ data, skipDuplicates: true });
      }

      imported++;
      log(`Imported ${id} (${pageCount ?? '?'} pages)`);
    } catch (e) {
      failed++;
      err(`Failed to import ${path.basename(dir)}:`, e?.message || e);
    }
  }

  log(`Done. imported=${imported}, skipped=${skipped}, failed=${failed}`);
  await prisma.$disconnect();
}

main().catch((e) => {
  err(e?.stack || String(e));
  process.exit(1);
});
