import { documentQueue, JOB_TYPES } from '../lib/queue/config';

async function main() {
  const docIds = ['cmiaz97y10001bnetpqvlcapi', 'cmib0hvjz0009bnet1f3a4r50'];

  for (const docId of docIds) {
    console.log(`Enqueuing process-document for ${docId}...`);

    await documentQueue.add(
      JOB_TYPES.PROCESS_DOCUMENT,
      {
        docId,
        targetLanguage: 'vi',
        voice: 'female',
      },
      {
        jobId: `${JOB_TYPES.PROCESS_DOCUMENT}-${docId}`,
        removeOnComplete: true,
        removeOnFail: false,
      }
    );

    console.log(`✓ Enqueued ${docId}`);
  }

  console.log('Done! Jobs enqueued.');
  await documentQueue.close();
  process.exit(0);
}

main().catch((err) => {
  console.error('Error:', err);
  process.exit(1);
});
