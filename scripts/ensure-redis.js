#!/usr/bin/env node
/**
 * Ensure Redis is running before starting dev server
 * Auto-starts Redis container if Dock<PERSON> is available
 */

const { execSync } = require('child_process');
const IORedis = require('ioredis');

const REDIS_CONTAINER_NAME = 'transread-redis';
const REDIS_IMAGE = 'redis:7-alpine';
const REDIS_PORT = process.env.REDIS_PORT || '6379';

/**
 * Load Redis configuration from environment variables
 */
function loadRedisConfig() {
  return {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
  };
}

/**
 * Check if a native Redis instance is running and accessible
 */
async function checkNativeRedis(config, timeoutMs = 3000) {
  const { host, port, password } = config;
  let redis = null;

  try {
    // Create Redis client with aggressive timeout
    redis = new IORedis({
      host,
      port,
      password,
      retryStrategy: () => null, // Don't retry
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      connectTimeout: timeoutMs,
      commandTimeout: timeoutMs,
      lazyConnect: true, // Don't auto-connect
    });

    // Set up timeout promise
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('timeout')), timeoutMs)
    );

    // Connect and ping
    await redis.connect();
    await Promise.race([redis.ping(), timeoutPromise]);

    // Success - clean up and return
    await redis.quit();
    return { found: true, healthy: true, error: null };
  } catch (error) {
    // Clean up connection if it exists
    if (redis) {
      try {
        redis.disconnect();
      } catch (e) {
        // Ignore cleanup errors
      }
    }

    // Distinguish error types
    if (error.code === 'ECONNREFUSED' || error.message === 'timeout') {
      return { found: false, healthy: false, error: 'connection_failed' };
    }

    if (error.message?.includes('WRONGPASS')) {
      return { found: true, healthy: false, error: 'auth_failed' };
    }

    return { found: false, healthy: false, error: error.message };
  }
}

function exec(command, options = {}) {
  try {
    return execSync(command, { ...options, stdio: 'pipe' })
      .toString()
      .trim();
  } catch (error) {
    if (options.ignoreError) return null;
    throw error;
  }
}

function checkDocker() {
  try {
    exec('docker info', { ignoreError: true });
    return true;
  } catch {
    return false;
  }
}

function checkRedisHealth() {
  try {
    exec(`docker exec ${REDIS_CONTAINER_NAME} redis-cli ping`);
    return true;
  } catch {
    return false;
  }
}

function getContainerStatus() {
  try {
    const status = exec(
      `docker inspect -f '{{.State.Status}}' ${REDIS_CONTAINER_NAME}`,
      { ignoreError: true },
    );
    return status;
  } catch {
    return null;
  }
}

function waitForRedis(maxAttempts = 30) {
  console.log('Waiting for Redis to be ready...');
  for (let i = 0; i < maxAttempts; i++) {
    if (checkRedisHealth()) {
      console.log('✓ Redis is ready\n');
      return true;
    }
    execSync('sleep 1');
  }
  return false;
}

async function main() {
  console.log('Checking Redis setup...\n');

  const config = loadRedisConfig();

  // Check for native Redis first
  console.log(`Checking for native Redis on ${config.host}:${config.port}...`);
  const nativeCheck = await checkNativeRedis(config);

  if (nativeCheck.found && nativeCheck.healthy) {
    console.log('✓ Found native Redis');
    console.log('✓ Redis health check passed\n');
    return; // Exit successfully - skip Docker entirely
  }

  if (nativeCheck.error === 'auth_failed') {
    console.error('❌ Redis authentication failed');
    console.error('\nSolution:');
    console.error('  Set correct REDIS_PASSWORD in .env\n');
    process.exit(1);
  }

  // Native Redis not found - fall back to Docker
  console.log('Native Redis not accessible, falling back to Docker setup...\n');

  // Check Docker availability
  if (!checkDocker()) {
    console.error('❌ Docker is not running or not installed');
    console.error('\nRedis requires Docker for auto-setup.');
    console.error('\nOptions:');
    console.error('  1. Install Docker: https://docs.docker.com/get-docker/');
    console.error('  2. Install Redis locally: brew install redis (macOS)');
    console.error('  3. Manually start Redis before running npm run dev\n');
    process.exit(1);
  }

  const status = getContainerStatus();

  if (status === 'running') {
    console.log('✓ Redis container already running');
    if (checkRedisHealth()) {
      console.log('✓ Redis health check passed\n');
      return;
    }
    console.log('Waiting for Redis to become healthy...');
    if (!waitForRedis()) {
      console.error('❌ Redis health check failed\n');
      process.exit(1);
    }
    return;
  }

  if (status === 'exited' || status === 'created') {
    console.log('Starting existing Redis container...');
    exec(`docker start ${REDIS_CONTAINER_NAME}`);
    if (!waitForRedis()) {
      console.error('❌ Redis failed to start\n');
      process.exit(1);
    }
    return;
  }

  // Container doesn't exist - create it
  console.log('Creating Redis container...');
  try {
    exec(
      `docker run -d --name ${REDIS_CONTAINER_NAME} -p ${REDIS_PORT}:6379 --restart unless-stopped ${REDIS_IMAGE}`,
    );

    console.log('✓ Redis container created');

    if (!waitForRedis()) {
      console.error('❌ Redis failed to start\n');
      process.exit(1);
    }
  } catch (error) {
    if (error.message.includes('port is already allocated')) {
      console.error(`❌ Port ${REDIS_PORT} is already in use\n`);
      console.error('Solutions:');
      console.error(`  1. Check what's using port ${REDIS_PORT}:`);
      console.error(`     lsof -i :${REDIS_PORT}  # macOS/Linux`);
      console.error(`  2. Stop the service using that port:`);
      console.error(`     sudo systemctl stop redis  # Linux`);
      console.error(`     brew services stop redis   # macOS`);
      console.error(`  3. Or set REDIS_PORT to a different port in .env\n`);
      process.exit(1);
    }
    throw error;
  }
}

main().catch((error) => {
  console.error('❌ Failed:', error.message);
  process.exit(1);
});
