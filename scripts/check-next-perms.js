#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

const NEXT_DIR = path.join(process.cwd(), '.next');
const MANIFEST = path.join(NEXT_DIR, 'server', 'app-paths-manifest.json');

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `[${year}-${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

function info(msg) {
  console.log(getTimestamp(), `[predev] ${msg}`);
}

function warn(msg) {
  console.warn(getTimestamp(), `[predev] ${msg}`);
}

function main() {
  try {
    if (!fs.existsSync(NEXT_DIR)) return;

    // Fast path: ensure the .next directory is writable by current user
    try {
      fs.accessSync(NEXT_DIR, fs.constants.W_OK);
    } catch (e) {
      warn(
        `'.next' is not writable by current user. This often happens if it was created by root (e.g., via sudo or Docker).`,
      );
      const uid = process.getuid && process.getuid();
      if (uid !== undefined) {
        warn(`Suggested fix: sudo chown -R $(id -u):$(id -g) .next`);
      } else {
        warn(
          `Suggested fix: remove '.next' or adjust its permissions to be writable by your user.`,
        );
      }
      process.exit(1);
    }

    // Try to proactively remove a stale manifest if present
    if (fs.existsSync(MANIFEST)) {
      try {
        fs.unlinkSync(MANIFEST);
        info(
          `Removed stale manifest: ${path.relative(process.cwd(), MANIFEST)}`,
        );
      } catch (err) {
        if (err && err.code === 'EACCES') {
          warn(
            `Permission denied removing manifest. Likely root-owned files in '.next'.`,
          );
          const uid = process.getuid && process.getuid();
          if (uid !== undefined) {
            warn(`Run: sudo chown -R $(id -u):$(id -g) .next`);
          }
          process.exit(1);
        }
        // Non-permission errors: surface but do not block
        warn(`Could not remove manifest: ${err.message}`);
      }
    }
  } catch (e) {
    // Never block dev on unexpected errors; just log for awareness
    warn(`check failed: ${e.message}`);
  }
}

main();
