#!/usr/bin/env node
/**
 * Stop development services
 */

const { execSync } = require('child_process');

console.log('Stopping development services...\n');

try {
  execSync('docker stop transread-redis', { stdio: 'inherit' });
  console.log('✓ Redis stopped');
} catch {
  console.log('Redis not running');
}

try {
  execSync('docker stop transreed-pg', { stdio: 'inherit' });
  console.log('✓ PostgreSQL stopped');
} catch {
  console.log('PostgreSQL not running');
}

console.log('\n✓ Development services stopped');
