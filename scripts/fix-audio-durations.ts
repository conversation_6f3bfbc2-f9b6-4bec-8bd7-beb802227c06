/**
 * Fix incorrect audio durations in the database
 *
 * This script recalculates the real duration of all PageAudio records
 * using ffprobe instead of relying on estimated durations.
 *
 * Run with: npx tsx scripts/fix-audio-durations.ts
 */

import { prisma } from '../lib/prisma';
import { getAudioDuration } from '../lib/ai/audio-utils';
import { getFileByKey } from '../lib/file-service';
import { logger } from '../lib/logger';

async function fixAudioDurations() {
  console.log('Starting audio duration fix...\n');

  // Get all PageAudio records that have audio files
  const audioRecords = await prisma.pageAudio.findMany({
    where: {
      s3KeyAudioMp3: { not: null },
    },
    select: {
      id: true,
      s3KeyAudioMp3: true,
      duration: true,
      language: true,
      voice: true,
      page: {
        select: {
          pageNumber: true,
          documentId: true,
        },
      },
    },
  });

  console.log(`Found ${audioRecords.length} audio records to process\n`);

  let fixed = 0;
  let errors = 0;
  let skipped = 0;

  for (const record of audioRecords) {
    if (!record.s3KeyAudioMp3) {
      skipped++;
      continue;
    }

    try {
      // Read the audio file
      const audioBytes = await getFileByKey(record.s3KeyAudioMp3);

      // Get real duration
      const realDuration = await getAudioDuration(audioBytes);

      if (realDuration === null) {
        console.log(
          `❌ Failed to get duration for ${record.page.documentId}/page-${record.page.pageNumber} (${record.language}/${record.voice})`,
        );
        errors++;
        continue;
      }

      const oldDuration = record.duration || 0;
      const diff = realDuration - oldDuration;
      const diffPercent = oldDuration > 0 ? (diff / oldDuration) * 100 : 0;

      // Only update if there's a significant difference (> 5%)
      if (Math.abs(diffPercent) > 5) {
        await prisma.pageAudio.update({
          where: { id: record.id },
          data: { duration: realDuration },
        });

        console.log(
          `✓ Updated ${record.page.documentId}/page-${record.page.pageNumber} (${record.language}/${record.voice}): ${oldDuration}s → ${realDuration}s (diff: ${diff > 0 ? '+' : ''}${diff}s, ${diffPercent.toFixed(1)}%)`,
        );
        fixed++;
      } else {
        console.log(
          `- Skipped ${record.page.documentId}/page-${record.page.pageNumber} (${record.language}/${record.voice}): ${oldDuration}s ≈ ${realDuration}s (diff: ${diff}s, ${diffPercent.toFixed(1)}%)`,
        );
        skipped++;
      }
    } catch (error) {
      console.log(
        `❌ Error processing ${record.page.documentId}/page-${record.page.pageNumber}:`,
        (error as Error)?.message || error,
      );
      errors++;
    }
  }

  console.log('\n=== Summary ===');
  console.log(`Total records: ${audioRecords.length}`);
  console.log(`Fixed: ${fixed}`);
  console.log(`Skipped (no change): ${skipped}`);
  console.log(`Errors: ${errors}`);

  // Now recalculate user usage based on corrected durations
  console.log('\n\nRecalculating user usage...');

  const users = await prisma.user.findMany({
    select: { id: true, email: true },
  });

  for (const user of users) {
    // Get all documents owned by this user
    const documents = await prisma.document.findMany({
      where: { userId: user.id },
      select: { id: true, targetLanguage: true, voice: true },
    });

    let totalDurationSec = 0;

    for (const doc of documents) {
      const whereClause: any = {
        page: { documentId: doc.id },
        s3KeyAudioMp3: { not: null },
      };

      if (doc.targetLanguage) {
        whereClause.language = doc.targetLanguage;
      }
      if (doc.voice) {
        whereClause.voice = doc.voice;
      }

      // Get ONE audio per page (deduplicate)
      const pages = await prisma.page.findMany({
        where: { documentId: doc.id },
        select: {
          id: true,
          pageAudios: {
            where: whereClause,
            select: { duration: true },
            take: 1, // Only take the first matching audio per page
          },
        },
      });

      for (const page of pages) {
        if (page.pageAudios.length > 0) {
          totalDurationSec += page.pageAudios[0].duration || 0;
        }
      }
    }

    if (totalDurationSec > 0) {
      // Update usage for current cycle
      const now = new Date();
      const y = now.getUTCFullYear();
      const m = now.getUTCMonth();
      const cycleStart = new Date(Date.UTC(y, m, 1, 0, 0, 0));
      const cycleEnd = new Date(Date.UTC(y, m + 1, 1, 0, 0, 0));

      await prisma.usage.upsert({
        where: { userId_cycleStart: { userId: user.id, cycleStart } },
        update: { listeningTime: totalDurationSec },
        create: {
          userId: user.id,
          cycleStart,
          cycleEnd,
          listeningTime: totalDurationSec,
        },
      });

      console.log(
        `✓ Updated usage for ${user.email}: ${totalDurationSec}s (${Math.floor(totalDurationSec / 60)}m ${totalDurationSec % 60}s)`,
      );
    }
  }

  console.log('\n✅ Done!');
}

fixAudioDurations()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
