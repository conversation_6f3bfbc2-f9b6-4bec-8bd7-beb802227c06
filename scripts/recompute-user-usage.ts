/**
 * Recompute correct usage time for a user
 *
 * This script:
 * 1. Finds all documents for a user
 * 2. For each document, gets PageAudio matching the document's targetLanguage and voice
 * 3. Sums only the correct audio durations (one per page, matching document settings)
 * 4. Updates the user's usage record for the current billing cycle
 *
 * Usage:
 *   npx tsx scripts/recompute-user-usage.ts <email>
 *   npx tsx scripts/recompute-user-usage.ts <email> --dry-run
 */

import { prisma } from '../lib/prisma';

async function recomputeUsageForUser(email: string, dryRun: boolean = false) {
  console.log(`Looking up user: ${email}`);

  const user = await prisma.user.findFirst({
    where: { email },
    include: {
      plan: {
        select: {
          name: true,
          includedListeningTime: true,
        },
      },
    },
  });

  if (!user) {
    console.error(`❌ User not found: ${email}`);
    process.exit(1);
  }

  console.log(`\n✅ Found user: ${user.id}`);
  console.log(`   Name: ${user.name || '(none)'}`);
  console.log(`   Email: ${user.email || '(none)'}`);
  console.log(`   Plan: ${user.plan.name} (${user.plan.includedListeningTime ? formatTime(user.plan.includedListeningTime) : 'unlimited'})`);

  // Get current billing cycle
  const now = new Date();
  const y = now.getUTCFullYear();
  const m = now.getUTCMonth();
  const cycleStart = new Date(Date.UTC(y, m, 1, 0, 0, 0));
  const cycleEnd = new Date(Date.UTC(y, m + 1, 1, 0, 0, 0));

  console.log(`\n📅 Current billing cycle: ${cycleStart.toISOString().split('T')[0]} to ${cycleEnd.toISOString().split('T')[0]}`);

  // Get all documents for this user
  const documents = await prisma.document.findMany({
    where: { userId: user.id },
    select: {
      id: true,
      originalFilename: true,
      targetLanguage: true,
      voice: true,
      pageCount: true,
      createdAt: true,
      suggestedStartPageId: true,
    },
    orderBy: { createdAt: 'desc' },
  });

  console.log(`\n📚 Found ${documents.length} document(s)`);

  let totalCorrectDuration = 0;
  const documentDetails: Array<{
    filename: string;
    pages: number;
    duration: number;
    language: string;
    voice: string;
  }> = [];

  for (const doc of documents) {
    console.log(`\n📄 Processing: ${doc.originalFilename}`);
    console.log(`   Document ID: ${doc.id}`);
    console.log(`   Language: ${doc.targetLanguage || 'not set'}`);
    console.log(`   Voice: ${doc.voice || 'not set'}`);

    // Get the suggested start page number (or default to 1)
    let startPageNumber = 1;
    if (doc.suggestedStartPageId) {
      const startPage = await prisma.page.findUnique({
        where: { id: doc.suggestedStartPageId },
        select: { pageNumber: true },
      });
      if (startPage) {
        startPageNumber = startPage.pageNumber;
        console.log(`   Suggested start page: ${startPageNumber} (skipping front matter)`);
      }
    }

    // Get pages from suggestedStartPage onwards
    const pages = await prisma.page.findMany({
      where: {
        documentId: doc.id,
        pageNumber: { gte: startPageNumber },
      },
      select: {
        id: true,
        pageNumber: true,
      },
      orderBy: { pageNumber: 'asc' },
    });

    console.log(`   Pages to count: ${pages.length} (from page ${startPageNumber})`);

    let docDuration = 0;
    let audioPages = 0;

    for (const page of pages) {
      // Find PageAudio matching the document's language and voice
      // If no language/voice set on document, use the first audio found
      const whereClause: any = {
        pageId: page.id,
        s3KeyAudioMp3: { not: null },
      };

      if (doc.targetLanguage) {
        whereClause.language = doc.targetLanguage;
      }
      if (doc.voice) {
        whereClause.voice = doc.voice;
      }

      const audio = await prisma.pageAudio.findFirst({
        where: whereClause,
        select: {
          duration: true,
          language: true,
          voice: true,
        },
        orderBy: { createdAt: 'desc' }, // Get most recent if multiple
      });

      if (audio && audio.duration) {
        docDuration += audio.duration;
        audioPages++;
        console.log(`   ✓ Page ${page.pageNumber}: ${formatTime(audio.duration)} (${audio.language}/${audio.voice})`);
      } else {
        console.log(`   ○ Page ${page.pageNumber}: no audio found`);
      }
    }

    if (docDuration > 0) {
      totalCorrectDuration += docDuration;
      documentDetails.push({
        filename: doc.originalFilename,
        pages: audioPages,
        duration: docDuration,
        language: doc.targetLanguage || 'unknown',
        voice: doc.voice || 'unknown',
      });
      console.log(`   📊 Document total: ${formatTime(docDuration)} (${audioPages} pages with audio)`);
    } else {
      console.log(`   ⚠️  No audio found for this document`);
    }
  }

  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 SUMMARY`);
  console.log(`${'='.repeat(60)}`);

  if (documentDetails.length > 0) {
    console.log(`\nDocuments with audio:`);
    documentDetails.forEach((d, i) => {
      console.log(`${i + 1}. ${d.filename}`);
      console.log(`   ${d.pages} pages, ${formatTime(d.duration)} (${d.language}/${d.voice})`);
    });
  }

  console.log(`\n✅ Calculated correct total: ${formatTime(totalCorrectDuration)} (${totalCorrectDuration}s)`);

  // Get current usage record
  const currentUsage = await prisma.usage.findUnique({
    where: {
      userId_cycleStart: {
        userId: user.id,
        cycleStart,
      },
    },
  });

  if (currentUsage) {
    console.log(`\n📈 Current usage record:`);
    console.log(`   Listening time: ${formatTime(currentUsage.listeningTime)} (${currentUsage.listeningTime}s)`);
    console.log(`   Documents processed: ${currentUsage.documentsProcessed}`);
    console.log(`   Pages processed: ${currentUsage.pagesProcessed}`);

    const difference = currentUsage.listeningTime - totalCorrectDuration;

    if (difference === 0) {
      console.log(`\n✅ Usage is already correct! No update needed.`);
      return;
    }

    console.log(`\n⚠️  DISCREPANCY DETECTED!`);
    console.log(`   Current: ${formatTime(currentUsage.listeningTime)} (${currentUsage.listeningTime}s)`);
    console.log(`   Correct: ${formatTime(totalCorrectDuration)} (${totalCorrectDuration}s)`);
    console.log(`   Difference: ${formatTime(Math.abs(difference))} ${difference > 0 ? 'OVER' : 'UNDER'}`);

    if (dryRun) {
      console.log(`\n🔍 DRY RUN - No changes made`);
      console.log(`   Run without --dry-run to apply the fix`);
      return;
    }

    // Update usage
    await prisma.usage.update({
      where: {
        userId_cycleStart: {
          userId: user.id,
          cycleStart,
        },
      },
      data: {
        listeningTime: totalCorrectDuration,
      },
    });

    console.log(`\n✅ Usage updated successfully!`);
    console.log(`   New listening time: ${formatTime(totalCorrectDuration)} (${totalCorrectDuration}s)`);
  } else {
    console.log(`\n⚠️  No usage record found for current cycle`);

    if (totalCorrectDuration > 0) {
      console.log(`   User has ${formatTime(totalCorrectDuration)} of audio generated but no usage tracked`);

      if (dryRun) {
        console.log(`\n🔍 DRY RUN - No changes made`);
        console.log(`   Run without --dry-run to create usage record`);
        return;
      }

      // Create usage record
      await prisma.usage.create({
        data: {
          userId: user.id,
          cycleStart,
          cycleEnd,
          listeningTime: totalCorrectDuration,
          documentsProcessed: documents.filter(d =>
            documentDetails.some(dd => dd.filename === d.originalFilename)
          ).length,
          pagesProcessed: documentDetails.reduce((sum, d) => sum + d.pages, 0),
        },
      });

      console.log(`\n✅ Usage record created!`);
      console.log(`   Listening time: ${formatTime(totalCorrectDuration)} (${totalCorrectDuration}s)`);
    } else {
      console.log(`   No audio generated, so no usage to track`);
    }
  }

  // Show remaining time
  if (user.plan.includedListeningTime) {
    const remaining = user.plan.includedListeningTime - totalCorrectDuration;
    console.log(`\n⏱️  Remaining time: ${formatTime(Math.max(0, remaining))} of ${formatTime(user.plan.includedListeningTime)}`);

    if (remaining < 0) {
      console.log(`   ⚠️  User has exceeded their plan limit by ${formatTime(Math.abs(remaining))}`);
    }
  }
}

function formatTime(seconds: number): string {
  if (!Number.isFinite(seconds) || seconds < 0) return '0:00';
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// Parse command line arguments
const args = process.argv.slice(2);
const email = args.find(arg => !arg.startsWith('--'));
const dryRun = args.includes('--dry-run');

if (!email) {
  console.error('Usage: npx tsx scripts/recompute-user-usage.ts <email> [--dry-run]');
  console.error('');
  console.error('Examples:');
  console.error('  npx tsx scripts/recompute-user-usage.ts <EMAIL>');
  console.error('  npx tsx scripts/recompute-user-usage.ts <EMAIL> --dry-run');
  console.error('');
  console.error('Options:');
  console.error('  --dry-run    Show what would be changed without making any updates');
  process.exit(1);
}

recomputeUsageForUser(email, dryRun)
  .then(() => {
    console.log('\n✨ Done!\n');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Error:', error);
    process.exit(1);
  });
