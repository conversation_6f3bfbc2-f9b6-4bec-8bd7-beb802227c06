/**
 * Fix usage time for a specific user
 * Recalculates usage based on actual PageAudio durations
 */

import { prisma } from '../lib/prisma';

async function fixUsageForUser(email: string) {
  console.log(`Looking up user: ${email}`);

  const user = await prisma.user.findFirst({
    where: { email },
  });

  if (!user) {
    console.error(`User not found: ${email}`);
    process.exit(1);
  }

  console.log(`Found user: ${user.id} (${user.name || user.email})`);

  // Get all documents for this user
  const documents = await prisma.document.findMany({
    where: { userId: user.id },
    select: {
      id: true,
      originalFilename: true,
      targetLanguage: true,
      voice: true,
      suggestedStartPageId: true,
    },
  });

  console.log(`\nFound ${documents.length} document(s)`);

  let totalDurationSec = 0;
  const audioRecords: Array<{
    documentFilename: string;
    pageNumber: number;
    duration: number;
  }> = [];

  for (const doc of documents) {
    // Get the suggested start page number (or default to 1)
    let startPageNumber = 1;
    if (doc.suggestedStartPageId) {
      const startPage = await prisma.page.findUnique({
        where: { id: doc.suggestedStartPageId },
        select: { pageNumber: true },
      });
      if (startPage) {
        startPageNumber = startPage.pageNumber;
      }
    }

    // Get all pages >= suggestedStartPage
    const pages = await prisma.page.findMany({
      where: {
        documentId: doc.id,
        pageNumber: { gte: startPageNumber },
      },
      select: {
        id: true,
        pageNumber: true,
      },
      orderBy: { pageNumber: 'asc' },
    });

    // For each page, get ONE audio matching document's language and voice
    for (const page of pages) {
      const whereClause: any = {
        pageId: page.id,
        s3KeyAudioMp3: { not: null },
      };

      // Filter by document's target language and voice if set
      if (doc.targetLanguage) {
        whereClause.language = doc.targetLanguage;
      }
      if (doc.voice) {
        whereClause.voice = doc.voice;
      }

      const audio = await prisma.pageAudio.findFirst({
        where: whereClause,
        select: { duration: true },
        orderBy: { createdAt: 'desc' }, // Get most recent if multiple
      });

      if (audio && audio.duration) {
        totalDurationSec += audio.duration;
        audioRecords.push({
          documentFilename: doc.originalFilename,
          pageNumber: page.pageNumber,
          duration: audio.duration,
        });
      }
    }
  }

  console.log(`\nAudio records found: ${audioRecords.length}`);
  audioRecords.forEach((record) => {
    console.log(
      `  - ${record.documentFilename} (page ${record.pageNumber}): ${record.duration}s`,
    );
  });
  console.log(
    `\nTotal actual duration: ${totalDurationSec}s (${formatTime(totalDurationSec)})`,
  );
  console.log(
    `Note: Only counting from suggestedStartPage onwards (skipping front matter)`,
  );

  // Get current usage
  const now = new Date();
  const y = now.getUTCFullYear();
  const m = now.getUTCMonth();
  const cycleStart = new Date(Date.UTC(y, m, 1, 0, 0, 0));

  const currentUsage = await prisma.usage.findUnique({
    where: {
      userId_cycleStart: {
        userId: user.id,
        cycleStart,
      },
    },
  });

  if (!currentUsage) {
    console.log('\nNo usage record found for current cycle. Nothing to fix.');
    return;
  }

  console.log(`\nCurrent usage record:`);
  console.log(
    `  - Listening time: ${currentUsage.listeningTime}s (${formatTime(currentUsage.listeningTime)})`,
  );
  console.log(`  - Documents processed: ${currentUsage.documentsProcessed}`);
  console.log(`  - Pages processed: ${currentUsage.pagesProcessed}`);

  if (currentUsage.listeningTime === totalDurationSec) {
    console.log('\n✅ Usage is already correct. No update needed.');
    return;
  }

  console.log(`\n⚠️  Usage mismatch detected!`);
  console.log(
    `  Expected: ${totalDurationSec}s (${formatTime(totalDurationSec)})`,
  );
  console.log(
    `  Current:  ${currentUsage.listeningTime}s (${formatTime(currentUsage.listeningTime)})`,
  );
  console.log(
    `  Difference: ${currentUsage.listeningTime - totalDurationSec}s`,
  );

  // Update usage to correct value
  await prisma.usage.update({
    where: {
      userId_cycleStart: {
        userId: user.id,
        cycleStart,
      },
    },
    data: {
      listeningTime: totalDurationSec,
    },
  });

  console.log(
    `\n✅ Usage updated successfully to ${totalDurationSec}s (${formatTime(totalDurationSec)})`,
  );
}

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

const email = process.argv[2];

if (!email) {
  console.error('Usage: npx tsx scripts/fix-usage.ts <email>');
  console.error('Example: npx tsx scripts/fix-usage.ts <EMAIL>');
  process.exit(1);
}

fixUsageForUser(email)
  .then(() => {
    console.log('\nDone!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\nError:', error);
    process.exit(1);
  });
