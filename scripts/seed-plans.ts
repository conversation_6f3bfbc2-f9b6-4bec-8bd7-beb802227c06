import { logger } from '@/lib/logger';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  logger.log('Seeding plans...');

  const plans = [
    {
      key: 'free',
      name: 'Free',
      priceMonthlyCents: 0,
      includedListeningTime: 300, // 5 minutes
      description: 'Free plan with 5 minutes of listening time',
      isActive: true,
    },
    {
      key: 'starter',
      name: 'Starter',
      priceMonthlyCents: 500, // $5.00
      includedListeningTime: 3600, // 1 hour
      description: 'Starter plan with 1 hour of listening time per month',
      isActive: true,
    },
    {
      key: 'pro',
      name: 'Pro',
      priceMonthlyCents: 1900, // $19.00
      includedListeningTime: 18000, // 5 hours
      description: 'Pro plan with 5 hours of listening time per month',
      isActive: true,
    },
    {
      key: 'premium',
      name: 'Premium',
      priceMonthlyCents: 4900, // $49.00
      includedListeningTime: 54000, // 15 hours
      description: 'Premium plan with 15 hours of listening time per month',
      isActive: true,
    },
  ];

  // Upsert defined plans
  for (const plan of plans) {
    const result = await prisma.plan.upsert({
      where: { key: plan.key },
      update: plan,
      create: plan,
    });
    logger.log(
      `✓ ${result.key}: ${result.name} - $${result.priceMonthlyCents / 100}/month`,
    );
  }

  // Remove plans that are no longer defined
  const definedPlanKeys = plans.map((p) => p.key);
  const plansToDelete = await prisma.plan.findMany({
    where: {
      key: {
        notIn: definedPlanKeys,
      },
    },
  });

  if (plansToDelete.length > 0) {
    logger.log('\nRemoving obsolete plans:');
    for (const plan of plansToDelete) {
      await prisma.plan.delete({
        where: { key: plan.key },
      });
      logger.log(`✗ Deleted: ${plan.key} (${plan.name})`);
    }
  }

  logger.log('\n✓ Plans seeded successfully!');

  // Display all plans
  const allPlans = await prisma.plan.findMany({
    orderBy: { priceMonthlyCents: 'asc' },
  });
  logger.log('\nAll plans:');
  console.table(
    allPlans.map((p) => ({
      key: p.key,
      name: p.name,
      price: `$${p.priceMonthlyCents / 100}`,
      listeningTime: p.includedListeningTime
        ? `${Math.floor(p.includedListeningTime / 3600)}h`
        : 'Unlimited',
    })),
  );

  await prisma.$disconnect();
}

main().catch((e) => {
  logger.error(e);
  process.exit(1);
});
