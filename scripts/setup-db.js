#!/usr/bin/env node
/*
  Setup script for TransReed database (PostgreSQL + Prisma)

  What it does:
  - Ensures a DATABASE_URL exists in .env (Prisma reads .env)
  - If Docker is available, ensures a local Postgres container is running
    with default creds (user/pass/db: transreed) bound to localhost:5432
  - Waits for the DB to be reachable
  - Runs `prisma generate` and `prisma migrate deploy` (falls back to `db push`)

  Usage:
  - npm run setup:db
*/

const fs = require('fs');
const path = require('path');
const { spawnSync } = require('child_process');
const net = require('net');

const ROOT = process.cwd();
const ENV_FILE = path.join(ROOT, '.env');

const DEFAULT_DB = {
  user: 'transreed',
  password: 'transreed',
  host: 'localhost',
  port: 5432,
  db: 'transreed',
};

function dbUrlString({ user, password, host, port, db }) {
  return `postgresql://${user}:${password}@${host}:${port}/${db}`;
}

const DEFAULT_URL = dbUrlString(DEFAULT_DB);

function log(msg) {
  process.stdout.write(`[setup-db] ${msg}\n`);
}

function readEnvFile(file) {
  try {
    const txt = fs.readFileSync(file, 'utf8');
    const lines = txt.split(/\r?\n/);
    const map = {};
    for (const l of lines) {
      if (!l || l.trim().startsWith('#')) continue;
      const eq = l.indexOf('=');
      if (eq === -1) continue;
      const k = l.slice(0, eq).trim();
      const v = l.slice(eq + 1).trim();
      map[k] = v;
    }
    return map;
  } catch (e) {
    return {};
  }
}

function writeEnvVar(file, key, value) {
  let content = '';
  try {
    content = fs.readFileSync(file, 'utf8');
  } catch {}
  if (content.includes(`${key}=`)) {
    const updated = content.replace(
      new RegExp(`^${key}=.*$`, 'm'),
      `${key}=${value}`,
    );
    fs.writeFileSync(file, updated, 'utf8');
  } else {
    const suffix = content.endsWith('\n') || content.length === 0 ? '' : '\n';
    fs.writeFileSync(file, content + suffix + `${key}=${value}\n`, 'utf8');
  }
}

function cmd(bin, args, opts = {}) {
  const res = spawnSync(bin, args, {
    stdio: 'pipe',
    encoding: 'utf8',
    ...opts,
  });
  return res;
}

function hasDocker() {
  const res = cmd('docker', ['version', '--format', '{{.Server.Version}}']);
  return res.status === 0;
}

function listDockerNames() {
  const res = cmd('docker', ['ps', '-a', '--format', '{{.Names}}']);
  if (res.status !== 0) return [];
  return res.stdout.split(/\r?\n/).filter(Boolean);
}

function isPortInUse(host, port) {
  return new Promise((resolve) => {
    const sock = new net.Socket();
    sock.setTimeout(1000);
    const done = (used) => {
      try {
        sock.destroy();
      } catch {}
      resolve(used);
    };
    sock.once('connect', () => done(true));
    sock.once('timeout', () => done(false));
    sock.once('error', () => done(false));
    sock.connect(port, host);
  });
}

async function findFreePort(preferred, maxTries = 20) {
  for (let i = 0; i < maxTries; i++) {
    const port = preferred + i;
    // eslint-disable-next-line no-await-in-loop
    const used = await isPortInUse('127.0.0.1', port);
    if (!used) return port;
  }
  return preferred; // fallback
}

function removeContainer(name) {
  cmd('docker', ['rm', '-f', name]);
}

function startContainer(name) {
  return cmd('docker', ['start', name]);
}

function runContainer(name, hostPort) {
  // Use a named volume so data persists across restarts/recreates
  const volumeName = 'transreed-pg-data';
  return cmd('docker', [
    'run',
    '--name',
    name,
    '--restart',
    'unless-stopped',
    '-e',
    `POSTGRES_USER=${DEFAULT_DB.user}`,
    '-e',
    `POSTGRES_PASSWORD=${DEFAULT_DB.password}`,
    '-e',
    `POSTGRES_DB=${DEFAULT_DB.db}`,
    '-v',
    `${volumeName}:/var/lib/postgresql/data`,
    '-p',
    `${hostPort}:5432`,
    '-d',
    'postgres:15',
  ]);
}

async function ensureDockerPg(desiredPort) {
  const name = 'transreed-pg';
  const names = listDockerNames();
  let hostPort = desiredPort;
  if (names.includes(name)) {
    log('Starting existing Docker container transreed-pg...');
    const start = startContainer(name);
    if (start.status !== 0) {
      const stderr = start.stderr || '';
      if (/port is already allocated|Bind for .* failed/i.test(stderr)) {
        log(
          'Existing container cannot bind to port. Recreating on a free port...',
        );
        removeContainer(name);
        hostPort = await findFreePort(desiredPort);
        const run = runContainer(name, hostPort);
        if (run.status !== 0) {
          throw new Error(
            `Failed to run Docker Postgres on ${hostPort}.\n${run.stderr || run.stdout}`,
          );
        }
      } else {
        throw new Error(`Failed to start Docker container: ${stderr}`);
      }
    }
  } else {
    log('Creating Docker Postgres (postgres:15) as transreed-pg...');
    const run = runContainer(name, hostPort);
    if (run.status !== 0) {
      const stderr = run.stderr || '';
      if (/port is already allocated|Bind for .* failed/i.test(stderr)) {
        hostPort = await findFreePort(desiredPort);
        log(`Port ${desiredPort} busy; retrying on ${hostPort}...`);
        const run2 = runContainer(name, hostPort);
        if (run2.status !== 0) {
          throw new Error(
            `Failed to run Docker Postgres on ${hostPort}.\n${run2.stderr || run2.stdout}`,
          );
        }
      } else {
        throw new Error(
          `Failed to run Docker Postgres.\n${stderr || run.stdout}`,
        );
      }
    }
  }
  return hostPort;
}

function waitForPort({ host, port, timeoutMs = 30000 }) {
  return new Promise((resolve, reject) => {
    const start = Date.now();
    function tryOnce() {
      const sock = new net.Socket();
      sock.setTimeout(1500);
      sock.once('connect', () => {
        sock.destroy();
        resolve(true);
      });
      sock.once('timeout', () => {
        sock.destroy();
        retry();
      });
      sock.once('error', () => {
        sock.destroy();
        retry();
      });
      sock.connect(port, host);
    }
    function retry() {
      if (Date.now() - start > timeoutMs)
        return reject(new Error('Timeout waiting for DB port'));
      setTimeout(tryOnce, 500);
    }
    tryOnce();
  });
}

async function main() {
  log('Ensuring DATABASE_URL in .env');
  const envMap = readEnvFile(ENV_FILE);
  let dbUrl = envMap.DATABASE_URL;
  if (!dbUrl) {
    log(
      `No DATABASE_URL found, writing default pointing to localhost: ${DEFAULT_URL}`,
    );
    writeEnvVar(ENV_FILE, 'DATABASE_URL', DEFAULT_URL);
    dbUrl = DEFAULT_URL;
  } else {
    log(`Using existing DATABASE_URL from .env`);
  }

  let intendedPort = DEFAULT_DB.port;
  if (hasDocker()) {
    try {
      log('Docker detected; ensuring local Postgres container is running...');
      intendedPort = await ensureDockerPg(DEFAULT_DB.port);
      const currentUrl = dbUrlString({ ...DEFAULT_DB, port: intendedPort });
      const envUrl = (dbUrl || '').trim();
      const transreedPattern =
        /^postgresql:\/\/transreed:transreed@localhost:\d+\/transreed$/;
      if (!envUrl || transreedPattern.test(envUrl)) {
        if (envUrl !== currentUrl) {
          log(`Updating DATABASE_URL in .env to ${currentUrl}`);
          writeEnvVar(ENV_FILE, 'DATABASE_URL', currentUrl);
          dbUrl = currentUrl;
        }
      } else {
        log(
          'Custom DATABASE_URL detected; not modifying. Make sure it matches your running DB.',
        );
      }
    } catch (e) {
      log(String(e.message || e));
      log(
        'Proceeding without Docker-managed Postgres. Ensure your DB is running.',
      );
    }
  } else {
    log(
      'Docker not found. Skipping container setup. Ensure your Postgres is running.',
    );
  }

  // Wait for whichever port we intend to connect to (based on DATABASE_URL)
  try {
    let urlToUse = dbUrl || DEFAULT_URL;
    let port = intendedPort;
    try {
      const u = new URL(urlToUse);
      port = Number(u.port) || intendedPort;
    } catch {}
    log(`Waiting for database port ${port} to become available...`);
    await waitForPort({ host: DEFAULT_DB.host, port, timeoutMs: 45000 });
  } catch (e) {
    log('Port check timed out. Prisma may still try to connect; continuing.');
  }

  function containsP1000(out) {
    const s = String(out || '');
    return /P1000: Authentication failed/i.test(s);
  }

  function runPrismaOnce() {
    log('Generating Prisma client...');
    let res = cmd('npx', ['prisma', 'generate'], { env: process.env });
    if (res.status !== 0) {
      process.stderr.write(
        res.stderr || res.stdout || 'Failed to run prisma generate\n',
      );
      return { ok: false, res };
    }
    // Safer for existing data: apply existing migrations; do not create new ones or reset
    log('Applying Prisma migrations (deploy)...');
    res = cmd('npx', ['prisma', 'migrate', 'deploy'], { env: process.env });
    if (res.status !== 0) {
      log(
        '`prisma migrate deploy` failed, attempting `prisma db push` as fallback...',
      );
      const res2 = cmd('npx', ['prisma', 'db', 'push'], { env: process.env });
      if (res2.status !== 0) {
        return { ok: false, res, res2 };
      }
    }
    return { ok: true };
  }

  let attempt = runPrismaOnce();
  if (
    !attempt.ok &&
    (containsP1000(attempt.res && (attempt.res.stderr || attempt.res.stdout)) ||
      containsP1000(
        attempt.res2 && (attempt.res2.stderr || attempt.res2.stdout),
      ))
  ) {
    if (hasDocker()) {
      log(
        'Detected Prisma P1000 (credential mismatch). Recreating Docker Postgres with default credentials...',
      );
      try {
        removeContainer('transreed-pg');
      } catch {}
      intendedPort = await ensureDockerPg(DEFAULT_DB.port);
      const currentUrl = dbUrlString({ ...DEFAULT_DB, port: intendedPort });
      log(`Updating DATABASE_URL in .env to ${currentUrl}`);
      writeEnvVar(ENV_FILE, 'DATABASE_URL', currentUrl);
      dbUrl = currentUrl;
      log(`Waiting for database port ${intendedPort} to become available...`);
      await waitForPort({
        host: DEFAULT_DB.host,
        port: intendedPort,
        timeoutMs: 45000,
      });
      attempt = runPrismaOnce();
      if (!attempt.ok) {
        process.stderr.write(
          'Recreated DB but Prisma operations still failed. Please check Docker logs.\n',
        );
        process.exit(1);
      }
    } else {
      process.stderr.write(
        'Prisma P1000 and Docker not available to auto-fix. Please ensure credentials match your running DB.\n',
      );
      process.exit(1);
    }
  } else if (!attempt.ok) {
    process.stderr.write('Prisma operations failed. Check above logs.\n');
    process.exit(1);
  }

  log('Database is ready. You can now run `npm run dev`.');
}

main().catch((e) => {
  process.stderr.write(
    `[setup-db] Error: ${e && e.stack ? e.stack : String(e)}\n`,
  );
  process.exit(1);
});
