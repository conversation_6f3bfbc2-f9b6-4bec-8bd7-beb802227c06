#!/usr/bin/env node
/*
  Index existing local page artifacts (page PDFs, translated texts, audio files)
  under ./data/documents/<docId>/pages into the Postgres database.

  - Upserts Page.s3KeySinglePagePdf when page-<n>.pdf exists
  - Upserts PageAudio rows per page/language/voice
  - Sets s3KeyTranslatedText when sentences.json (preferred) or text.txt exists
  - Sets s3KeyAudioMp3 when audio.(mp3|wav) exists
  - Updates Page.status to 'ready' if audio exists; 'processing' if translation exists; else 'pending'
  - Optionally infers Document.targetLanguage and voice from first page artifacts
  - Sets Document.status to 'ready' if any page has audio; 'processing' if any translation; else 'uploaded'

  Usage:
    node scripts/index-local-artifacts.js [<docId>]
*/

const fs = require('fs');
const fsp = fs.promises;
const path = require('path');
const { PrismaClient } = require('@prisma/client');

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `[${year}-${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

function log(...args) {
  console.log(getTimestamp(), '[index-artifacts]', ...args);
}
function warn(...args) {
  console.warn(getTimestamp(), '[index-artifacts]', ...args);
}
function err(...args) {
  console.error(getTimestamp(), '[index-artifacts]', ...args);
}

async function fileExists(p) {
  try {
    await fsp.stat(p);
    return true;
  } catch {
    return false;
  }
}
async function listDirs(p) {
  return (await fsp.readdir(p, { withFileTypes: true }).catch(() => []))
    .filter((e) => e.isDirectory())
    .map((e) => path.join(p, e.name));
}

function parsePageNumberFromDir(name) {
  const m = /page-(\d+)/.exec(name);
  return m ? parseInt(m[1], 10) : undefined;
}

async function processDoc(prisma, docId) {
  const base = path.join(process.cwd(), 'data', 'documents', docId);
  const pagesDir = path.join(base, 'pages');
  if (!(await fileExists(pagesDir))) {
    warn(`Doc ${docId}: no pages directory; skipping`);
    return { docId, pages: 0, audios: 0, translations: 0 };
  }

  const doc = await prisma.document.findUnique({ where: { id: docId } });
  if (!doc) {
    warn(`Doc ${docId}: not found in DB; run import-docs first`);
    return { docId, pages: 0, audios: 0, translations: 0 };
  }

  const pageDirs = await listDirs(pagesDir);
  let pageWithAudio = 0,
    pageWithText = 0,
    updatedPages = 0,
    createdAudios = 0,
    updatedAudios = 0;
  let inferredLang = doc.targetLanguage,
    inferredVoice = doc.voice;

  for (const pdir of pageDirs) {
    const pageNumber = parsePageNumberFromDir(path.basename(pdir));
    if (!pageNumber) continue;
    const page = await prisma.page.findFirst({
      where: { documentId: docId, pageNumber },
    });
    if (!page) continue;

    // page-<n>.pdf
    const onePdf = path.join(path.dirname(pdir), `page-${pageNumber}.pdf`);
    const hasPdf = await fileExists(onePdf);
    // translations/<lang>/(sentences.json|text.txt)
    const translationsDir = path.join(pdir, 'translations');
    const langDirs = await listDirs(translationsDir);

    // audio/<lang>/<voice>/audio.(mp3|wav)
    const audioDir = path.join(pdir, 'audio');
    const audioLangDirs = await listDirs(audioDir);

    // Update page pdf path if exists
    if (hasPdf && page.s3KeySinglePagePdf !== onePdf) {
      await prisma.page.update({
        where: { id: page.id },
        data: { s3KeySinglePagePdf: onePdf },
      });
      updatedPages++;
    }

    let thisPageHasText = false;
    let thisPageHasAudio = false;

    // Handle translations
    for (const ldir of langDirs) {
      const language = path.basename(ldir);
      const sentencesJson = path.join(ldir, 'sentences.json');
      const textTxt = path.join(ldir, 'text.txt');
      const textPath = (await fileExists(sentencesJson))
        ? sentencesJson
        : (await fileExists(textTxt))
          ? textTxt
          : null;
      if (!textPath) continue;
      thisPageHasText = true;

      // Try to find an existing PageAudio for this language
      let audioRow = await prisma.pageAudio.findFirst({
        where: { pageId: page.id, language },
      });
      if (!audioRow) {
        audioRow = await prisma.pageAudio.create({
          data: {
            pageId: page.id,
            language,
            voice: inferredVoice || 'female',
            ttsEngine: 'import',
            ttsParamsHash: 'imported',
            modelVersion: 'imported',
            s3KeyTranslatedText: textPath,
          },
        });
        createdAudios++;
      } else if (audioRow.s3KeyTranslatedText !== textPath) {
        await prisma.pageAudio.update({
          where: { id: audioRow.id },
          data: { s3KeyTranslatedText: textPath },
        });
        updatedAudios++;
      }
    }

    // Handle audio
    for (const ldir of audioLangDirs) {
      const language = path.basename(ldir);
      const voiceDirs = await listDirs(ldir);
      for (const vdir of voiceDirs) {
        const voice = path.basename(vdir);
        const mp3 = path.join(vdir, 'audio.mp3');
        const audioPath = (await fileExists(mp3)) ? mp3 : null;
        if (!audioPath) continue;
        thisPageHasAudio = true;
        if (!inferredLang) inferredLang = language;
        if (!inferredVoice) inferredVoice = voice;
        let audioRow = await prisma.pageAudio.findFirst({
          where: { pageId: page.id, language, voice },
        });
        if (!audioRow) {
          audioRow = await prisma.pageAudio.create({
            data: {
              pageId: page.id,
              language,
              voice,
              ttsEngine: 'import',
              ttsParamsHash: 'imported',
              modelVersion: 'imported',
              s3KeyAudioMp3: audioPath,
            },
          });
          createdAudios++;
        } else if (audioRow.s3KeyAudioMp3 !== audioPath) {
          await prisma.pageAudio.update({
            where: { id: audioRow.id },
            data: { s3KeyAudioMp3: audioPath },
          });
          updatedAudios++;
        }
      }
    }

    // Update page status
    const newStatus = thisPageHasAudio
      ? 'ready'
      : thisPageHasText
        ? 'processing'
        : 'pending';
    if (page.status !== newStatus) {
      await prisma.page.update({
        where: { id: page.id },
        data: { status: newStatus },
      });
    }
    if (thisPageHasText) pageWithText++;
    if (thisPageHasAudio) pageWithAudio++;
  }

  // Update doc inferred fields and status
  const newDocStatus =
    pageWithAudio > 0 ? 'ready' : pageWithText > 0 ? 'processing' : 'uploaded';
  const patch = {};
  if (doc.status !== newDocStatus) patch.status = newDocStatus;
  if (!doc.targetLanguage && inferredLang) patch.targetLanguage = inferredLang;
  if (!doc.voice && inferredVoice) patch.voice = inferredVoice;
  if (Object.keys(patch).length) {
    await prisma.document.update({ where: { id: docId }, data: patch });
  }

  return {
    docId,
    pages: pageDirs.length,
    audios: createdAudios + updatedAudios,
    translations: pageWithText,
  };
}

async function main() {
  const prisma = new PrismaClient();
  const onlyId = process.argv[2];
  const DATA_BASE = path.join(process.cwd(), 'data', 'documents');
  const docDirs = onlyId
    ? [path.join(DATA_BASE, onlyId)]
    : await listDirs(DATA_BASE);
  const results = [];
  for (const dir of docDirs) {
    const id = path.basename(dir);
    const r = await processDoc(prisma, id);
    results.push(r);
    log(
      `Indexed ${id}: pages=${r.pages}, translations=${r.translations}, audiosChanged=${r.audios}`,
    );
  }
  await prisma.$disconnect();
}

main().catch((e) => {
  err(e?.stack || String(e));
  process.exit(1);
});
