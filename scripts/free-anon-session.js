#!/usr/bin/env node
/*
  Free up an anonymous session by deleting its attached document(s).

  Why: Anonymous users are limited to a single document. If one is already
  attached to a given anonymous session (stable fingerprint), uploads will
  reuse that doc. This script removes any document(s) bound to the provided
  anonymous session ID(s) so a new upload can be started.

  Behavior:
  - Finds all documents with userId=null and sessionId in the provided list
  - Logs matched docs
  - Deletes them from the database (CASCADE will remove pages, audios, etc.)
  - Best-effort removal of local files under ./data/documents/<docId>
  - S3 cleanup is not implemented (storage is optional for local dev)

  Usage:
    node scripts/free-anon-session.js --sid <SESSION_ID>
    node scripts/free-anon-session.js --sid <ID1> --sid <ID2>
    node scripts/free-anon-session.js --sid <ID1,ID2,ID3>

  Also supports env var:
    ANON_SID="<ID1,ID2>" node scripts/free-anon-session.js
*/

const fs = require('fs');
const fsp = fs.promises;
const path = require('path');
const { PrismaClient } = require('@prisma/client');

function getTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  return `[${year}-${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

function log(...args) {
  console.log(getTimestamp(), '[free-anon]', ...args);
}
function warn(...args) {
  console.warn(getTimestamp(), '[free-anon]', ...args);
}
function err(...args) {
  console.error(getTimestamp(), '[free-anon]', ...args);
}

function parseArgs(argv) {
  const sids = [];
  for (let i = 2; i < argv.length; i++) {
    const a = argv[i];
    if (a === '--sid' || a === '--session') {
      const v = argv[i + 1];
      i++;
      if (v) sids.push(v);
    } else if (a.startsWith('--sid=')) {
      sids.push(a.slice('--sid='.length));
    } else if (a.startsWith('--session=')) {
      sids.push(a.slice('--session='.length));
    }
  }
  // Allow comma-separated list via single arg or env var
  const fromEnv = process.env.ANON_SID || process.env.SID || '';
  if (fromEnv) sids.push(fromEnv);
  // Split all by comma and normalize
  const flat = sids
    .flatMap((x) => String(x).split(',').map((s) => s.trim()))
    .filter(Boolean);
  return Array.from(new Set(flat));
}

async function dirExists(p) {
  try {
    const st = await fsp.stat(p);
    return st.isDirectory();
  } catch {
    return false;
  }
}

async function rmrf(p) {
  try {
    await fsp.rm(p, { recursive: true, force: true });
    return true;
  } catch {
    return false;
  }
}

async function main() {
  const sids = parseArgs(process.argv);
  if (sids.length === 0) {
    err(
      'Missing anonymous session ID(s). Use --sid <ID> or ANON_SID env.\n' +
        'Example: node scripts/free-anon-session.js --sid fp_abcd1234',
    );
    process.exit(1);
  }
  log('Looking for anonymous docs bound to sessionId(s):', sids.join(', '));

  const prisma = new PrismaClient();
  try {
    const docs = await prisma.document.findMany({
      where: { userId: null, sessionId: { in: sids } },
      orderBy: { createdAt: 'desc' },
    });
    if (docs.length === 0) {
      log('No anonymous documents found for provided sessionId(s).');
      return;
    }
    log(`Found ${docs.length} document(s):`);
    for (const d of docs) {
      log(
        `- id=${d.id} createdAt=${d.createdAt?.toISOString?.() || d.createdAt} status=${d.status} file=${d.originalFilename}`,
      );
    }

    // Best-effort: remove local files under ./data/documents/<docId>
    const DATA_BASE = path.join(process.cwd(), 'data', 'documents');
    for (const d of docs) {
      const docDir = path.join(DATA_BASE, d.id);
      if (await dirExists(docDir)) {
        const ok = await rmrf(docDir);
        if (ok) log(`Removed local files for ${d.id}: ${docDir}`);
        else warn(`Failed to remove local files for ${d.id}: ${docDir}`);
      }
    }

    // Delete from DB (CASCADE will cleanup related rows)
    const result = await prisma.document.deleteMany({
      where: { id: { in: docs.map((d) => d.id) } },
    });
    log(`Deleted ${result.count} document(s) from database.`);
  } catch (e) {
    err(e?.stack || String(e));
    process.exitCode = 1;
  } finally {
    await prisma.$disconnect();
  }
}

main();

