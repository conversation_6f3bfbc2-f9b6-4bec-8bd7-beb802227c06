#!/usr/bin/env tsx

/**
 * Production Environment Validation Script
 *
 * Validates that all required environment variables are set and
 * that critical services (S3, Redis, Database) are accessible.
 *
 * Usage:
 *   npx tsx scripts/validate-production-env.ts
 *
 * Exit codes:
 *   0 - All checks passed
 *   1 - One or more checks failed
 */

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';

interface CheckResult {
  name: string;
  status: 'pass' | 'fail' | 'warn';
  message: string;
}

const results: CheckResult[] = [];

function check(
  name: string,
  status: 'pass' | 'fail' | 'warn',
  message: string,
) {
  results.push({ name, status, message });
  const icon = status === 'pass' ? '✓' : status === 'warn' ? '⚠' : '✗';
  const color =
    status === 'pass'
      ? '\x1b[32m'
      : status === 'warn'
        ? '\x1b[33m'
        : '\x1b[31m';
  console.log(`${color}${icon}\x1b[0m ${name}: ${message}`);
}

async function validateEnvironmentVariables() {
  console.log('\n📋 Checking Environment Variables...\n');

  const required = [
    'DATABASE_URL',
    'AWS_REGION',
    'S3_BUCKET_NAME',
    'GEMINI_API_KEY',
    'CLERK_SECRET_KEY',
    'CLERK_PUBLISHABLE_KEY',
  ];

  const optional = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'REDIS_HOST',
    'REDIS_PORT',
    'REDIS_PASSWORD',
  ];

  for (const varName of required) {
    if (process.env[varName]) {
      check(varName, 'pass', 'Set');
    } else {
      check(varName, 'fail', 'MISSING - Required for production');
    }
  }

  for (const varName of optional) {
    if (process.env[varName]) {
      check(varName, 'pass', 'Set');
    } else {
      check(
        varName,
        'warn',
        'Not set - May use defaults or IAM role credentials',
      );
    }
  }
}

async function validateS3() {
  console.log('\n☁️  Checking S3 Access...\n');

  if (!process.env.AWS_REGION || !process.env.S3_BUCKET_NAME) {
    check('S3', 'fail', 'AWS_REGION or S3_BUCKET_NAME not configured');
    return;
  }

  try {
    const creds =
      process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
        ? {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
          }
        : undefined;

    const s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: creds,
    });

    // Test bucket access
    await s3Client.send(
      new ListObjectsV2Command({
        Bucket: process.env.S3_BUCKET_NAME,
        MaxKeys: 1,
      }),
    );

    check(
      'S3 Access',
      'pass',
      `Bucket '${process.env.S3_BUCKET_NAME}' accessible in ${process.env.AWS_REGION}`,
    );
  } catch (error: any) {
    const errorMsg = error.message?.substring(0, 100) || 'Unknown error';
    check('S3 Access', 'fail', `Cannot access S3: ${error.name} - ${errorMsg}`);

    if (error.name === 'NoSuchBucket') {
      console.log(
        `   → Bucket '${process.env.S3_BUCKET_NAME}' does not exist or is in a different region`,
      );
    } else if (error.name === 'InvalidAccessKeyId') {
      console.log('   → AWS_ACCESS_KEY_ID is invalid');
    } else if (error.name === 'SignatureDoesNotMatch') {
      console.log('   → AWS_SECRET_ACCESS_KEY is invalid');
    } else if (error.name === 'AccessDenied') {
      console.log(
        '   → AWS credentials lack permissions to access this bucket',
      );
    }
  }
}

async function validateRedis() {
  console.log('\n🔴 Checking Redis Connection...\n');

  const redisHost = process.env.REDIS_HOST || 'localhost';
  const redisPort = parseInt(process.env.REDIS_PORT || '6379', 10);
  const redisPassword = process.env.REDIS_PASSWORD;

  let redisClient: Redis | null = null;

  try {
    redisClient = new Redis({
      host: redisHost,
      port: redisPort,
      password: redisPassword,
      maxRetriesPerRequest: 3,
      connectTimeout: 5000,
      lazyConnect: true,
    });

    await redisClient.connect();

    // Test basic operations
    const testKey = 'validation:test:' + Date.now();
    await redisClient.set(testKey, 'test', 'EX', 10);
    const value = await redisClient.get(testKey);
    await redisClient.del(testKey);

    if (value === 'test') {
      check('Redis', 'pass', `Connected to ${redisHost}:${redisPort}`);
    } else {
      check('Redis', 'warn', 'Connected but read/write test failed');
    }
  } catch (error: any) {
    const errorMsg = error.message?.substring(0, 100) || 'Connection failed';
    check('Redis', 'fail', `Cannot connect: ${errorMsg}`);
    console.log(
      `   → Check that Redis is running at ${redisHost}:${redisPort}`,
    );
    console.log('   → In K8s deployments, REDIS_HOST should be "redis"');
  } finally {
    if (redisClient) {
      await redisClient.quit();
    }
  }
}

async function validateDatabase() {
  console.log('\n🗄️  Checking Database Connection...\n');

  if (!process.env.DATABASE_URL) {
    check('Database', 'fail', 'DATABASE_URL not set');
    return;
  }

  let prisma: PrismaClient | null = null;

  try {
    prisma = new PrismaClient();
    await prisma.$connect();

    // Test query
    const planCount = await prisma.plan.count();
    check('Database', 'pass', `Connected (${planCount} plans found)`);
  } catch (error: any) {
    const errorMsg = error.message?.substring(0, 100) || 'Connection failed';
    check('Database', 'fail', `Cannot connect: ${errorMsg}`);
    console.log('   → Check DATABASE_URL is correct');
    console.log('   → Ensure database is running and accessible');
  } finally {
    if (prisma) {
      await prisma.$disconnect();
    }
  }
}

async function main() {
  console.log('🔍 TransReed Production Environment Validation\n');
  console.log('='.repeat(60));

  await validateEnvironmentVariables();
  await validateS3();
  await validateRedis();
  await validateDatabase();

  console.log('\n' + '='.repeat(60));
  console.log('\n📊 Summary:\n');

  const passed = results.filter((r) => r.status === 'pass').length;
  const warned = results.filter((r) => r.status === 'warn').length;
  const failed = results.filter((r) => r.status === 'fail').length;

  console.log(`   ✓ Passed: ${passed}`);
  console.log(`   ⚠ Warnings: ${warned}`);
  console.log(`   ✗ Failed: ${failed}`);

  if (failed > 0) {
    console.log(
      '\n❌ Validation FAILED - Fix the errors above before deploying\n',
    );
    process.exit(1);
  } else if (warned > 0) {
    console.log(
      '\n⚠️  Validation passed with warnings - Review warnings before deploying\n',
    );
    process.exit(0);
  } else {
    console.log(
      '\n✅ All checks passed - Environment is ready for production\n',
    );
    process.exit(0);
  }
}

main().catch((error) => {
  console.error('\n💥 Validation script failed:', error);
  process.exit(1);
});
