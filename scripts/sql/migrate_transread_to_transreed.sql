-- Migration: move data and credentials from transread -> transreed
-- Run connected to a database other than transread/transreed (e.g., postgres):
--   psql "postgresql://USER:PASSWORD@HOST:PORT/postgres" -f scripts/sql/migrate_transread_to_transreed.sql

-- 1) Terminate any active connections to the old database so it can be renamed
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'transread' AND pid <> pg_backend_pid();

-- 2) If the old database exists, rename it to the new name
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_database WHERE datname = 'transread'
  ) AND NOT EXISTS (
    SELECT 1 FROM pg_database WHERE datname = 'transreed'
  ) THEN
    EXECUTE 'ALTER DATABASE transread RENAME TO transreed';
  END IF;
END $$;

-- 3) Roles: prefer having a transreed login role
--    Handle three scenarios:
--    A) Only transread exists -> rename it to transreed and set password
--    B) Both exist -> reassign ownership to transreed, drop transread
--    C) Neither exists -> create transreed
DO $$
DECLARE
  is_current_old BOOLEAN := (CURRENT_USER = 'transread');
BEGIN
  IF is_current_old THEN
    -- Connected as the old role; cannot rename/drop current session user.
    -- Ensure the new role exists.
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transreed') THEN
      EXECUTE 'CREATE ROLE transreed LOGIN PASSWORD ''transreed''';
    END IF;
    -- Ensure DB ownership as well.
    IF EXISTS (SELECT 1 FROM pg_database WHERE datname = 'transreed') THEN
      EXECUTE 'ALTER DATABASE transreed OWNER TO transreed';
    END IF;
    RAISE NOTICE 'Connected as transread; skipped REASSIGN/DROP/RENAME of current role. Reassign/cleanup will be attempted inside transreed DB below.';
  ELSE
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transread')
       AND EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transreed') THEN
      -- B) Both roles exist: drop the old after cleanup
      -- Ensure DB ownership as well
      IF EXISTS (SELECT 1 FROM pg_database WHERE datname = 'transreed') THEN
        EXECUTE 'ALTER DATABASE transreed OWNER TO transreed';
      END IF;
      -- Ownership reassignment happens inside transreed DB below. Drop old role later.
    ELSIF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transread') THEN
      -- A) Only old role exists: rename it
      EXECUTE 'ALTER ROLE transread RENAME TO transreed';
      EXECUTE 'ALTER ROLE transreed WITH PASSWORD ''transreed''';
      IF EXISTS (SELECT 1 FROM pg_database WHERE datname = 'transreed') THEN
        EXECUTE 'ALTER DATABASE transreed OWNER TO transreed';
      END IF;
    ELSIF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transreed') THEN
      -- C) No relevant roles: create the new role
      EXECUTE 'CREATE ROLE transreed LOGIN PASSWORD ''transreed''';
      IF EXISTS (SELECT 1 FROM pg_database WHERE datname = 'transreed') THEN
        EXECUTE 'ALTER DATABASE transreed OWNER TO transreed';
      END IF;
    END IF;
  END IF;
END $$;

-- 3b) Switch into the renamed DB and reassign/permissions
\echo Switching to database: transreed for ownership reassignment and grants
\connect transreed

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transreed') THEN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transread') THEN
      BEGIN
        EXECUTE 'REASSIGN OWNED BY transread TO transreed';
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'REASSIGN OWNED skipped: %', SQLERRM;
      END;
    END IF;

    BEGIN
      EXECUTE 'ALTER SCHEMA public OWNER TO transreed';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'ALTER SCHEMA public OWNER skipped: %', SQLERRM;
    END;

    BEGIN
      EXECUTE 'GRANT USAGE, CREATE ON SCHEMA public TO transreed';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'GRANT schema privileges skipped: %', SQLERRM;
    END;

    BEGIN
      EXECUTE 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO transreed';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'GRANT tables skipped: %', SQLERRM;
    END;

    BEGIN
      EXECUTE 'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO transreed';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'GRANT sequences skipped: %', SQLERRM;
    END;

    BEGIN
      EXECUTE 'GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO transreed';
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'GRANT functions skipped: %', SQLERRM;
    END;

    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'transread') THEN
      BEGIN
        EXECUTE 'ALTER DEFAULT PRIVILEGES FOR ROLE transread IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO transreed';
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ALTER DEFAULT PRIVILEGES (tables) skipped: %', SQLERRM;
      END;
      BEGIN
        EXECUTE 'ALTER DEFAULT PRIVILEGES FOR ROLE transread IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO transreed';
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ALTER DEFAULT PRIVILEGES (sequences) skipped: %', SQLERRM;
      END;
      BEGIN
        EXECUTE 'ALTER DEFAULT PRIVILEGES FOR ROLE transread IN SCHEMA public GRANT ALL PRIVILEGES ON FUNCTIONS TO transreed';
      EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ALTER DEFAULT PRIVILEGES (functions) skipped: %', SQLERRM;
      END;
    END IF;
  END IF;
END $$;

\echo Ownership reassignment/grants attempted. If transread still exists, you may drop it later when not connected as that role:
\echo   DROP OWNED BY transread; DROP ROLE transread;

-- 4) Optional: grant privileges on public schema in the renamed DB
--    (Run in db=transreed; these may be no-ops depending on ownership.)
-- You can run the below after reconnecting to transreed as a superuser:
--   psql ".../transreed" -c "GRANT ALL ON SCHEMA public TO transreed;"
--   psql ".../transreed" -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO transreed;"
