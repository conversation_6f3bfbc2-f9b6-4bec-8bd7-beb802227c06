import {
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { logger } from '@/lib/logger';
import { fileExistenceCache } from '@/lib/cache/file-existence-cache';

// S3-only storage service (no local filesystem writes)

export type StorageMode = 's3';

let cachedS3: { client: S3Client; bucket: string } | null = null;

function normalizeKey(parts: string[] | string): string {
  if (Array.isArray(parts)) {
    return parts
      .map((p) => String(p).replace(/^\/+|\/+$/g, ''))
      .filter(Boolean)
      .join('/');
  }
  return String(parts).replace(/^\/+/, '').replace(/\/+$/, '');
}

function getS3(): { client: S3Client; bucket: string } {
  if (cachedS3) return cachedS3;
  const region = process.env.AWS_REGION;
  const bucket = process.env.S3_BUCKET_NAME;
  if (!region || !bucket) throw new Error('S3 not configured');
  const creds =
    process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
      ? {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        }
      : undefined;

  // Log S3 configuration on first initialization
  logger.log('[S3] Initializing S3 client', {
    region,
    bucket,
    credentialsProvided: !!creds,
    accessKeyIdPresent: !!process.env.AWS_ACCESS_KEY_ID,
    secretAccessKeyPresent: !!process.env.AWS_SECRET_ACCESS_KEY,
  });

  cachedS3 = { client: new S3Client({ region, credentials: creds }), bucket };
  return cachedS3;
}

async function streamToBuffer(stream: any): Promise<Buffer> {
  const chunks: Uint8Array[] = [];
  return new Promise((resolve, reject) => {
    stream.on('data', (chunk: Uint8Array) => chunks.push(chunk));
    stream.on('end', () => resolve(Buffer.concat(chunks)));
    stream.on('error', reject);
  });
}

export function getStorageMode(): StorageMode {
  return 's3';
}

export async function saveFile(
  keyParts: string[] | string,
  data: Buffer | string,
  contentType?: string,
): Promise<string> {
  const key = normalizeKey(keyParts);
  const s3 = getS3();
  const body = typeof data === 'string' ? Buffer.from(data, 'utf-8') : data;
  logger.log('[S3] Saving to S3:', { contentType, key });
  await s3.client.send(
    new PutObjectCommand({
      Bucket: s3.bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    }),
  );

  // Invalidate cache after successful write
  await fileExistenceCache.delete(key);

  return key; // return S3 key
}

export async function getFileByKey(
  keyParts: string[] | string,
): Promise<Buffer> {
  const key = normalizeKey(keyParts);
  const s3 = getS3();
  logger.log('[S3] Reading from S3:', { key });
  const res = await s3.client.send(
    new GetObjectCommand({ Bucket: s3.bucket, Key: key }),
  );
  if (!res.Body) throw new Error('Empty S3 body');
  return streamToBuffer(res.Body as any);
}

export async function fileExistsFlexible(
  pointer: string | null | undefined,
  options?: { skipCache?: boolean; ttl?: number },
): Promise<boolean> {
  if (!pointer) return false;

  // Check cache first (unless skipCache flag set)
  if (!options?.skipCache) {
    const cached = await fileExistenceCache.get(pointer);
    if (cached !== null) {
      logger.log('[Cache] Hit for:', pointer);
      return cached;
    }
  }

  // Cache miss - check S3
  const s3 = getS3();
  try {
    logger.log('[S3] Checking existence in S3:', { key: pointer });
    await s3.client.send(
      new HeadObjectCommand({
        Bucket: s3.bucket,
        Key: normalizeKey(pointer),
      }),
    );

    // Cache positive result
    await fileExistenceCache.set(pointer, true, options?.ttl);
    return true;
  } catch (error: any) {
    const errorName = error?.name || 'UnknownError';
    const errorMessage = error?.message || 'Unknown error';
    const errorCode = error?.$metadata?.httpStatusCode;

    // Log detailed error information for debugging
    logger.error('[S3] File existence check failed', {
      pointer: pointer.substring(0, 100), // Truncate long paths
      bucket: s3.bucket,
      errorName,
      errorMessage: errorMessage.substring(0, 200), // Truncate long messages
      errorCode,
    });

    // Distinguish between different error types
    if (errorName === 'NotFound' || errorName === 'NoSuchKey') {
      // File genuinely doesn't exist - cache negative result
      logger.log('[S3] File not found (will cache):', {
        pointer: pointer.substring(0, 100),
      });
      await fileExistenceCache.set(pointer, false, 10);
      return false;
    }

    // For other errors (credentials, network, permissions), don't cache
    // This allows recovery when the underlying issue is fixed
    logger.warn('[S3] Infrastructure error (not caching to allow recovery):', {
      pointer: pointer.substring(0, 100),
      errorName,
    });
    return false;
  }
}

export async function readTextFlexible(
  pointer: string,
  encoding: BufferEncoding = 'utf-8',
): Promise<string> {
  const s3 = getS3();
  logger.log('[S3] Read Text from S3:', { key: pointer });
  const res = await s3.client.send(
    new GetObjectCommand({ Bucket: s3.bucket, Key: normalizeKey(pointer) }),
  );
  if (!res.Body) throw new Error('Empty S3 body');
  const buf = await streamToBuffer(res.Body as any);
  return buf.toString(encoding);
}

export async function readBufferFlexible(pointer: string): Promise<Buffer> {
  const s3 = getS3();
  logger.log('[S3] Read Buffer from S3:', { key: pointer });
  const res = await s3.client.send(
    new GetObjectCommand({ Bucket: s3.bucket, Key: normalizeKey(pointer) }),
  );
  if (!res.Body) throw new Error('Empty S3 body');
  return streamToBuffer(res.Body as any);
}
