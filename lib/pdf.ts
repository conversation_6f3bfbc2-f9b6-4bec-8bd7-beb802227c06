// PDF Outline utilities: robust (pdfjs) + lightweight fallbacks
// - extractOutlineWithPdfjs: uses pdfjs-dist to parse outline with titles and page numbers
// - extractPdfOutlineTitles: lightweight string scanner for `/Title (...)` or `/Title <...>`

function decodePdfStringBytes(bytes: Uint8Array): string {
  if (bytes.length >= 2) {
    // UTF-16 BOM detection
    const b0 = bytes[0];
    const b1 = bytes[1];
    if (b0 === 0xfe && b1 === 0xff) {
      const dec = new TextDecoder('utf-16be');
      return dec.decode(bytes.subarray(2));
    }
    if (b0 === 0xff && b1 === 0xfe) {
      const dec = new TextDecoder('utf-16le');
      return dec.decode(bytes.subarray(2));
    }
  }
  try {
    return new TextDecoder('utf-8', { fatal: false }).decode(bytes);
  } catch {
    return new TextDecoder('latin1').decode(bytes);
  }
}

function hexToBytes(hex: string): Uint8Array {
  const clean = hex.replace(/\s+/g, '');
  const out = new Uint8Array(Math.floor(clean.length / 2));
  for (let i = 0; i < out.length; i++) {
    out[i] = parseInt(clean.substr(i * 2, 2), 16) & 0xff;
  }
  return out;
}

export function extractPdfOutlineTitles(buf: Buffer): string[] {
  // Interpret buffer as latin1 to preserve 1:1 byte mapping.
  const s = buf.toString('latin1');
  const titles: string[] = [];
  let idx = 0;
  // 1) Literal string form: /Title (...)
  const needleParen = '/Title (';
  while (idx < s.length) {
    const start = s.indexOf(needleParen, idx);
    if (start === -1) break;
    let i = start + needleParen.length; // position at first char after '('
    let depth = 1;
    let escaped = false;
    // Capture until balanced ')' considering escapes and nested parentheses per PDF rules
    const startPos = i;
    for (; i < s.length; i++) {
      const ch = s[i];
      if (escaped) {
        escaped = false;
        continue;
      }
      if (ch === '\\') {
        escaped = true;
        continue;
      }
      if (ch === '(') depth++;
      else if (ch === ')') {
        depth--;
        if (depth === 0) {
          i++; // include the closing ')'
          break;
        }
      }
    }
    const raw = s.slice(startPos, i - 1); // content between parens
    // Convert to bytes per latin1 to preserve binary
    const bytes = new Uint8Array(raw.length);
    for (let j = 0; j < raw.length; j++) bytes[j] = raw.charCodeAt(j) & 0xff;
    let text = decodePdfStringBytes(bytes);
    // Unescape common sequences that remain visible
    text = text
      .replace(/\\\(/g, '(')
      .replace(/\\\)/g, ')')
      .replace(/\\n/g, '\n')
      .replace(/\\r/g, '\r')
      .replace(/\\t/g, '\t')
      .replace(/\\\\/g, '\\');
    text = text.trim();
    if (text) titles.push(text);
    idx = i;
  }
  // 2) Hex string form: /Title <....>
  idx = 0;
  const needleHex = '/Title <';
  while (idx < s.length) {
    const start = s.indexOf(needleHex, idx);
    if (start === -1) break;
    const begin = start + needleHex.length;
    const end = s.indexOf('>', begin);
    if (end === -1) break;
    const hex = s.slice(begin, end);
    try {
      const bytes = hexToBytes(hex);
      let text = decodePdfStringBytes(bytes);
      text = text.trim();
      if (text) titles.push(text);
    } catch {
      // ignore
    }
    idx = end + 1;
  }
  // De-duplicate consecutive identical titles
  return titles.filter((t, n, arr) => n === 0 || t !== arr[n - 1]);
}

// Robust outline extractor using pdfjs-dist to get titles and page numbers
export interface OutlineItem {
  name: string;
  level: number;
  startPageNumber?: number;
}

export async function extractOutlineWithPdfjs(
  buffer: Buffer,
): Promise<OutlineItem[]> {
  // Import on-demand to avoid loading in environments that don't need it
  const pdfjs = await import('pdfjs-dist/legacy/build/pdf.mjs');
  // Disable worker for Node
  // @ts-ignore - pdfjs typing variations
  pdfjs.GlobalWorkerOptions.workerSrc = undefined;

  const loadingTask = pdfjs.getDocument({ data: new Uint8Array(buffer) });
  const pdf = await loadingTask.promise;
  try {
    // Some PDFs compress outlines; pdfjs handles this
    const outline = await pdf.getOutline();
    if (!outline || outline.length === 0) return [];

    const results: OutlineItem[] = [];

    const resolvePageNumber = async (
      dest: unknown,
    ): Promise<number | undefined> => {
      try {
        let d: any = dest as any;
        if (typeof dest === 'string') {
          d = await (pdf as any).getDestination(dest as any);
        }
        const ref = Array.isArray(d) ? d[0] : undefined;
        if (!ref) return undefined;
        const pageIndex = await (pdf as any).getPageIndex(ref);
        return typeof pageIndex === 'number' ? pageIndex + 1 : undefined;
      } catch {
        return undefined;
      }
    };

    const walk = async (items: any[], level: number) => {
      for (const it of items) {
        const title = (it.title ?? '').toString().trim();
        let page = it.dest ? await resolvePageNumber(it.dest) : undefined;
        if (!page && it.url && typeof it.url === 'string') {
          const m = it.url.match(/[#&]page=(\d+)/i);
          if (m) page = parseInt(m[1], 10) || undefined;
        }
        results.push({
          name: title || 'Untitled',
          level,
          startPageNumber: page,
        });
        if (it.items && it.items.length) await walk(it.items, level + 1);
      }
    };

    await walk(outline as any[], 1);
    return results;
  } finally {
    try {
      await pdf.cleanup();
    } catch {}
    try {
      await pdf.destroy();
    } catch {}
  }
}

// Alternative robust extractor using pdf-parse's bundled PDFJS version
export async function extractOutlineWithPdfParse(
  buffer: Buffer,
): Promise<OutlineItem[]> {
  try {
    // This relies on pdf-parse's internal pdf.js path (as requested)
    const mod = await import('pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js');
    const PDFJS: any = (mod as any).default || (mod as any);
    if (!PDFJS || !PDFJS.getDocument) return [];
    // Disable worker in Node
    if (PDFJS.GlobalWorkerOptions)
      PDFJS.GlobalWorkerOptions.workerSrc = undefined;
    const loadingTask = PDFJS.getDocument({ data: new Uint8Array(buffer) });
    const pdf = await loadingTask.promise;
    try {
      const outline: any[] | null = await (pdf as any)
        .getOutline()
        .catch(() => null);
      if (!outline || outline.length === 0) return [];

      const results: OutlineItem[] = [];
      const resolvePageNumber = async (
        dest: unknown,
      ): Promise<number | undefined> => {
        try {
          let d: any = dest as any;
          if (typeof dest === 'string') {
            d = await (pdf as any).getDestination(dest as any);
          }
          const ref = Array.isArray(d) ? d[0] : undefined;
          if (!ref) return undefined;
          const pageIndex = await (pdf as any).getPageIndex(ref);
          return typeof pageIndex === 'number' ? pageIndex + 1 : undefined;
        } catch {
          return undefined;
        }
      };

      const walk = async (items: any[], level: number) => {
        for (const it of items) {
          const title = (it.title ?? '').toString().trim();
          let page = it.dest ? await resolvePageNumber(it.dest) : undefined;
          if (!page && it.url && typeof it.url === 'string') {
            const m = it.url.match(/[#&]page=(\d+)/i);
            if (m) page = parseInt(m[1], 10) || undefined;
          }
          results.push({
            name: title || 'Untitled',
            level,
            startPageNumber: page,
          });
          if (it.items && it.items.length) await walk(it.items, level + 1);
        }
      };

      await walk(outline as any[], 1);
      return results;
    } finally {
      try {
        await (pdf as any).cleanup?.();
      } catch {}
      try {
        await (pdf as any).destroy?.();
      } catch {}
    }
  } catch {
    return [];
  }
}

export async function extractOutline(buffer: Buffer): Promise<OutlineItem[]> {
  try {
    // Try pdf-parse bundled PDFJS first, per your request
    const viaPdfParse = await extractOutlineWithPdfParse(buffer);
    if (viaPdfParse.length > 0) return viaPdfParse;
  } catch {}
  try {
    const items = await extractOutlineWithPdfjs(buffer);
    if (items.length > 0) return items;
  } catch {
    // fall through to lightweight scan
  }
  const titles = extractPdfOutlineTitles(buffer);
  return titles.map((t) => ({ name: t, level: 1 }));
}
