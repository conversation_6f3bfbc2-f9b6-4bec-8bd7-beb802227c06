import { logger } from '@/lib/logger';

export function base64Decode(input: string | undefined): string {
  try {
    if (!input || typeof input !== 'string') {
      logger.error('Invalid Base64 input: expected string, got', typeof input);
      return '';
    }
    return Buffer.from(input, 'base64').toString('utf-8');
  } catch (error) {
    logger.error('Invalid Base64 string:', error);
    return '';
  }
}
