import { PDFDocument } from 'pdf-lib';
import {
  saveDocumentOriginal,
  savePagePdf,
  saveTranslatedSentences,
  saveTranslationMeta,
  savePageAudioFile,
  deletePagePdf,
} from './storage';
import {
  addPageAudio,
  getDocument,
  seedPagesForDocument,
  updateDocument,
} from './db';
import { translatePdfPageWithGemini } from './ai/gemini';
import { prisma } from './prisma';
import { synthesizeTtsGemini } from './ai/tts';
import { logger } from '@/lib/logger';
import { appConfig } from '@/lib/config';

export async function processDocumentPipeline(
  docId: string,
  original: Buffer,
  opts: { targetLanguage: string },
) {
  const doc = await getDocument(docId);
  if (!doc) return;
  await updateDocument(docId, { status: 'splitting' });

  await saveDocumentOriginal(docId, original);

  // Split into single-page PDFs
  const src = await PDFDocument.load(original);
  const pageCount = src.getPageCount();
  await updateDocument(docId, { pageCount });
  const pagesMeta = await seedPagesForDocument(docId, pageCount);

  await updateDocument(docId, { status: 'processing' });

  let carryover: string | undefined;
  for (const pm of pagesMeta) {
    try {
      // Build a one-page PDF
      const single = await PDFDocument.create();
      const [p] = await single.copyPages(src, [pm.pageNumber - 1]);
      single.addPage(p);
      const bytes = await single.save();
      const pagePath = await savePagePdf(
        docId,
        pm.pageNumber,
        Buffer.from(bytes),
      );
      await prisma.page.update({
        where: { id: pm.id },
        data: { s3KeySinglePagePdf: pagePath, status: 'processing' },
      });
      await updateDocument(docId, {}); // touch updatedAt

      const res = await translatePdfPageWithGemini({
        fileBytes: Buffer.from(bytes),
        pageNumber: pm.pageNumber,
        targetLanguage: opts.targetLanguage,
        carryoverText: carryover,
      });

      logger.log('<><><> res=', res);

      const textPath = await saveTranslatedSentences(
        docId,
        pm.pageNumber,
        opts.targetLanguage,
        res.translatedSentences || [],
      );
      await saveTranslationMeta(docId, pm.pageNumber, opts.targetLanguage, {
        sentences: res.sentences ?? [],
        endsWithCompleteSentence: res.endsWithCompleteSentence,
        isChapterStart: res.isChapterStart,
        isSkippable: res.isSkippable ?? false,
        detectedHeading: res.detectedHeading,
        headingConfidence: res.headingConfidence,
        modelVersion: res.modelVersion,
      });

      // Cleanup page PDF after successful translation (saves ~40-50% storage)
      if (appConfig.cleanupPagePdfsAfterTranslation) {
        await deletePagePdf(docId, pm.pageNumber);
      }

      await addPageAudio({
        pageId: pm.id,
        language: opts.targetLanguage,
        voice: 'female',
        ttsEngine: 'gemini',
        ttsParamsHash: 'default',
        modelVersion: res.modelVersion,
        s3KeyTranslatedText: textPath,
        s3KeyAudioMp3: null,
        duration: null,
      });

      await prisma.page.update({
        where: { id: pm.id },
        data: {
          status: 'ready',
          endsWithCompleteSentence: res.endsWithCompleteSentence,
          isChapterStart: res.isChapterStart,
          isSkippableByDefault: res.isSkippable ?? false,
        },
      });

      // Handle sentence carryover based on last incomplete sentence from previous page
      if (
        !res.endsWithCompleteSentence &&
        Array.isArray(res.sentences) &&
        res.sentences.length > 0
      ) {
        carryover = res.sentences[res.sentences.length - 1];
      } else {
        carryover = undefined;
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      logger.error('[processor] page error', pm.pageNumber, err);
    }
  }

  await updateDocument(docId, { status: 'ready' });
}

// Concurrently translate pages in rounds of 10, find the first not skippable, and synthesize TTS for it.
export async function primeFirstMeaningfulStart(
  docId: string,
  original: Buffer,
  opts: {
    targetLanguage: string;
    voice: string;
    rounds?: number;
    batchSize?: number;
  },
) {
  const pdf = await PDFDocument.load(original);
  const pageCount = pdf.getPageCount();
  const rounds = Math.min(opts.rounds ?? 3, 5);
  const batchSize = Math.max(1, Math.min(opts.batchSize ?? 10, 50));

  function mapLang(l?: string | null): string | undefined {
    switch ((l || '').toLowerCase()) {
      case 'en':
        return 'en-US';
      case 'vi':
        return 'vi-VN';
      case 'es':
        return 'es-ES';
      case 'fr':
        return 'fr-FR';
      case 'de':
        return 'de-DE';
      case 'ja':
        return 'ja-JP';
      case 'zh':
        return 'cmn-CN';
      case 'zh-cn':
        return 'cmn-CN';
      case 'zh-tw':
        return 'cmn-TW';
      default:
        return undefined;
    }
  }

  let found: {
    page: { id: string; pageNumber: number };
    res: { translatedSentences: string[]; endsWithCompleteSentence: boolean };
  } | null = null;

  for (let r = 0; r < rounds && !found; r++) {
    const startPage = r * batchSize + 1;
    if (startPage > pageCount) break;
    const endPage = Math.min(pageCount, startPage + batchSize - 1);

    const pages = await prisma.page.findMany({
      where: {
        documentId: docId,
        pageNumber: { gte: startPage, lte: endPage },
      },
      orderBy: { pageNumber: 'asc' },
    });
    // Launch all translations for the batch concurrently to minimize waiting.
    // NOTE: Carryover text is NOT passed during concurrent priming for performance.
    // This is intentional - priming prioritizes speed to start playback ASAP.
    // Pages 2+ will be re-processed with correct carryover when accessed on-demand
    // via stream-tts or process-page routes, ensuring sentence continuity for actual playback.
    const tasks = pages.map(async (p) => {
      try {
        const single = await PDFDocument.create();
        const [pg] = await single.copyPages(pdf, [p.pageNumber - 1]);
        single.addPage(pg);
        const bytes = await single.save();
        const pagePath = await savePagePdf(
          docId,
          p.pageNumber,
          Buffer.from(bytes),
        );
        await prisma.page.update({
          where: { id: p.id },
          data: { s3KeySinglePagePdf: pagePath, status: 'processing' },
        });

        // NOTE: No carryoverText passed here - see comment above
        const res = await translatePdfPageWithGemini({
          fileBytes: Buffer.from(bytes),
          pageNumber: p.pageNumber,
          targetLanguage: opts.targetLanguage,
        });
        const textPath = await saveTranslatedSentences(
          docId,
          p.pageNumber,
          opts.targetLanguage,
          res.translatedSentences || [],
        );
        await saveTranslationMeta(docId, p.pageNumber, opts.targetLanguage, {
          sentences: res.sentences ?? [],
          endsWithCompleteSentence: res.endsWithCompleteSentence,
          isChapterStart: res.isChapterStart,
          isSkippable: res.isSkippable ?? false,
          detectedHeading: res.detectedHeading,
          headingConfidence: res.headingConfidence,
          modelVersion: res.modelVersion,
        });

        // Cleanup page PDF after successful translation (saves ~40-50% storage)
        if (appConfig.cleanupPagePdfsAfterTranslation) {
          await deletePagePdf(docId, p.pageNumber);
        }

        const modelVersion = res.modelVersion || 'v1';
        await prisma.pageAudio.upsert({
          where: {
            pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
              pageId: p.id,
              language: opts.targetLanguage,
              voice: opts.voice,
              ttsEngine: 'gemini',
              ttsParamsHash: 'default',
              modelVersion,
            },
          },
          update: { s3KeyTranslatedText: textPath },
          create: {
            pageId: p.id,
            language: opts.targetLanguage,
            voice: opts.voice,
            ttsEngine: 'gemini',
            ttsParamsHash: 'default',
            modelVersion,
            s3KeyTranslatedText: textPath,
          },
        });
        await prisma.page.update({
          where: { id: p.id },
          data: {
            endsWithCompleteSentence: res.endsWithCompleteSentence,
            isChapterStart: res.isChapterStart ?? false,
            isSkippableByDefault: res.isSkippable ?? false,
            status: 'ready',
          },
        });
        return { page: { id: p.id, pageNumber: p.pageNumber }, res };
      } catch (e) {
        logger.error(
          '[prime] batch translate failed for page',
          p.pageNumber,
          e,
        );
        return null;
      }
    });

    // Resolve as soon as any non-skippable page finishes.
    const firstMeaningful = Promise.any(
      tasks.map(async (t) => {
        const r = await t;
        if (r && r.res && !r.res.isSkippable) return r;
        throw new Error('skippable');
      }),
    ).catch(() => null);

    const winner = await firstMeaningful;
    if (winner) {
      found = {
        page: winner.page,
        res: {
          translatedSentences: winner.res.translatedSentences || [],
          endsWithCompleteSentence: winner.res.endsWithCompleteSentence,
        },
      };
      // Allow the rest of the batch to finish in the background; do not schedule new batches.
      break;
    }
  }

  if (!found) return;

  // Save suggested start page and synthesize TTS for it
  const document = await prisma.document.update({
    where: { id: docId },
    data: { suggestedStartPageId: found.page.id },
  });
  // Build TTS text from already available translated sentences
  let sentences: string[] = found.res.translatedSentences;
  if (!found.res.endsWithCompleteSentence && sentences.length > 0)
    sentences = sentences.slice(0, -1);
  const ttsText = sentences.join(' ').trim();
  if (!ttsText) return;
  const languageCode = mapLang(opts.targetLanguage);
  const res = await synthesizeTtsGemini(ttsText, opts.voice, languageCode);

  // NOTE: Do NOT track usage during generation - only track during playback or claim
  // This prevents double-counting when audio is generated and then document is claimed

  if (!res.audioBytes) {
    throw new Error('TTS synthesis returned null audio bytes');
  }

  const audioPath = await savePageAudioFile(
    docId,
    found.page.pageNumber,
    opts.targetLanguage,
    opts.voice,
    res.audioBytes,
    res.ext,
  );
  await prisma.pageAudio.updateMany({
    where: {
      pageId: found.page.id,
      language: opts.targetLanguage,
      voice: opts.voice,
    },
    data: { s3KeyAudioMp3: audioPath, duration: res.durationSec },
  });
  await prisma.page.update({
    where: { id: found.page.id },
    data: { status: 'ready' },
  });
}
