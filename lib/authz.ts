import { auth } from '@clerk/nextjs/server';
import { prisma } from './prisma';
import { verifyToken } from './access-token';

// Parse anonymous session candidates from headers or query params
export function getAnonCandidatesFromReq(req: Request): string[] {
  const url = new URL(req.url);
  const out = new Set<string>();
  // Token in query string for media tags
  const at = url.searchParams.get('at');
  if (at) {
    // We don't validate here; just note it exists; actual validation happens below
  }
  const sid = url.searchParams.get('sid');
  const sids = url.searchParams.get('sids');
  if (sid) out.add(sid);
  if (sids) {
    try {
      // Accept CSV or JSON array
      if (sids.trim().startsWith('[')) {
        const arr = JSON.parse(sids);
        if (Array.isArray(arr)) {
          for (const v of arr) if (typeof v === 'string' && v) out.add(v);
        }
      } else {
        for (const v of sids.split(',').map((x) => x.trim())) if (v) out.add(v);
      }
    } catch {}
  }
  const h = req.headers;
  const hid = h.get('x-anon-id');
  const halt = h.get('x-anon-alt-ids');
  if (hid) out.add(hid);
  if (halt) {
    try {
      if (halt.trim().startsWith('[')) {
        const arr = JSON.parse(halt);
        if (Array.isArray(arr)) {
          for (const v of arr) if (typeof v === 'string' && v) out.add(v);
        }
      } else {
        for (const v of halt.split(',').map((x) => x.trim())) if (v) out.add(v);
      }
    } catch {}
  }
  // Referer fallback: if request has no ids but referrer URL includes sid/sids
  try {
    if (out.size === 0) {
      const ref = h.get('referer') || h.get('referrer');
      if (ref) {
        const ru = new URL(ref);
        const rsid = ru.searchParams.get('sid');
        const rsids = ru.searchParams.get('sids');
        if (rsid) out.add(rsid);
        if (rsids) {
          for (const v of rsids.split(',').map((x) => x.trim()))
            if (v) out.add(v);
        }
      }
    }
  } catch {}
  // Cookies fallback: tr_anon_sid (primary) and tr_anon_sids (csv)
  try {
    const cookie = h.get('cookie') || '';
    if (cookie) {
      const map = Object.fromEntries(
        cookie
          .split(';')
          .map((p) => p.trim())
          .filter(Boolean)
          .map((kv) => {
            const i = kv.indexOf('=');
            if (i === -1) return [kv, ''];
            const k = decodeURIComponent(kv.slice(0, i).trim());
            const v = decodeURIComponent(kv.slice(i + 1).trim());
            return [k, v];
          }),
      ) as Record<string, string>;
      const csid = map['tr_anon_sid'];
      const csids = map['tr_anon_sids'];
      if (csid) out.add(csid);
      if (csids) {
        for (const v of csids.split(',').map((x) => x.trim()))
          if (v) out.add(v);
      }
    }
  } catch {}
  const list = Array.from(out).filter(
    (x) => typeof x === 'string' && x.length > 0,
  );
  return list;
}

export async function getRequester(req: Request): Promise<{
  clerkUserId: string | null;
  dbUserId: string | null;
  anonCandidates: string[];
  accessToken?: {
    ok: boolean;
    type?: 'anon_doc' | 'user';
    docId?: string | null;
    userId?: string | null;
    jti?: string;
    error?: string;
  };
}> {
  const { userId } = await auth();
  let dbUserId: string | null = null;
  if (userId) {
    try {
      const u = await prisma.user.findUnique({
        where: { clerkUserId: userId },
      });
      dbUserId = u?.id || null;
    } catch {
      dbUserId = null;
    }
  }
  const anonCandidates = getAnonCandidatesFromReq(req);
  // Try access token from Authorization header or query param
  let token: string | null = null;
  try {
    const h =
      req.headers.get('authorization') || req.headers.get('Authorization');
    if (h && /^Bearer\s+/i.test(h)) token = h.split(/\s+/)[1];
  } catch {}
  if (!token) {
    try {
      const u = new URL(req.url);
      token = u.searchParams.get('at');
    } catch {}
  }
  let accessToken: any = undefined;
  if (token) {
    let vt: Awaited<ReturnType<typeof verifyToken>>;
    try {
      vt = await verifyToken(token);
    } catch {
      vt = { ok: false, error: 'invalid' } as const;
    }
    if (vt.ok === true) {
      accessToken = {
        ok: true,
        type: vt.row.type,
        docId: vt.row.docId,
        userId: vt.row.userId,
        jti: vt.row.id,
      };
    } else {
      accessToken = { ok: false, error: vt.error };
    }
  }
  return { clerkUserId: userId || null, dbUserId, anonCandidates, accessToken };
}

// Returns true if the requester can access this document
export async function authorizeDocumentAccess(
  req: Request,
  document: { id: string; userId: string | null; sessionId: string | null },
): Promise<boolean> {
  const { clerkUserId, dbUserId, anonCandidates, accessToken } =
    await getRequester(req);
  // Prefer access token when valid
  if (accessToken?.ok) {
    if (accessToken.type === 'user' && accessToken.userId && document.userId) {
      if (accessToken.userId === document.userId) return true;
    }
    if (accessToken.type === 'anon_doc' && accessToken.docId) {
      if (accessToken.docId === document.id) return true;
    }
  }
  if (clerkUserId && dbUserId) {
    return document.userId === dbUserId;
  }
  if (!clerkUserId) {
    if (document.sessionId && anonCandidates.includes(document.sessionId)) {
      return true;
    }
  }
  return false;
}

// Append anon session query params for browser media requests (no custom headers)
export async function appendAnonSidToUrlIfNeeded(
  req: Request,
  urlPath: string,
): Promise<string> {
  const { clerkUserId, anonCandidates } = await getRequester(req);
  if (clerkUserId) return urlPath;
  if (!anonCandidates || anonCandidates.length === 0) return urlPath;
  const u = new URL(urlPath, 'http://local');
  // Put the primary id as `sid` and full list as `sids`
  if (!u.searchParams.get('sid')) u.searchParams.set('sid', anonCandidates[0]);
  if (!u.searchParams.get('sids'))
    u.searchParams.set('sids', anonCandidates.join(','));
  const withQ = u.pathname + (u.search ? u.search : '');
  return withQ;
}
