// Simple helper for formatting processing durations in logs.
// Converts milliseconds to a human-readable "Xm Ys" string.
export function formatMs(ms: number): string {
  if (!Number.isFinite(ms) || ms <= 0) return '0s';
  const totalSec = Math.round(ms / 1000);
  const m = Math.floor(totalSec / 60);
  const s = totalSec % 60;
  if (m <= 0) return `${s}s`;
  const ss = String(s).padStart(2, '0');
  return `${m}m ${ss}s`;
}
