import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import {
  authorizeDocumentAccess,
  appendAnonSidToUrlIfNeeded,
} from '@/lib/authz';
import { ensureUserByClerkId } from '@/lib/user';
import {
  checkAnonymousFreeTrialLimit,
  canGenerateAudio,
} from '@/lib/plan-guard';
import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

type PageWithDocument = Prisma.PageGetPayload<{
  include: { document: true };
}>;

/**
 * Validates page access and returns page data with document
 * @returns Page with document or error response
 */
export async function validatePageAccess(
  pageId: string,
  req: Request,
): Promise<
  | { success: true; page: PageWithDocument }
  | { success: false; response: NextResponse }
> {
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: { document: true },
  });

  if (!page) {
    return {
      success: false,
      response: NextResponse.json({ error: 'Not found' }, { status: 404 }),
    };
  }

  // Authorization: signed-in owner or matching anonymous session
  const allowed = await authorizeDocumentAccess(req, {
    id: page.documentId,
    userId: page.document?.userId || null,
    sessionId: page.document?.sessionId || null,
  });

  if (!allowed) {
    return {
      success: false,
      response: NextResponse.json({ error: 'Unauthorized' }, { status: 403 }),
    };
  }

  return { success: true, page };
}

/**
 * Checks if streaming mode is enabled and returns appropriate response
 */
export async function checkStreamingMode(
  pageId: string,
  req: Request,
): Promise<NextResponse | null> {
  if (appConfig.ttsDeliveryMode === 'streaming') {
    logger.log(
      '[ensure-tts] Streaming mode enabled, using stream-tts endpoint',
    );
    return NextResponse.json({
      ok: true,
      streaming: true,
      streamUrl: await appendAnonSidToUrlIfNeeded(
        req,
        `/api/page/${pageId}/stream-tts`,
      ),
      status: 'ready',
    });
  }
  return null;
}

/**
 * Gets database user ID from Clerk user ID
 */
export async function getDbUserId(
  clerkUserId: string | null,
): Promise<string | null> {
  if (!clerkUserId) return null;

  try {
    const dbUserId = await ensureUserByClerkId(clerkUserId);
    logger.log('[ensure-tts] User ID mapping:', {
      clerkUserId,
      dbUserId,
    });
    return dbUserId;
  } catch (e) {
    logger.error('[ensure-tts] Failed to get DB user ID:', e);
    return null;
  }
}

/**
 * Checks anonymous user free trial limits
 */
export async function checkAnonymousLimits(
  documentId: string,
  clerkUserId: string | null,
): Promise<NextResponse | null> {
  if (clerkUserId) return null; // Skip for authenticated users

  try {
    const FREE_TRIAL_SECONDS = appConfig.freeTrialSeconds;
    const anonCheck = await checkAnonymousFreeTrialLimit(
      documentId,
      FREE_TRIAL_SECONDS,
    );

    if (!anonCheck.allowed) {
      logger.log('[ensure-tts] Anonymous free trial limit reached:', {
        documentId,
        usedSeconds: anonCheck.usedSeconds,
        limitSeconds: anonCheck.limitSeconds,
      });
      return NextResponse.json(
        {
          ok: false,
          requireLogin: true,
          reason: anonCheck.reason,
          usedSeconds: anonCheck.usedSeconds,
          limitSeconds: anonCheck.limitSeconds,
        },
        { status: 403 },
      );
    }
  } catch {}

  return null;
}

/**
 * Checks user plan limits before expensive operations
 */
export async function checkPlanLimits(
  dbUserId: string | null,
): Promise<NextResponse | null> {
  if (!dbUserId) return null;

  const audioCheck = await canGenerateAudio(dbUserId);
  if (!audioCheck.allowed) {
    logger.log('[ensure-tts] Plan limit reached, blocking translation:', {
      dbUserId,
      reason: audioCheck.reason,
      usedSeconds: audioCheck.usedSeconds,
      limitSeconds: audioCheck.limitSeconds,
    });
    return NextResponse.json(
      {
        ok: false,
        requireUpgrade: true,
        reason: audioCheck.reason,
        usedSeconds: audioCheck.usedSeconds,
        limitSeconds: audioCheck.limitSeconds,
        remainingSeconds: audioCheck.remainingSeconds,
      },
      { status: 403 },
    );
  }

  return null;
}

/**
 * Re-checks plan limits before generating individual chunks
 */
export async function recheckPlanLimit(
  dbUserId: string | null,
  pageNumber: number,
  chunkIndex: number,
): Promise<boolean> {
  if (!dbUserId) return true;

  try {
    const audioCheck = await canGenerateAudio(dbUserId);
    if (!audioCheck.allowed) {
      logger.warn('[tts] plan limit reached; skipping chunk', {
        page: pageNumber,
        dbUserId,
        chunk: chunkIndex,
        reason: audioCheck.reason,
      });
      return false;
    }
  } catch {}

  return true;
}
