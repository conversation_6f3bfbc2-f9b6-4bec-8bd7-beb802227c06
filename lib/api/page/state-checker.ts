import { Page } from '@prisma/client';
import { fileExistsFlexible } from '@/lib/file-service';
import { buildAudioPaths } from '@/lib/api/page/audio-cache';

/**
 * Build translation directory paths for a page
 */
export function buildTranslationPaths(
  documentId: string,
  pageNumber: number,
  language: string,
) {
  const translationsDir = [
    'documents',
    documentId,
    'pages',
    `page-${pageNumber}`,
    'translations',
    language,
  ].join('/');

  return {
    translationsDir,
    sentencesPath: `${translationsDir}/sentences.json`,
    metaPath: `${translationsDir}/meta.json`,
  };
}

/**
 * Check if complete audio exists for a page
 */
export async function checkAudioExists(
  page: Page,
  language: string,
  voice: string,
): Promise<boolean> {
  const paths = buildAudioPaths(
    page.documentId,
    page.pageNumber,
    language,
    voice,
  );
  return await fileExistsFlexible(paths.mp3Path);
}

/**
 * Check if translation exists for a page
 */
export async function checkTranslationExists(
  page: Page,
  language: string,
): Promise<boolean> {
  const paths = buildTranslationPaths(
    page.documentId,
    page.pageNumber,
    language,
  );
  return await fileExistsFlexible(paths.sentencesPath);
}

/**
 * Get the current processing state of a page
 *
 * @returns 'ready' if audio exists, 'translated' if translation exists but no audio, 'pending' otherwise
 */
export async function getPageProcessingState(
  page: Page,
  language: string,
  voice: string,
): Promise<'ready' | 'translated' | 'pending'> {
  const audioExists = await checkAudioExists(page, language, voice);
  if (audioExists) return 'ready';

  const translationExists = await checkTranslationExists(page, language);
  if (translationExists) return 'translated';

  return 'pending';
}
