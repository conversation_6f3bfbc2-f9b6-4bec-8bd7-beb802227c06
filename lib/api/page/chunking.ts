import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

/**
 * Map document targetLanguage to Google TTS language codes
 */
export function mapLanguageCode(l?: string | null): string | undefined {
  switch ((l || '').toLowerCase()) {
    case 'en':
      return 'en-US';
    case 'vi':
      return 'vi-VN';
    case 'es':
      return 'es-ES';
    case 'fr':
      return 'fr-FR';
    case 'de':
      return 'de-DE';
    case 'ja':
      return 'ja-JP';
    case 'zh':
      return 'cmn-CN';
    case 'zh-cn':
      return 'cmn-CN';
    case 'zh-tw':
      return 'cmn-TW';
    default:
      return undefined;
  }
}

/**
 * Chunk sentences into groups by word count
 */
export function chunkSentencesByWords(
  sentences: string[],
  maxWords: number,
): string[] {
  const chunks: string[] = [];
  let cur: string[] = [];
  let count = 0;
  const safeMax = Math.max(1, isFinite(maxWords) ? Math.floor(maxWords) : 260);

  for (const s of sentences) {
    const w = (s.match(/\S+/g) || []).length;
    if (cur.length > 0 && count + w > safeMax) {
      chunks.push(cur.join(' ').trim());
      cur = [s];
      count = w;
    } else {
      cur.push(s);
      count += w;
    }
  }

  if (cur.length > 0) {
    chunks.push(cur.join(' ').trim());
  }

  return chunks.filter((c) => c.length > 0);
}

/**
 * Two-tier chunking strategy result
 */
export interface ChunkingStrategyResult {
  ttsChunks: string[];
  chunkModels: string[];
}

/**
 * Two-tier chunking strategy (Gemini-optimized):
 * - First 5 chunks: use 60 words per chunk with flash model for speed
 * - Remaining chunks: use 160 words per chunk with flash model
 */
export function applyTwoTierChunkingStrategy(
  ttsSentences: string[],
): ChunkingStrategyResult {
  const TTS_CHUNK_WORDS = appConfig.ttsChunkWords;
  const FIRST_CHUNKS_COUNT = 5;
  const FIRST_CHUNKS_WORDS = TTS_CHUNK_WORDS; // 60 words
  const REMAINING_CHUNKS_WORDS = 160; // 160 words for subsequent chunks

  // First, create initial chunks to determine first 5 chunks worth of text
  const initialChunks = chunkSentencesByWords(ttsSentences, FIRST_CHUNKS_WORDS);
  let ttsChunks: string[] = [];
  let chunkModels: string[] = [];

  if (initialChunks.length <= FIRST_CHUNKS_COUNT) {
    // If total chunks <= 5, use flash model for all
    ttsChunks = initialChunks;
    chunkModels = Array(ttsChunks.length).fill('gemini-2.5-flash-preview-tts');
  } else {
    // Take first 5 chunks as-is (60 words each)
    const firstChunks = initialChunks.slice(0, FIRST_CHUNKS_COUNT);
    ttsChunks = [...firstChunks];
    chunkModels = Array(FIRST_CHUNKS_COUNT).fill(
      'gemini-2.5-flash-preview-tts',
    );

    // For remaining text, re-chunk with 160 words
    const targetWords = firstChunks.reduce(
      (sum, chunk) => sum + (chunk.match(/\S+/g) || []).length,
      0,
    );

    const remainingSentences: string[] = [];
    let currentWords = 0;
    for (const sentence of ttsSentences) {
      const sentenceWords = (sentence.match(/\S+/g) || []).length;
      if (currentWords + sentenceWords <= targetWords) {
        currentWords += sentenceWords;
      } else {
        remainingSentences.push(sentence);
      }
    }

    // Chunk remaining sentences with 160 words
    if (remainingSentences.length > 0) {
      const remainingChunks = chunkSentencesByWords(
        remainingSentences,
        REMAINING_CHUNKS_WORDS,
      );
      ttsChunks.push(...remainingChunks);
      chunkModels.push(
        ...Array(remainingChunks.length).fill('gemini-2.5-flash-preview-tts'),
      );
    }
  }

  logger.log('[tts] Gemini chunking strategy:', {
    totalSentences: ttsSentences.length,
    totalChunks: ttsChunks.length,
    firstChunksCount: Math.min(FIRST_CHUNKS_COUNT, ttsChunks.length),
    firstChunksWords: FIRST_CHUNKS_WORDS,
    remainingChunksWords: REMAINING_CHUNKS_WORDS,
    chunkModels: chunkModels.length,
    modelBreakdown: {
      flash: chunkModels.filter((m) => m.includes('flash')).length,
      pro: chunkModels.filter((m) => m.includes('pro')).length,
    },
  });

  return { ttsChunks, chunkModels };
}

/**
 * Vbee chunking strategy:
 * - If chunking is disabled (vbeeEnableChunking=false): sends whole text as single chunk
 * - If chunking is enabled (vbeeEnableChunking=true): uses larger chunks (500 words default) for async webhook processing
 * - No model differentiation needed since Vbee handles TTS processing
 */
export function applyVbeeChunkingStrategy(
  ttsSentences: string[],
): ChunkingStrategyResult {
  const VBEE_CHUNK_WORDS = appConfig.vbeeChunkWords;
  const VBEE_ENABLE_CHUNKING = appConfig.vbeeEnableChunking;

  let ttsChunks: string[];
  let chunkModels: string[];

  if (VBEE_ENABLE_CHUNKING) {
    // Create larger chunks for Vbee async processing
    ttsChunks = chunkSentencesByWords(ttsSentences, VBEE_CHUNK_WORDS);
    chunkModels = Array(ttsChunks.length).fill('vbee'); // Placeholder model name

    logger.log('[tts] Vbee chunking strategy (chunking enabled):', {
      totalSentences: ttsSentences.length,
      totalChunks: ttsChunks.length,
      chunkWords: VBEE_CHUNK_WORDS,
      avgChunkSize: Math.round(
        ttsChunks.reduce((sum, c) => sum + (c.match(/\S+/g) || []).length, 0) /
          ttsChunks.length,
      ),
    });
  } else {
    // Send whole text as single chunk
    const wholeText = ttsSentences.join(' ').trim();
    ttsChunks = [wholeText];
    chunkModels = ['vbee'];

    logger.log(
      '[tts] Vbee chunking strategy (chunking disabled - whole text):',
      {
        totalSentences: ttsSentences.length,
        totalChunks: 1,
        totalWords: (wholeText.match(/\S+/g) || []).length,
        textLength: wholeText.length,
      },
    );
  }

  return { ttsChunks, chunkModels };
}
