import path from 'path';
import { readTextFlexible } from '@/lib/file-service';
import { logger } from '@/lib/logger';

/**
 * Load translated sentences from storage
 */
export async function loadTranslatedSentences(
  s3KeyTranslatedText: string,
): Promise<string[]> {
  try {
    const raw = await readTextFlexible(s3KeyTranslatedText, 'utf-8');
    const arr = JSON.parse(raw);
    if (Array.isArray(arr)) {
      return arr.map((s) => String(s));
    }
  } catch {}
  return [];
}

/**
 * Get completeness flag from translation metadata
 */
export async function getEndsWithCompleteSentence(
  s3KeyTranslatedText: string,
  fallbackValue: boolean,
): Promise<boolean> {
  try {
    const metaDir = path.posix.dirname(s3KeyTranslatedText);
    const metaPath = path.posix.join(metaDir, 'meta.json');
    const metaRaw = await readTextFlexible(metaPath, 'utf-8');
    const meta = JSON.parse(metaRaw);
    if (meta && typeof meta.endsWithCompleteSentence === 'boolean') {
      return meta.endsWithCompleteSentence;
    }
  } catch {}
  return fallbackValue;
}

/**
 * Prepare TTS text by loading sentences and handling incomplete sentences
 */
export async function prepareTtsText(
  s3KeyTranslatedText: string,
  endsWithCompleteSentenceDB: boolean,
): Promise<{ ttsSentences: string[]; ttsText: string }> {
  const ttsSentences = await loadTranslatedSentences(s3KeyTranslatedText);
  const endsWithCompleteSentence = await getEndsWithCompleteSentence(
    s3KeyTranslatedText,
    endsWithCompleteSentenceDB,
  );

  // Remove last sentence if incomplete
  const finalSentences =
    !endsWithCompleteSentence && ttsSentences.length > 0
      ? ttsSentences.slice(0, -1)
      : ttsSentences;

  logger.log('Before synthesize TTS ttsSentences=', finalSentences);

  return {
    ttsSentences: finalSentences,
    ttsText: finalSentences.join(' ').trim(),
  };
}
