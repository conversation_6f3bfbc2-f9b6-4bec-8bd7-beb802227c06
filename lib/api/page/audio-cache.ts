import { NextResponse } from 'next/server';
import path from 'path';
import { prisma } from '@/lib/prisma';
import { fileExistsFlexible, readTextFlexible } from '@/lib/file-service';
import { appendAnonSidToUrlIfNeeded } from '@/lib/authz';
import { logger } from '@/lib/logger';
import { saveFile } from '@/lib/file-service';

/**
 * Build audio directory paths for a page
 */
export function buildAudioPaths(
  documentId: string,
  pageNumber: number,
  language: string,
  voice: string,
) {
  const audioDir = [
    'documents',
    documentId,
    'pages',
    `page-${pageNumber}`,
    'audio',
    language,
    voice,
  ].join('/');

  return {
    audioDir,
    mp3Path: path.posix.join(audioDir, 'audio.mp3'),
    chunksDir: path.posix.join(audioDir, 'chunks'),
    chunksMetaPath: path.posix.join(audioDir, 'chunks', 'meta.json'),
  };
}

/**
 * Check for existing complete audio and return response if found
 */
export async function checkExistingAudio(
  pageId: string,
  language: string,
  voice: string,
  req: Request,
): Promise<NextResponse | null> {
  const existingAudio = await prisma.pageAudio.findFirst({
    where: {
      pageId,
      language,
      voice,
      s3KeyAudioMp3: { not: null },
    },
  });

  if (
    existingAudio &&
    existingAudio.s3KeyAudioMp3 &&
    existingAudio.s3KeyAudioMp3.endsWith('.mp3') &&
    (await fileExistsFlexible(existingAudio.s3KeyAudioMp3))
  ) {
    return NextResponse.json({
      ok: true,
      url: await appendAnonSidToUrlIfNeeded(
        req,
        `/api/audio/${existingAudio.id}`,
      ),
      status: 'ready',
      processingStage: 'loading_audio',
    });
  }

  return null;
}

/**
 * Check for existing chunks and return response if found
 */
export async function checkExistingChunks(
  pageId: string,
  chunksDir: string,
  chunksMetaPath: string,
  mp3Path: string,
  language: string,
  voice: string,
  req: Request,
): Promise<NextResponse | null> {
  try {
    const metaExists = await fileExistsFlexible(chunksMetaPath);
    if (!metaExists) return null;

    const metaRaw = await readTextFlexible(chunksMetaPath, 'utf-8');
    const m = JSON.parse(metaRaw);
    const total = Number(m?.total || 0);
    if (total === 0) return null;

    // Check all chunks in parallel
    const indices = Array.from({ length: total }, (_, i) => i + 1);
    const res = await Promise.all(
      indices.map((i) =>
        fileExistsFlexible(path.posix.join(chunksDir, `chunk-${i}.mp3`)),
      ),
    );
    const existing = indices.filter((_, idx) => res[idx]);

    if (existing.length === 0) return null;

    // If all chunks complete and final mp3 exists, return final audio
    if (existing.length === total && (await fileExistsFlexible(mp3Path))) {
      const audioRow = await prisma.pageAudio.findFirst({
        where: { pageId, language, voice },
      });
      if (audioRow) {
        try {
          const page = await prisma.page.findUnique({ where: { id: pageId } });
          await prisma.pageAudio.update({
            where: { id: audioRow.id },
            data: { s3KeyAudioMp3: mp3Path },
          });
          if (page) {
            await prisma.page.update({
              where: { id: page.id },
              data: { status: 'ready' },
            });
          }
          return NextResponse.json({
            ok: true,
            url: await appendAnonSidToUrlIfNeeded(
              req,
              `/api/audio/${audioRow.id}`,
            ),
            status: 'ready',
            processingStage: 'loading_audio',
          });
        } catch {}
      }
    }

    // Return available chunks
    const urls = await Promise.all(
      existing.map(async (i) =>
        appendAnonSidToUrlIfNeeded(req, `/api/page/${pageId}/chunk/${i}`),
      ),
    );
    return NextResponse.json({
      ok: true,
      chunked: true,
      total,
      available: existing,
      urls,
      status: existing.length === total ? 'ready' : 'processing',
      processingStage:
        existing.length === total ? 'loading_audio' : 'generating_audio',
    });
  } catch (e) {
    logger.error('[ensure-tts] chunk check error:', e);
    return null;
  }
}

/**
 * Re-check if another request already produced audio
 */
export async function recheckAudioGeneration(
  pageId: string,
  language: string,
  voice: string,
  mp3Path: string,
  audioId: string,
  req: Request,
): Promise<NextResponse | null> {
  try {
    const fresh = await prisma.pageAudio.findFirst({
      where: {
        pageId,
        language,
        voice,
        s3KeyAudioMp3: { not: null },
      },
    });

    if (
      fresh &&
      fresh.s3KeyAudioMp3 &&
      fresh.s3KeyAudioMp3.endsWith('.mp3') &&
      (await fileExistsFlexible(fresh.s3KeyAudioMp3))
    ) {
      return NextResponse.json({
        ok: true,
        url: await appendAnonSidToUrlIfNeeded(req, `/api/audio/${fresh.id}`),
      });
    }

    if (await fileExistsFlexible(mp3Path)) {
      const page = await prisma.page.findUnique({ where: { id: pageId } });
      const row = await prisma.pageAudio.update({
        where: { id: audioId },
        data: { s3KeyAudioMp3: mp3Path },
      });
      if (page) {
        await prisma.page.update({
          where: { id: page.id },
          data: { status: 'ready' },
        });
      }
      return NextResponse.json({
        ok: true,
        url: await appendAnonSidToUrlIfNeeded(req, `/api/audio/${row.id}`),
        status: 'ready',
        processingStage: 'loading_audio',
      });
    }
  } catch {}

  return null;
}

/**
 * Check existing chunks during synthesis and return response if found
 */
export async function checkChunksDuringSynthesis(
  pageId: string,
  chunksDir: string,
  chunksMetaPath: string,
  mp3Path: string,
  audioId: string,
  req: Request,
): Promise<{
  existing: number[];
  totalChunks: number;
  response: NextResponse | null;
}> {
  let existing: number[] = [];
  let totalChunks = 0;

  try {
    const metaExists = await fileExistsFlexible(chunksMetaPath);
    if (metaExists) {
      const metaRaw = await readTextFlexible(chunksMetaPath, 'utf-8');
      const m = JSON.parse(metaRaw);
      const total = Number(m?.total || 0);
      if (total > 0) {
        totalChunks = total;
        // Check all chunks in parallel
        const indices = Array.from({ length: total }, (_, i) => i + 1);
        const res = await Promise.all(
          indices.map((i) =>
            fileExistsFlexible(path.posix.join(chunksDir, `chunk-${i}.mp3`)),
          ),
        );
        existing = indices.filter((_, idx) => res[idx]);

        // Fix meta.json if actual chunks don't match expected total
        if (existing.length > 0 && existing.length < total) {
          const isConsecutive = existing.every((idx, i) => idx === i + 1);
          if (isConsecutive) {
            try {
              const page = await prisma.page.findUnique({
                where: { id: pageId },
              });
              logger.warn('[ensure-tts] Fixing meta.json chunk count', {
                expected: total,
                actual: existing.length,
                page: page?.pageNumber,
              });
              totalChunks = existing.length;
              await saveFile(
                chunksMetaPath,
                JSON.stringify({ total: existing.length }, null, 2),
                'application/json',
              );
            } catch (e) {
              logger.error('[ensure-tts] Failed to fix meta.json', e);
            }
          }
        }
      }
    }
  } catch {}

  // If at least one chunk exists, return all available chunks immediately
  if (existing.length > 0) {
    const urls = await Promise.all(
      existing.map(async (i) =>
        appendAnonSidToUrlIfNeeded(req, `/api/page/${pageId}/chunk/${i}`),
      ),
    );

    // If all chunks are done and final exists, finalize DB pointers
    if (
      existing.length === totalChunks &&
      (await fileExistsFlexible(mp3Path))
    ) {
      try {
        const page = await prisma.page.findUnique({ where: { id: pageId } });
        await prisma.pageAudio.update({
          where: { id: audioId },
          data: { s3KeyAudioMp3: mp3Path },
        });
        if (page) {
          await prisma.page.update({
            where: { id: page.id },
            data: { status: 'ready' },
          });
        }
      } catch {}
    }

    return {
      existing,
      totalChunks,
      response: NextResponse.json({
        ok: true,
        chunked: true,
        total: totalChunks,
        available: existing,
        urls,
        status: existing.length === totalChunks ? 'ready' : 'processing',
        processingStage:
          existing.length === totalChunks
            ? 'loading_audio'
            : 'generating_audio',
      }),
    };
  }

  return { existing, totalChunks, response: null };
}
