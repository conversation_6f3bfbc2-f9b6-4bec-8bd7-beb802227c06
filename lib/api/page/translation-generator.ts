import path from 'path';
import { PDFDocument } from 'pdf-lib';
import { prisma } from '@/lib/prisma';
import {
  fileExistsFlexible,
  readBufferFlexible,
  readTextFlexible,
  saveFile,
} from '@/lib/file-service';
import { translatePdfPageWithGemini } from '@/lib/ai/gemini';
import {
  saveTranslatedSentences,
  saveTranslationMeta,
  deletePagePdf,
} from '@/lib/storage';
import { formatMs } from '@/lib/time';
import { logger } from '@/lib/logger';
import { appConfig } from '@/lib/config';

/**
 * Get page bytes for translation (from single-page PDF or extract from original)
 */
export async function getPageBytes(page: {
  id: string;
  s3KeySinglePagePdf: string | null;
  pageNumber: number;
  documentId: string;
  document: { s3KeyOriginal: string | null } | null;
}): Promise<Buffer> {
  // Try to get single-page PDF first
  try {
    if (
      page.s3KeySinglePagePdf &&
      (await fileExistsFlexible(page.s3KeySinglePagePdf))
    ) {
      return await readBufferFlexible(page.s3KeySinglePagePdf);
    }
  } catch {}

  // Fall back to extracting from original PDF
  const origPath = page.document?.s3KeyOriginal || '';
  if (!origPath) {
    throw new Error('Missing original PDF');
  }

  const origBuf = await readBufferFlexible(origPath);
  const src = await PDFDocument.load(origBuf);
  const single = await PDFDocument.create();
  const [p] = await single.copyPages(src, [page.pageNumber - 1]);
  single.addPage(p);
  const bytes = await single.save();
  return Buffer.from(bytes);
}

/**
 * Get carryover text from previous page if it ended mid-sentence
 */
export async function getCarryoverText(
  documentId: string,
  pageNumber: number,
  language: string,
): Promise<string | undefined> {
  if (pageNumber <= 1) return undefined;

  try {
    const prev = await prisma.page.findFirst({
      where: {
        documentId,
        pageNumber: pageNumber - 1,
      },
    });

    if (!prev) return undefined;

    const prevAudio = await prisma.pageAudio.findFirst({
      where: { pageId: prev.id, language },
    });

    if (!prevAudio || !prevAudio.s3KeyTranslatedText) return undefined;

    const metaDir = path.posix.dirname(prevAudio.s3KeyTranslatedText);
    const metaPath = path.posix.join(metaDir, 'meta.json');

    try {
      const metaRaw = await readTextFlexible(metaPath, 'utf-8');
      const meta = JSON.parse(metaRaw);
      if (
        meta &&
        meta.endsWithCompleteSentence === false &&
        Array.isArray(meta.sentences) &&
        meta.sentences.length > 0
      ) {
        return meta.sentences[meta.sentences.length - 1];
      }
    } catch {}
  } catch {}

  return undefined;
}

/**
 * Acquire translation lock to prevent duplicate work
 */
export async function acquireTranslationLock(
  documentId: string,
  pageNumber: number,
  language: string,
): Promise<boolean> {
  const translationsDir = [
    'documents',
    documentId,
    'pages',
    `page-${pageNumber}`,
    'translations',
    language,
  ].join('/');
  const translateLock = path.posix.join(translationsDir, '.lock');

  try {
    if (!(await fileExistsFlexible(translateLock))) {
      await saveFile(translateLock, String(Date.now()), 'text/plain');
      return true;
    }
  } catch {}

  return false;
}

/**
 * Generate translation for a page
 */
export async function generateTranslation(
  page: {
    id: string;
    pageNumber: number;
    documentId: string;
    s3KeySinglePagePdf: string | null;
    document: {
      s3KeyOriginal: string | null;
      targetLanguage: string | null;
    } | null;
  },
  language: string,
  voice: string,
): Promise<{
  audio: NonNullable<Awaited<ReturnType<typeof prisma.pageAudio.findFirst>>>;
  isSkippable: boolean;
} | null> {
  // Check if translation already exists
  const existing = await prisma.pageAudio.findFirst({
    where: {
      pageId: page.id,
      language,
      s3KeyTranslatedText: { not: null },
    },
  });

  if (existing) {
    return { audio: existing, isSkippable: false };
  }

  // Try to acquire lock
  const hasLock = await acquireTranslationLock(
    page.documentId,
    page.pageNumber,
    language,
  );

  if (!hasLock) {
    // Another request is processing, wait and check again
    const audio = await prisma.pageAudio.findFirst({
      where: {
        pageId: page.id,
        language,
        s3KeyTranslatedText: { not: null },
      },
    });
    if (audio) {
      return { audio, isSkippable: false };
    }
    return null;
  }

  // Get page bytes and carryover text
  const pageBytes = await getPageBytes(page);
  const carryoverText = await getCarryoverText(
    page.documentId,
    page.pageNumber,
    language,
  );

  // Translate
  const trStart = Date.now();
  const res = await translatePdfPageWithGemini({
    fileBytes: pageBytes,
    pageNumber: page.pageNumber,
    targetLanguage: language,
    carryoverText,
  });

  // Save translation
  const textPath = await saveTranslatedSentences(
    page.documentId,
    page.pageNumber,
    language,
    res.translatedSentences || [],
  );

  await saveTranslationMeta(page.documentId, page.pageNumber, language, {
    sentences: res.sentences ?? [],
    endsWithCompleteSentence: res.endsWithCompleteSentence,
    isChapterStart: res.isChapterStart,
    isSkippable: res.isSkippable ?? false,
    detectedHeading: res.detectedHeading,
    headingConfidence: res.headingConfidence,
    modelVersion: res.modelVersion,
  });

  // Cleanup page PDF after successful translation (saves ~40-50% storage)
  if (appConfig.cleanupPagePdfsAfterTranslation) {
    await deletePagePdf(page.documentId, page.pageNumber);
  }

  const trEnd = Date.now();
  const translatedChars = (res.translatedSentences || []).join(' ').length;
  logger.log('[translate] complete', {
    elapsed: formatMs(trEnd - trStart),
    page: page.pageNumber,
    lang: language,
    chars: translatedChars,
  });

  // Upsert PageAudio row
  const modelVersion = res.modelVersion || 'v1';
  const audio = await prisma.pageAudio.upsert({
    where: {
      pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
        pageId: page.id,
        language,
        voice,
        ttsEngine: 'gemini',
        ttsParamsHash: 'default',
        modelVersion,
      },
    },
    update: { s3KeyTranslatedText: textPath },
    create: {
      pageId: page.id,
      language,
      voice,
      ttsEngine: 'gemini',
      ttsParamsHash: 'default',
      modelVersion,
      s3KeyTranslatedText: textPath,
    },
  });

  // Update page metadata
  await prisma.page.update({
    where: { id: page.id },
    data: {
      endsWithCompleteSentence: res.endsWithCompleteSentence,
      isChapterStart: res.isChapterStart ?? false,
      isSkippableByDefault: res.isSkippable ?? false,
      status: 'processing',
    },
  });

  return { audio, isSkippable: res.isSkippable ?? false };
}

/**
 * Ensure audio row exists for desired voice
 */
export async function ensureAudioForVoice(
  pageId: string,
  language: string,
  voice: string,
  sourceAudio: {
    s3KeyTranslatedText: string | null;
    modelVersion: string | null;
  },
): Promise<
  NonNullable<Awaited<ReturnType<typeof prisma.pageAudio.findFirst>>>
> {
  const mv = sourceAudio.modelVersion || 'v1';
  return await prisma.pageAudio.upsert({
    where: {
      pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
        pageId,
        language,
        voice,
        ttsEngine: 'gemini',
        ttsParamsHash: 'default',
        modelVersion: mv,
      },
    },
    update: {
      s3KeyTranslatedText: sourceAudio.s3KeyTranslatedText ?? undefined,
    },
    create: {
      pageId,
      language,
      voice,
      ttsEngine: 'gemini',
      ttsParamsHash: 'default',
      modelVersion: mv,
      s3KeyTranslatedText: sourceAudio.s3KeyTranslatedText,
    },
  });
}
