import { buildS3DocKey, putS3Object, deleteS3Object } from './s3';

export async function saveDocumentOriginal(docId: string, bytes: Buffer) {
  const key = buildS3DocKey(['documents', docId, 'original.pdf']);
  await putS3Object(key, bytes, 'application/pdf');
  return key;
}

export async function savePagePdf(
  docId: string,
  pageNumber: number,
  bytes: Buffer,
) {
  const key = buildS3DocKey([
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'raw.pdf',
  ]);
  await putS3Object(key, bytes, 'application/pdf');
  return key;
}

export async function saveTranslatedText(
  docId: string,
  pageNumber: number,
  language: string,
  text: string,
) {
  // Legacy helper: write a plain text translation file
  const key = buildS3DocKey([
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'translations',
    language,
    'text.txt',
  ]);
  await putS3Object(key, text, 'text/plain; charset=utf-8');
  return key;
}

export async function saveTranslatedSentences(
  docId: string,
  pageNumber: number,
  language: string,
  sentences: string[],
) {
  // New helper: write translated sentences as JSON array
  const key = buildS3DocKey([
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'translations',
    language,
    'sentences.json',
  ]);
  const payload = Buffer.from(JSON.stringify(sentences, null, 2), 'utf-8');
  await putS3Object(key, payload, 'application/json');
  return key;
}

export async function saveTranslationMeta(
  docId: string,
  pageNumber: number,
  language: string,
  meta: any,
) {
  const key = buildS3DocKey([
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'translations',
    language,
    'meta.json',
  ]);
  const payload = Buffer.from(JSON.stringify(meta, null, 2), 'utf-8');
  await putS3Object(key, payload, 'application/json');
  return key;
}

export async function savePageAudioFile(
  docId: string,
  pageNumber: number,
  language: string,
  voice: string,
  bytes: Buffer,
  ext: 'wav' | 'mp3' = 'wav',
) {
  const key = buildS3DocKey([
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'audio',
    language,
    voice,
    `audio.${ext}`,
  ]);
  const contentType = ext === 'mp3' ? 'audio/mpeg' : 'audio/wav';
  await putS3Object(key, bytes, contentType);
  return key;
}

export async function deletePagePdf(docId: string, pageNumber: number) {
  const key = buildS3DocKey([
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'raw.pdf',
  ]);
  return deleteS3Object(key);
}
