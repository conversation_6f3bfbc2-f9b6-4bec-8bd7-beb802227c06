import { prisma } from './prisma';

function getCycleBounds(now: Date) {
  const y = now.getUTCFullYear();
  const m = now.getUTCMonth();
  const start = new Date(Date.UTC(y, m, 1, 0, 0, 0));
  const end = new Date(Date.UTC(y, m + 1, 1, 0, 0, 0));
  return { start, end };
}

export async function addListeningSecondsForUser(
  userId: string,
  seconds: number,
) {
  const sec = Math.max(0, Math.floor(seconds || 0));
  if (!userId || sec <= 0) return;
  const { start, end } = getCycleBounds(new Date());
  // Ensure a Usage row exists then increment listeningTime atomically
  await prisma.usage.upsert({
    where: { userId_cycleStart: { userId, cycleStart: start } },
    update: { listeningTime: { increment: sec } },
    create: { userId, cycleStart: start, cycleEnd: end, listeningTime: sec },
  });
}

export async function getUserCycleListeningSeconds(
  userId: string,
): Promise<number> {
  if (!userId) return 0;
  const { start } = getCycleBounds(new Date());
  const row = await prisma.usage.findUnique({
    where: { userId_cycleStart: { userId, cycleStart: start } },
    select: { listeningTime: true },
  });
  return Math.max(0, row?.listeningTime || 0);
}

export async function getUserPlanLimitSeconds(
  userId: string,
): Promise<number | null> {
  if (!userId) return null;
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { plan: true },
  });
  const lim = user?.plan?.includedListeningTime;
  if (lim == null) return null; // null => no limit
  return Math.max(0, lim);
}

export async function getRemainingListeningSeconds(
  userId: string,
): Promise<number | null> {
  const limit = await getUserPlanLimitSeconds(userId);
  if (limit == null) return null;
  const used = await getUserCycleListeningSeconds(userId);
  return Math.max(0, limit - used);
}
