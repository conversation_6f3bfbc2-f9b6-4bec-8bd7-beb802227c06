export type Voice = 'male' | 'female';

export type DocumentStatus =
  | 'uploaded'
  | 'splitting'
  | 'processing'
  | 'ready'
  | 'failed';

export interface DocumentMeta {
  id: string;
  userId?: string | null;
  sessionId?: string | null;
  originalFilename: string;
  contentType: string;
  sizeBytes: number;
  pageCount?: number;
  targetLanguage: string;
  voice: Voice;
  status: DocumentStatus;
  createdAt: string;
  updatedAt: string;
  authors?: string | null;
  hasFullAudio?: boolean;
  tocSource?: 'pdf_outline' | 'ai_inferred' | 'hybrid' | 'none';
}

export interface PageMeta {
  id: string;
  documentId: string;
  pageNumber: number;
  endsWithCompleteSentence: boolean;
  isChapterStart: boolean;
  isSkippableByDefault: boolean;
  role: 'front_matter' | 'toc' | 'content' | 'appendix' | 'index' | 'unknown';
  status: 'pending' | 'processing' | 'ready' | 'failed';
}

// DBShape removed — storage now uses Prisma/PostgreSQL

export interface Chapter {
  id: string;
  documentId: string;
  name: string;
  startPageNumber?: number;
  level: number;
  order: number;
}

export interface PageTranslation {
  id: string;
  pageId: string;
  language: string;
  translatedSentences: string[];
  endsWithCompleteSentence: boolean;
  isChapterStart: boolean;
  isSkippable: boolean;
  detectedHeading: string | null;
  headingConfidence: number;
  model: string;
  createdAt: string;
}
