// Lightweight client-side helpers to manage access tokens

let inMemoryToken: string | null = null;
let inMemoryType: 'anon_doc' | 'user' | null = null;

export function setAccessToken(token: string, type: 'anon_doc' | 'user') {
  inMemoryToken = token;
  inMemoryType = type;
  try {
    sessionStorage.setItem('tr_access_token', token);
    sessionStorage.setItem('tr_access_token_type', type);
  } catch {}
}

export function getAccessToken(): {
  token: string | null;
  type: 'anon_doc' | 'user' | null;
} {
  if (!inMemoryToken) {
    try {
      inMemoryToken = sessionStorage.getItem('tr_access_token');
      inMemoryType =
        (sessionStorage.getItem('tr_access_token_type') as any) || null;
    } catch {}
  }
  return { token: inMemoryToken, type: inMemoryType };
}

export function clearAccessToken() {
  inMemoryToken = null;
  inMemoryType = null;
  try {
    sessionStorage.removeItem('tr_access_token');
    sessionStorage.removeItem('tr_access_token_type');
  } catch {}
}

export function getAuthHeaders(
  base?: Record<string, string>,
): Record<string, string> {
  const h = { ...(base || {}) } as Record<string, string>;
  const { token } = getAccessToken();
  if (token) h['Authorization'] = `Bearer ${token}`;
  return h;
}

export function appendTokenToUrl(url: string): string {
  try {
    const { token } = getAccessToken();
    if (!token) return url;
    const u = new URL(
      url,
      typeof window !== 'undefined' ? window.location.origin : 'http://local',
    );
    if (!u.searchParams.get('at')) u.searchParams.set('at', token);
    return u.pathname + (u.search || '');
  } catch {
    return url;
  }
}

export async function refreshAccessToken(opts?: {
  docId?: string;
  anonId?: string;
  recaptchaToken?: string;
}) {
  try {
    // Strategy update: do not include reCAPTCHA on refresh/token issuance.
    const body: any = {};
    if (opts?.docId && opts?.anonId) {
      body.docId = opts.docId;
      body.anonId = opts.anonId;
    }
    const res = await fetch('/api/auth/token/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });
    const j = await res.json().catch(() => ({}));
    if (res.ok && j?.token && j?.type) {
      setAccessToken(j.token, j.type);
      return { ok: true };
    }
    return { ok: false, error: j?.error || 'refresh_failed' };
  } catch (e) {
    return { ok: false, error: 'refresh_failed' };
  }
}
