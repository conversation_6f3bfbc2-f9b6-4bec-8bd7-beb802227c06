import { prisma } from './prisma';
import { clerkClient } from '@clerk/nextjs/server';

// Ensure a Prisma user exists for a given Clerk userId. Returns the DB user id.
export async function ensureUserByClerkId(
  clerkUserId: string,
): Promise<string> {
  // Try to find existing first
  const existing = await prisma.user.findUnique({ where: { clerkUserId } });
  if (existing) return existing.id;

  // Ensure default Free plan exists to satisfy FK
  try {
    await prisma.plan.upsert({
      where: { key: 'free' },
      update: {},
      create: {
        key: 'free',
        name: 'Free',
        priceMonthlyCents: 0,
        description: 'Free plan',
        includedListeningTime: 600, // 10 minutes
      },
    });
  } catch {}

  // Fetch basic profile from Clerk for email/name when available
  let email: string | null = null;
  let name: string | null = null;
  try {
    const client = await clerkClient();
    const u = await client.users.getUser(clerkUserId);
    email = (u?.emailAddresses?.[0]?.emailAddress as string) || null;
    name = ((u?.firstName || '') + ' ' + (u?.lastName || '')).trim() || null;
  } catch {
    // best effort only
  }

  const created = await prisma.user.create({
    data: {
      clerkUserId,
      email,
      name,
      planKey: 'free',
      subscriptionStatus: 'inactive',
    },
  });
  return created.id;
}
