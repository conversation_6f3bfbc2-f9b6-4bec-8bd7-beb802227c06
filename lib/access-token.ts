import { prisma } from './prisma';
import crypto from 'crypto';

type JwtHeader = { alg: 'HS256'; typ: 'JWT' };
type JwtPayload = Record<string, any>;

function b64url(input: Buffer | string): string {
  const b = Buffer.isBuffer(input) ? input : Buffer.from(input);
  return b
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
}

function signHS256(payload: JwtPayload, secret: string): string {
  const header: JwtHeader = { alg: 'HS256', typ: 'JWT' };
  const h = b64url(JSON.stringify(header));
  const p = b64url(JSON.stringify(payload));
  const data = `${h}.${p}`;
  const sig = crypto.createHmac('sha256', secret).update(data).digest();
  return `${data}.${b64url(sig)}`;
}

function verifyHS256(token: string, secret: string): JwtPayload | null {
  try {
    const [h, p, s] = token.split('.');
    if (!h || !p || !s) return null;
    const data = `${h}.${p}`;
    const expected = b64url(
      crypto.createHmac('sha256', secret).update(data).digest(),
    );
    if (!crypto.timingSafeEqual(Buffer.from(s), Buffer.from(expected))) {
      return null;
    }
    const payload = JSON.parse(Buffer.from(p, 'base64').toString('utf-8'));
    return payload || null;
  } catch {
    return null;
  }
}

export type AccessTokenType = 'anon_doc' | 'user';

export function getAccessTokenTtlSeconds(): number {
  const raw = Number(process.env.ACCESS_TOKEN_TTL_SECONDS || 1800);
  return Number.isFinite(raw) && raw > 0 ? Math.floor(raw) : 1800;
}

export async function issueAnonDocToken(
  docId: string,
  sessionId: string,
  opts?: {
    maxRequests?: number;
    maxSeconds?: number;
    ipHash?: string | null;
    userAgentHash?: string | null;
  },
): Promise<{ token: string; jti: string; exp: number }> {
  const ttl = getAccessTokenTtlSeconds();
  const exp = Math.floor(Date.now() / 1000) + ttl;
  const row = await prisma.accessToken.create({
    data: {
      type: 'anon_doc',
      docId,
      sessionId,
      expiresAt: new Date(exp * 1000),
      maxRequests:
        opts?.maxRequests ??
        Number(process.env.ACCESS_TOKEN_ANON_MAX_REQUESTS || 50),
      maxSeconds:
        opts?.maxSeconds ??
        Number(process.env.ACCESS_TOKEN_ANON_MAX_SECONDS || 60),
      ipHash: opts?.ipHash || undefined,
      userAgentHash: opts?.userAgentHash || undefined,
    },
  });
  const payload: JwtPayload = {
    jti: row.id,
    typ: 'anon_doc',
    sub: docId,
    sid: sessionId,
    scope: 'doc:read page:read audio:read',
    exp,
  };
  const secret = process.env.ACCESS_TOKEN_SECRET || '';
  const token = signHS256(payload, secret);
  return { token, jti: row.id, exp };
}

export async function issueUserToken(
  userId: string,
  opts?: {
    maxRequests?: number;
    ipHash?: string | null;
    userAgentHash?: string | null;
  },
): Promise<{ token: string; jti: string; exp: number }> {
  const ttl = getAccessTokenTtlSeconds();
  const exp = Math.floor(Date.now() / 1000) + ttl;
  const row = await prisma.accessToken.create({
    data: {
      type: 'user',
      userId,
      expiresAt: new Date(exp * 1000),
      maxRequests: opts?.maxRequests ?? 5000,
      ipHash: opts?.ipHash || undefined,
      userAgentHash: opts?.userAgentHash || undefined,
    },
  });
  const payload: JwtPayload = {
    jti: row.id,
    typ: 'user',
    sub: userId,
    scope: 'doc:read page:read audio:read',
    exp,
  };
  const secret = process.env.ACCESS_TOKEN_SECRET || '';
  const token = signHS256(payload, secret);
  return { token, jti: row.id, exp };
}

export async function verifyToken(token: string): Promise<
  | {
      ok: true;
      claims: JwtPayload & {
        jti: string;
        typ: AccessTokenType;
        sub: string;
        exp: number;
      };
      row: {
        id: string;
        type: AccessTokenType;
        docId: string | null;
        userId: string | null;
        sessionId: string | null;
        expiresAt: Date;
        revokedAt: Date | null;
        maxRequests: number | null;
        usedRequests: number;
        maxSeconds: number | null;
        usedSeconds: number;
      };
    }
  | { ok: false; error: 'invalid' | 'expired' | 'revoked' }
> {
  const secret = process.env.ACCESS_TOKEN_SECRET || '';
  const claims = verifyHS256(token, secret);
  if (!claims) return { ok: false, error: 'invalid' };
  if (typeof claims.exp !== 'number' || Date.now() / 1000 >= claims.exp)
    return { ok: false, error: 'expired' };
  const jti = String(claims.jti || '');
  if (!jti) return { ok: false, error: 'invalid' };
  const row = await prisma.accessToken.findUnique({ where: { id: jti } });
  if (!row) return { ok: false, error: 'invalid' };
  if (row.revokedAt) return { ok: false, error: 'revoked' };
  return {
    ok: true,
    claims: claims as any,
    row: {
      id: row.id,
      type: row.type as AccessTokenType,
      docId: row.docId || null,
      userId: row.userId || null,
      sessionId: row.sessionId || null,
      expiresAt: row.expiresAt,
      revokedAt: row.revokedAt || null,
      maxRequests: row.maxRequests ?? null,
      usedRequests: row.usedRequests,
      maxSeconds: row.maxSeconds ?? null,
      usedSeconds: row.usedSeconds,
    },
  };
}

export async function consumeUsage(
  jti: string,
  delta: { requests?: number; secondsServed?: number },
) {
  const reqs = Math.max(0, Math.floor(Number(delta.requests || 0)));
  const secs = Math.max(0, Math.floor(Number(delta.secondsServed || 0)));
  const row = await prisma.accessToken.update({
    where: { id: jti },
    data: {
      usedRequests: { increment: reqs },
      usedSeconds: { increment: secs },
      updatedAt: new Date(),
    },
  });
  if (row.revokedAt) throw new Error('revoked');
  if (row.expiresAt.getTime() <= Date.now()) throw new Error('expired');
  if (row.maxRequests != null && row.usedRequests > row.maxRequests)
    throw new Error('limit_exceeded');
  if (row.maxSeconds != null && row.usedSeconds > row.maxSeconds)
    throw new Error('limit_exceeded');
}

export async function revokeBy(filter: {
  docId?: string;
  userId?: string;
  sessionId?: string;
}) {
  await prisma.accessToken.updateMany({
    where: {
      docId: filter.docId,
      userId: filter.userId,
      sessionId: filter.sessionId,
      revokedAt: null,
    },
    data: { revokedAt: new Date() },
  });
}

export function hashIpUa(
  ip?: string | null,
  ua?: string | null,
): { ipHash?: string; userAgentHash?: string } {
  const out: { ipHash?: string; userAgentHash?: string } = {};
  try {
    if (ip) out.ipHash = crypto.createHash('sha256').update(ip).digest('hex');
  } catch {}
  try {
    if (ua)
      out.userAgentHash = crypto.createHash('sha256').update(ua).digest('hex');
  } catch {}
  return out;
}
