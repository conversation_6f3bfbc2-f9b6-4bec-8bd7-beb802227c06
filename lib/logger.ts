/**
 * Simple logging utility with timestamp prefix
 * Format: [YYYY-MM-DD HH:MM:SS] message
 */

function getTimestamp(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `[${year}-${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

export const logger = {
  log: (...args: unknown[]) => {
    console.log(getTimestamp(), ...args);
  },

  info: (...args: unknown[]) => {
    console.info(getTimestamp(), ...args);
  },

  warn: (...args: unknown[]) => {
    console.warn(getTimestamp(), ...args);
  },

  error: (...args: unknown[]) => {
    console.error(getTimestamp(), ...args);
  },

  debug: (...args: unknown[]) => {
    console.debug(getTimestamp(), ...args);
  },
};
