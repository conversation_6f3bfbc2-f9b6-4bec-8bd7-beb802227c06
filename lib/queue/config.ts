import { Queue, QueueOptions } from 'bullmq';
import IORedis from 'ioredis';

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null, // Required for BullMQ
  enableReadyCheck: false,
};

// Create Redis connection for BullMQ
export const redisConnection = new IORedis(redisConfig);

// Common queue options
const defaultQueueOptions: QueueOptions = {
  connection: redisConnection,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
    removeOnComplete: {
      age: 24 * 3600, // Keep completed jobs for 24 hours
      count: 1000,
    },
    removeOnFail: {
      age: 7 * 24 * 3600, // Keep failed jobs for 7 days
    },
  },
};

// Document processing queue
export const documentQueue = new Queue(
  'document-processing',
  defaultQueueOptions,
);

// Queue names
export const QUEUE_NAMES = {
  DOCUMENT_PROCESSING: 'document-processing',
} as const;

// Job types
export const JOB_TYPES = {
  PROCESS_DOCUMENT: 'process-document',
  SPLIT_DOCUMENT: 'split-document',
  ANALYZE_DOCUMENT_STRUCTURE: 'analyze-document-structure', // Batch analysis of first N pages
  TRANSLATE_PAGE: 'translate-page', // Was: TRANSLATE_FIRST_PAGE
  GENERATE_PAGE_AUDIO: 'generate-page-audio', // Was: GENERATE_AUDIO_FIRST_PAGE
  PROCESS_PAGE: 'process-page', // Process individual page (translate + audio)
  FAST_ANALYZE_AND_PRIME: 'fast-analyze-and-prime', // Fast-track: analyze structure + process first page
  SPLIT_ALL_PAGES: 'split-all-pages', // Background: split all pages into individual PDFs
} as const;
