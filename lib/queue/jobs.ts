// Job payload interfaces

export interface ProcessDocumentJobData {
  docId: string;
  targetLanguage: string;
  voice: string;
}

export interface SplitDocumentJobData {
  docId: string;
  targetLanguage: string;
  voice: string;
}

export interface TranslatePageJobData {
  // Was: TranslateFirstPageJobData
  docId: string;
  targetLanguage: string;
  voice: string;
  pageId?: string; // Optional: if provided, translate specific page; if not, find first non-skippable
}

export interface GeneratePageAudioJobData {
  // Was: GenerateAudioFirstPageJobData
  docId: string;
  pageId: string;
  targetLanguage: string;
  voice: string;
}

export interface ProcessPageJobData {
  docId: string;
  pageId: string;
  pageNumber: number;
  targetLanguage: string;
  voice: string;
}

export interface AnalyzeDocumentStructureJobData {
  docId: string;
  targetLanguage: string;
  maxPagesToAnalyze?: number; // Default: 20, can expand to 30
}

export interface FastAnalyzeAndPrimeJobData {
  docId: string;
  targetLanguage: string;
  voice: string;
}

export interface SplitAllPagesJobData {
  docId: string;
}

// Union type for all job data
export type JobData =
  | ProcessDocumentJobData
  | SplitDocumentJobData
  | AnalyzeDocumentStructureJobData
  | TranslatePageJobData // Was: TranslateFirstPageJobData
  | GeneratePageAudioJobData // Was: GenerateAudioFirstPageJobData
  | ProcessPageJobData
  | FastAnalyzeAndPrimeJobData
  | SplitAllPagesJobData;

// Document status constants
export const DOCUMENT_STATUS = {
  UPLOADED: 'uploaded',
  TRANSLATING_FIRST_PAGE: 'translating_first_page',
  TRANSLATED_FIRST_PAGE: 'translated_first_page',
  GENERATING_AUDIO_FIRST_PAGE: 'generating_audio_first_page',
  GENERATED_AUDIO_FIRST_PAGE: 'generated_audio_first_page',
  FAILED: 'failed',
} as const;

export type DocumentStatus =
  (typeof DOCUMENT_STATUS)[keyof typeof DOCUMENT_STATUS];
