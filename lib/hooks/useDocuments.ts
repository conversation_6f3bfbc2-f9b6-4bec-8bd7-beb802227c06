import { useQuery } from '@tanstack/react-query';

interface Document {
  id: string;
  originalFilename: string;
  pageCount: number;
  createdAt: string;
  _count: { pages: number };
}

async function fetchDocuments(): Promise<Document[]> {
  const response = await fetch('/api/documents');
  if (!response.ok) {
    throw new Error('Failed to fetch documents');
  }
  return response.json();
}

export function useDocuments() {
  return useQuery({
    queryKey: ['documents'],
    queryFn: fetchDocuments,
  });
}
