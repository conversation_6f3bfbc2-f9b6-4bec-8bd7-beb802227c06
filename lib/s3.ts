import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { logger } from '@/lib/logger';

type S3Config = {
  region: string;
  bucket: string;
  accessKeyId?: string;
  secretAccessKey?: string;
};

let s3Client: S3Client | null = null;
let cachedBucket: string | null = null;
let warnedNoS3 = false;

export function getS3(): { client: S3Client; bucket: string } | null {
  const region = process.env.AWS_REGION;
  const bucket = process.env.S3_BUCKET_NAME;
  if (!region || !bucket) {
    if (!warnedNoS3) {
      logger.warn(
        '[s3] Not configured. Set AWS_REGION and S3_BUCKET_NAME to enable uploads.',
      );
      warnedNoS3 = true;
    }
    return null;
  }

  if (!s3Client) {
    const creds =
      process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
        ? {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
          }
        : undefined;
    s3Client = new S3Client({ region, credentials: creds });
    cachedBucket = bucket;
  }
  return s3Client && cachedBucket
    ? { client: s3Client, bucket: cachedBucket }
    : null;
}

export async function putS3Object(
  key: string,
  body: Buffer | Uint8Array | string,
  contentType?: string,
) {
  const s3 = getS3();
  if (!s3) return { uploaded: false };
  try {
    await s3.client.send(
      new PutObjectCommand({
        Bucket: s3.bucket,
        Key: key,
        Body: body,
        ContentType: contentType,
      }),
    );
    return { uploaded: true, key };
  } catch (err) {
    logger.warn('[s3] Upload failed', { key, err: (err as Error).message });
    return { uploaded: false };
  }
}

export async function deleteS3Object(key: string) {
  const s3 = getS3();
  if (!s3) return { deleted: false };
  try {
    await s3.client.send(
      new DeleteObjectCommand({
        Bucket: s3.bucket,
        Key: key,
      }),
    );
    logger.log('[s3] Deleted from S3:', { key });
    return { deleted: true, key };
  } catch (err) {
    logger.warn('[s3] Delete failed', { key, err: (err as Error).message });
    return { deleted: false };
  }
}

export function buildS3DocKey(parts: string[]) {
  // Normalize and join path segments with '/'
  const cleaned = parts.map((p) => String(p).replace(/^\/+|\/+$/g, ''));
  return cleaned.filter(Boolean).join('/');
}
