export type AppConfig = {
  baseUrl: string;
  crispWebsiteId?: string;
  freeTrialSeconds: number;
  signedInFreeSeconds: number;
  enableSignedInGate: boolean;
  ttsChunkWords: number;
  ttsMaxConcurrent: number;
  ttsProgressiveBatchSize: number;
  ttsProgressiveBatchDelayMs: number;
  ttsChunkStaggerDelayMs: number;
  vbeeChunkWords: number;
  vbeeEnableChunking: boolean;
  geminiTtsVoiceNameFemale?: string;
  geminiTtsVoiceNameMale?: string;
  openaiTtsVoiceNameFemale?: string;
  openaiTtsVoiceNameMale?: string;
  vbeeBaseUrl: string;
  vbeeVoiceNameFemale?: string;
  vbeeVoiceNameMale?: string;
  vbeeAudioFormat?: string;
  vbeeBitRate?: number;
  vbeeFirstChunkTimeoutMs: number;
  // Which Gemini integration to use for TTS synthesis
  // - 'auto': try GenAI Live → GenAI SDK → REST → GCP OAuth → Classic API key
  // - 'genai_live': Google GenAI Live (WebSocket-style)
  // - 'genai_sdk': Google GenAI SDK generateContentStream
  // - 'rest': Generative Language REST API with response_mime_type
  // - 'gcp_oauth': Google Cloud Text-to-Speech v1 via OAuth
  // - 'gcp_classic': Google Cloud Text-to-Speech v1 via API key
  geminiTtsMethod?:
    | 'auto'
    | 'genai_live'
    | 'genai_sdk'
    | 'rest'
    | 'gcp_oauth'
    | 'gcp_classic';
  gcpTtsModel?: string;
  gcpTtsVoiceName?: string;
  gcpTtsAudioEncoding?: string;
  // TTS delivery mode
  // - 'streaming': Use Gemini Live API to stream audio in real-time (eliminates gaps)
  // - 'chunked': Use traditional chunked synthesis (current default)
  ttsDeliveryMode?: 'streaming' | 'chunked';
  // Proactive next page processing threshold (seconds)
  // When current page audio remaining duration falls below this threshold,
  // trigger translation + TTS processing for the next page
  nextPageProcessThresholdSeconds: number;
  // Cleanup page PDFs after translation completes (saves ~40-50% storage)
  // Page PDFs are only needed during translation; once sentences.json exists, they're redundant
  // Set to false to keep page PDFs indefinitely for debugging
  cleanupPagePdfsAfterTranslation: boolean;
  // File existence cache configuration
  // Cache S3 HEAD request results in Redis to reduce costs (90% reduction expected)
  fileExistenceCacheTtlSeconds: number;
  // Enable file existence caching (set to false to disable)
  enableFileCache: boolean;
};

const crispWebsiteId = '3eb7584d-4470-4b55-bb44-3a2f8adc96f2';

export const appConfig: AppConfig = {
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  crispWebsiteId,

  freeTrialSeconds: 60,
  signedInFreeSeconds: 300,
  enableSignedInGate: true,

  ttsChunkWords: 60,
  ttsMaxConcurrent: 5,
  ttsProgressiveBatchSize: 2, // Process 2 chunks per batch
  ttsProgressiveBatchDelayMs: 5000, // 5 seconds between batch starts
  ttsChunkStaggerDelayMs: parseInt(
    process.env.TTS_CHUNK_STAGGER_DELAY_MS || '1000',
    10,
  ), // Delay between starting each chunk request (default: 1 second)
  vbeeChunkWords: 500, // Vbee supports larger chunks for async processing
  vbeeEnableChunking: false, // Set to true to split text into chunks, false to send whole text
  geminiTtsVoiceNameFemale: 'Zephyr',
  geminiTtsVoiceNameMale: 'Puck',
  openaiTtsVoiceNameFemale: 'nova', // OpenAI female voices: nova, shimmer
  openaiTtsVoiceNameMale: 'onyx', // OpenAI male voices: echo, fable, onyx

  // Vbee voice names ef: https://documenter.getpostman.com/view/12951168/Uz5FHbSd
  vbeeBaseUrl: 'https://vbee.vn',
  vbeeVoiceNameFemale: 'hn_female_hachi_book_22k-vc',
  vbeeVoiceNameMale: 'sg_male_chidat_ebook_48k-phg',
  vbeeAudioFormat: 'mp3',
  vbeeBitRate: 128,
  vbeeFirstChunkTimeoutMs: parseInt(
    process.env.VBEE_FIRST_CHUNK_TIMEOUT_MS || '120000',
    10,
  ),

  // Choose the Gemini TTS method here. No env override.
  // Options: 'auto' | 'genai_live' | 'genai_sdk' | 'rest' | 'gcp_oauth' | 'gcp_classic'
  // Using genai_live for real-time WebSocket-based TTS with Gemini Live API
  geminiTtsMethod: 'genai_sdk',
  gcpTtsModel: 'gemini-2.5-flash-preview-tts',
  // gcpTtsVoiceName: 'vi-VN-Neural2-A',
  gcpTtsVoiceName: 'vi-VN-Standard-A',
  gcpTtsAudioEncoding: 'MP3',
  // TTS delivery mode:
  // - 'streaming': Attempts to use streaming (currently not working - generateContentStream not supported for TTS)
  // - 'chunked': Traditional non-streaming approach (RECOMMENDED - this works reliably)
  // NOTE: According to Google's official docs, TTS models only support non-streaming generateContent
  ttsDeliveryMode: 'chunked',
  // Proactive next page processing: start translation + TTS when 150 seconds remain
  nextPageProcessThresholdSeconds: parseInt(
    process.env.NEXT_PAGE_PROCESS_THRESHOLD_SECONDS || '150',
    10,
  ),
  // Cleanup page PDFs after translation (saves ~40-50% storage)
  cleanupPagePdfsAfterTranslation: true,
  // File existence cache: 60s TTL for regular files, 30s for chunk-1, 10s for non-existent
  fileExistenceCacheTtlSeconds: 60,
  // Enable cache flag (set to false to disable caching)
  enableFileCache: true,
};
