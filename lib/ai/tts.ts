// Public TTS facade: only exports methods for external callers.
import { wavToMp3 } from './audio-utils';
import { formatMs } from '@/lib/time';
import type { TtsResult } from './tts-types';
import {
  ttsViaGcpGeminiOAuth,
  ttsViaGenAiSdk,
  ttsViaGenAiLive,
  ttsViaGenerativeLanguageAPI,
  ttsViaClassicGcpApiKey,
} from './providers/gemini'; // Barrel export from gemini/index.ts
import { ttsViaOpenAIAudioSpeech } from './providers/gpt';
import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

export type { TtsResult } from './tts-types';

// Retry utility for transient errors
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  label: string,
  maxRetries = 3,
): Promise<T> {
  let lastError: any;
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (e: any) {
      lastError = e;
      const errMsg = e?.message || String(e);
      const isTransient =
        errMsg.includes('500') ||
        errMsg.includes('503') ||
        errMsg.includes('timeout') ||
        errMsg.includes('ECONNRESET') ||
        errMsg.includes('ETIMEDOUT');

      if (!isTransient || attempt === maxRetries) {
        logger.error(
          `[tts] ${label} failed after ${attempt + 1} attempts`,
          errMsg,
        );
        throw e;
      }

      const backoffMs = Math.min(1000 * Math.pow(2, attempt), 8000);
      logger.warn(
        `[tts] ${label} attempt ${attempt + 1} failed, retrying in ${backoffMs}ms...`,
        errMsg,
      );
      await new Promise((resolve) => setTimeout(resolve, backoffMs));
    }
  }
  throw lastError;
}

export async function synthesizeTtsGemini(
  text: string,
  voice: string,
  languageCode?: string,
  model?: string,
  apiKey?: string,
  apiKeyIndex?: number,
): Promise<TtsResult> {
  const words = (text.match(/\S+/g) || []).length;

  const t1 = Date.now();
  logger.log('[tts] synthesize:start', {
    chars: text.length,
    words,
    voice,
    languageCode,
  });

  const method = appConfig.geminiTtsMethod || 'auto';
  let res: TtsResult | null = null;
  const tryWrap = async <T>(fn: () => Promise<T | null>, label: string) => {
    try {
      const out = await retryWithBackoff(() => fn(), label);
      if (!out) logger.error(`[tts] ${label} returned null`);
      return out as any;
    } catch (e) {
      logger.error(`[tts] ${label} error`, (e as Error)?.message || e);
      return null;
    }
  };

  if (method !== 'auto') {
    if (method === 'genai_live')
      res = await tryWrap(
        () =>
          ttsViaGenAiLive(
            text,
            voice,
            languageCode,
            model,
            apiKey,
            apiKeyIndex,
          ),
        'genai live',
      );
    else if (method === 'genai_sdk')
      res = await tryWrap(
        () =>
          ttsViaGenAiSdk(text, voice, languageCode, model, apiKey, apiKeyIndex),
        'genai sdk',
      );
    else if (method === 'rest')
      res = await tryWrap(
        () =>
          ttsViaGenerativeLanguageAPI(
            text,
            voice,
            languageCode,
            model,
            apiKey,
            apiKeyIndex,
          ),
        'genai rest',
      );
    else if (method === 'gcp_oauth')
      res = await tryWrap(
        () => ttsViaGcpGeminiOAuth(text, voice, languageCode, model),
        'gcp oauth',
      );
    else if (method === 'gcp_classic')
      res = await tryWrap(
        () => ttsViaClassicGcpApiKey(text, voice, languageCode, model),
        'gcp classic',
      );

    // Fallback to OpenAI if configured method fails
    if (!res && process.env.OPENAI_API_KEY) {
      logger.log(
        `[tts] Gemini method '${method}' failed, trying OpenAI fallback`,
      );
      res = await tryWrap(
        () => ttsViaOpenAIAudioSpeech(text, voice, languageCode),
        'openai fallback',
      );
    }
  } else {
    // Auto strategy: try a robust fallback sequence including OpenAI
    res = await tryWrap(
      () =>
        ttsViaGenAiLive(text, voice, languageCode, model, apiKey, apiKeyIndex),
      'genai live',
    );
    if (!res)
      res = await tryWrap(
        () =>
          ttsViaGenAiSdk(text, voice, languageCode, model, apiKey, apiKeyIndex),
        'genai sdk',
      );
    if (!res)
      res = await tryWrap(
        () =>
          ttsViaGenerativeLanguageAPI(
            text,
            voice,
            languageCode,
            model,
            apiKey,
            apiKeyIndex,
          ),
        'genai rest',
      );
    if (!res)
      res = await tryWrap(
        () => ttsViaGcpGeminiOAuth(text, voice, languageCode, model),
        'gcp oauth',
      );
    if (!res)
      res = await tryWrap(
        () => ttsViaClassicGcpApiKey(text, voice, languageCode, model),
        'gcp classic',
      );
    // Final fallback to OpenAI if all Gemini methods fail
    if (!res && process.env.OPENAI_API_KEY) {
      logger.log(
        '[tts] All Gemini methods failed, trying OpenAI as final fallback',
      );
      res = await tryWrap(
        () => ttsViaOpenAIAudioSpeech(text, voice, languageCode),
        'openai final fallback',
      );
    }
  }

  const t2 = Date.now();
  logger.log('[tts] synthesize:end', {
    ok: !!res,
    elapsed: formatMs(t2 - t1),
  });
  if (res) {
    logger.log('[tts] synthesize:gemini', {
      elapsed: formatMs(Date.now() - t2),
      format: res.ext,
      sec: res.durationSec,
      bytes: res.audioBytes?.length || 0,
    });
    if (res.ext === 'mp3') return res;
    if (!res.audioBytes) throw new Error('TTS did not produce audio bytes');
    const mp3 = await wavToMp3(res.audioBytes).catch(() => null);
    if (!mp3) throw new Error('TTS did not produce MP3 and conversion failed');
    return {
      audioBytes: mp3,
      durationSec: res.durationSec,
      contentType: 'audio/mpeg',
      ext: 'mp3',
    };
  }
  throw new Error('TTS synthesis failed');
}

export async function synthesizeTtsOpenAI(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult> {
  const t1 = Date.now();
  logger.log('[tts] openai:start', {
    chars: text.length,
    voice,
    languageCode,
  });

  const res = await ttsViaOpenAIAudioSpeech(text, voice, languageCode).catch(
    (e) => {
      logger.error('[tts] openai error', (e as Error)?.message || e);
      return null;
    },
  );
  if (res) {
    logger.log('[tts] openai:done', {
      elapsed: formatMs(Date.now() - t1),
      format: res.ext,
      sec: res.durationSec,
      bytes: res.audioBytes?.length || 0,
    });
    if (res.ext === 'mp3') return res;
    if (!res.audioBytes)
      throw new Error('OpenAI TTS did not produce audio bytes');
    const mp3 = await wavToMp3(res.audioBytes).catch(() => null);
    if (!mp3)
      throw new Error('OpenAI TTS did not produce MP3 and conversion failed');
    return {
      audioBytes: mp3,
      durationSec: res.durationSec,
      contentType: 'audio/mpeg',
      ext: 'mp3',
    };
  }
  throw new Error('OpenAI TTS synthesis failed');
}
