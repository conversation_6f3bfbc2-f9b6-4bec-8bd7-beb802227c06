export function buildStylePrompt(
  text: string,
  voice: string,
  languageCode?: string,
) {
  const gender = voice === 'male' ? 'male' : 'female';
  const langHint = languageCode ? ` in language ${languageCode}` : '';

  return `Voice: ${gender} adult${langHint}, warm, confident
Style: motivational, clear, balance storytelling
Speed: 140-160 wpm. Natural pauses at punctuation; avoid monotone
Technical: Read text exactly; ignore footnotes, symbols, links. Pronounce business/tech terms smoothly. No repetition
Dialogue if any: Slightly vary voices (A=firm/direct; B=lighter/friendly). Adjust emotion (surprise=higher/faster; encouragement=warm/uplifting; affirmation=firm/lower pitch)
Delivery Rules:
- Title/Heading: Slow, clear, slight downward pitch, pause before/after 1s
- Key Idea: Emphasize keywords, slight pitch rise, pause 0.5s
- Story/Example: More vivid, energetic; vary pace (faster in action, slower at climax)
- List: Read items distinctly, short pause 0.3s between; emphasize markers/numbers
- Question: Upward intonation, pause 0.5s for reflection
- Summary/Takeaway: Slow, confident, stress final words`;
}

export function wordsPerSecondEstimate(text: string) {
  const words = (text.match(/\S+/g) || []).length;
  // ~2.5 words/sec (150 WPM)
  return Math.max(1, Math.round(words / 2.5));
}
