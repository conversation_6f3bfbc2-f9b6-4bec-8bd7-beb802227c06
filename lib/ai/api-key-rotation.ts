/**
 * API Key Rotation Utility
 *
 * Provides round-robin rotation of Gemini API keys to distribute load
 * and avoid rate limits when processing multiple TTS chunks in parallel.
 *
 * Usage:
 * - For shared rotation across all requests: Use `getGeminiKeyRotator()` (singleton)
 * - For isolated rotation in tests: Use `new ApiKeyRotator(keys)` directly
 *
 * The singleton pattern ensures that all concurrent requests share the same
 * rotator instance, providing true round-robin distribution across the entire
 * application rather than per-request rotation.
 */

import { logger } from '@/lib/logger';

export class ApiKeyRotator {
  private keys: string[];
  private currentIndex: number = 0;

  constructor(keys: string[]) {
    // Filter out empty/null keys
    this.keys = keys.filter((k) => k && k.trim().length > 0);
    if (this.keys.length === 0) {
      throw new Error('At least one API key is required');
    }
    logger.log('[api-key-rotation] Initialized with', {
      keyCount: this.keys.length,
    });
  }

  /**
   * Get the next API key in rotation
   */
  getNextKey(): string {
    const key = this.keys[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.keys.length;
    return key;
  }

  /**
   * Get a specific key by index (for deterministic assignment)
   */
  getKeyByIndex(index: number): string {
    return this.keys[index % this.keys.length];
  }

  /**
   * Get total number of available keys
   */
  getKeyCount(): number {
    return this.keys.length;
  }

  /**
   * Reset rotation to start from the beginning
   */
  reset(): void {
    this.currentIndex = 0;
  }
}

/**
 * Create a rotator instance from environment variables
 */
export function createGeminiKeyRotator(): ApiKeyRotator {
  const keys: string[] = [];

  // Collect all available GEMINI_API_KEY* environment variables
  if (process.env.GEMINI_API_KEY) {
    keys.push(process.env.GEMINI_API_KEY);
  }
  if (process.env.GEMINI_API_KEY2) {
    keys.push(process.env.GEMINI_API_KEY2);
  }

  // Support additional keys (GEMINI_API_KEY3, GEMINI_API_KEY4, etc.)
  for (let i = 3; i <= 10; i++) {
    const key = process.env[`GEMINI_API_KEY${i}`];
    if (key) {
      keys.push(key);
    }
  }

  return new ApiKeyRotator(keys);
}

/**
 * Singleton instance of the Gemini API key rotator
 * Shared across all requests for proper round-robin distribution
 */
let geminiKeyRotatorInstance: ApiKeyRotator | null = null;

/**
 * Get the singleton Gemini API key rotator instance
 * Creates it on first call, then reuses the same instance
 */
export function getGeminiKeyRotator(): ApiKeyRotator {
  if (!geminiKeyRotatorInstance) {
    geminiKeyRotatorInstance = createGeminiKeyRotator();
    logger.log('[api-key-rotation] Singleton rotator created and cached');
  }
  return geminiKeyRotatorInstance;
}
