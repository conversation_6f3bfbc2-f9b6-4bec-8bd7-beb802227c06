/**
 * Manual test for API Key Rotation
 * Run with: npx tsx lib/ai/api-key-rotation.manual-test.ts
 */

import { ApiKeyRotator, getGeminiKeyRotator } from './api-key-rotation';

console.log('Testing API Key Rotation...\n');

// Test 1: Round-robin rotation
console.log('Test 1: Round-robin rotation');
const rotator = new ApiKeyRotator(['key1', 'key2', 'key3']);
console.log('  getNextKey() =', rotator.getNextKey()); // key1
console.log('  getNextKey() =', rotator.getNextKey()); // key2
console.log('  getNextKey() =', rotator.getNextKey()); // key3
console.log('  getNextKey() =', rotator.getNextKey()); // key1 (wraps)
console.log('  ✓ Round-robin rotation works\n');

// Test 2: Get by index
console.log('Test 2: Get by index (deterministic)');
const rotator2 = new ApiKeyRotator(['key1', 'key2', 'key3']);
console.log('  getKeyByIndex(0) =', rotator2.getKeyByIndex(0)); // key1
console.log('  getKeyByIndex(1) =', rotator2.getKeyByIndex(1)); // key2
console.log('  getKeyByIndex(2) =', rotator2.getKeyByIndex(2)); // key3
console.log('  getKeyByIndex(3) =', rotator2.getKeyByIndex(3)); // key1 (wraps)
console.log('  getKeyByIndex(10) =', rotator2.getKeyByIndex(10)); // key2 (10 % 3 = 1)
console.log('  ✓ Index-based access works\n');

// Test 3: Key count
console.log('Test 3: Key count');
console.log('  getKeyCount() =', rotator.getKeyCount()); // 3
console.log('  ✓ Key count correct\n');

// Test 4: Reset
console.log('Test 4: Reset rotation');
const rotator3 = new ApiKeyRotator(['key1', 'key2', 'key3']);
rotator3.getNextKey(); // key1
rotator3.getNextKey(); // key2
rotator3.reset();
console.log('  After reset, getNextKey() =', rotator3.getNextKey()); // key1
console.log('  ✓ Reset works\n');

// Test 5: Filter empty keys
console.log('Test 5: Filter empty keys');
const rotator4 = new ApiKeyRotator(['key1', '', 'key2', '', 'key3']);
console.log('  getKeyCount() =', rotator4.getKeyCount()); // 3
console.log('  getNextKey() =', rotator4.getNextKey()); // key1
console.log('  getNextKey() =', rotator4.getNextKey()); // key2
console.log('  getNextKey() =', rotator4.getNextKey()); // key3
console.log('  ✓ Empty keys filtered out\n');

// Test 6: Single key
console.log('Test 6: Single key');
const rotator5 = new ApiKeyRotator(['only-key']);
console.log('  getKeyCount() =', rotator5.getKeyCount()); // 1
console.log('  getNextKey() =', rotator5.getNextKey()); // only-key
console.log('  getNextKey() =', rotator5.getNextKey()); // only-key
console.log('  getKeyByIndex(5) =', rotator5.getKeyByIndex(5)); // only-key
console.log('  ✓ Single key works\n');

// Test 7: Error on no keys
console.log('Test 7: Error on no valid keys');
try {
  new ApiKeyRotator([]);
  console.log('  ✗ Should have thrown error');
} catch (e: any) {
  console.log('  ✓ Correctly threw error:', e.message);
}

// Test 8: Singleton rotator
console.log('\nTest 8: Singleton rotator behavior');
// Set up env vars for testing
process.env.GEMINI_API_KEY = 'test-key-1';
process.env.GEMINI_API_KEY2 = 'test-key-2';

const rotator6 = getGeminiKeyRotator();
const rotator7 = getGeminiKeyRotator();
console.log('  First call returns rotator:', rotator6 !== null);
console.log('  Second call returns same instance:', rotator6 === rotator7);
console.log('  Key count:', rotator6.getKeyCount());
console.log('  ✓ Singleton works - same instance returned\n');

console.log('✓ All tests passed!');
