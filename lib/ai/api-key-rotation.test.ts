/**
 * Unit tests for API Key Rotation
 */

import { ApiKeyRotator } from './api-key-rotation';

describe('ApiKeyRotator', () => {
  test('should rotate through keys in round-robin fashion', () => {
    const rotator = new ApiKeyRotator(['key1', 'key2', 'key3']);

    expect(rotator.getNextKey()).toBe('key1');
    expect(rotator.getNextKey()).toBe('key2');
    expect(rotator.getNextKey()).toBe('key3');
    expect(rotator.getNextKey()).toBe('key1'); // Wraps around
    expect(rotator.getNextKey()).toBe('key2');
  });

  test('should get key by index deterministically', () => {
    const rotator = new ApiKeyRotator(['key1', 'key2', 'key3']);

    expect(rotator.getKeyByIndex(0)).toBe('key1');
    expect(rotator.getKeyByIndex(1)).toBe('key2');
    expect(rotator.getKeyByIndex(2)).toBe('key3');
    expect(rotator.getKeyByIndex(3)).toBe('key1'); // Wraps around
    expect(rotator.getKeyByIndex(4)).toBe('key2');
    expect(rotator.getKeyByIndex(10)).toBe('key2'); // 10 % 3 = 1
  });

  test('should return correct key count', () => {
    const rotator = new ApiKeyRotator(['key1', 'key2', 'key3']);
    expect(rotator.getKeyCount()).toBe(3);
  });

  test('should reset rotation to start', () => {
    const rotator = new ApiKeyRotator(['key1', 'key2', 'key3']);

    rotator.getNextKey(); // key1
    rotator.getNextKey(); // key2
    rotator.reset();

    expect(rotator.getNextKey()).toBe('key1');
  });

  test('should filter out empty keys', () => {
    const rotator = new ApiKeyRotator([
      'key1',
      '',
      'key2',
      null as any,
      'key3',
    ]);
    expect(rotator.getKeyCount()).toBe(3);
    expect(rotator.getNextKey()).toBe('key1');
    expect(rotator.getNextKey()).toBe('key2');
    expect(rotator.getNextKey()).toBe('key3');
  });

  test('should throw error if no valid keys provided', () => {
    expect(() => new ApiKeyRotator([])).toThrow(
      'At least one API key is required',
    );
    expect(() => new ApiKeyRotator(['', '  ', null as any])).toThrow(
      'At least one API key is required',
    );
  });

  test('should work with single key', () => {
    const rotator = new ApiKeyRotator(['only-key']);
    expect(rotator.getKeyCount()).toBe(1);
    expect(rotator.getNextKey()).toBe('only-key');
    expect(rotator.getNextKey()).toBe('only-key');
    expect(rotator.getKeyByIndex(0)).toBe('only-key');
    expect(rotator.getKeyByIndex(5)).toBe('only-key');
  });
});
