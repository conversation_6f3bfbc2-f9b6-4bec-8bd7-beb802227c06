/**
 * Validation utilities for Gemini TTS
 */

/**
 * Validates if a voice name follows the classic GCP TTS naming convention
 * Examples: en-US-Neural2-A, vi-VN-Standard-B
 * @param name - Voice name to validate
 * @returns True if it matches classic GCP voice naming pattern
 */
export function isClassicGcpVoiceName(name: string): boolean {
  // Rough validation for classic Cloud TTS voices
  // Format: {language}-{region}-{type}-{variant}
  // e.g., en-US-Neural2-A, vi-VN-Standard-B
  return /^[a-z]{2,3}-[A-Z]{2}(?:-[A-Za-z0-9]+){1,3}$/.test(name);
}
