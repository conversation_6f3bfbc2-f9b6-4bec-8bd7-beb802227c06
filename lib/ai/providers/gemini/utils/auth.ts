/**
 * Authentication utilities for Google Cloud services
 */

import { createSign } from 'crypto';
import fs from 'fs';
import { ServiceAccount } from '../types';
import { base64url } from './encoding';
import { logger } from '@/lib/logger';

/**
 * Reads Google Cloud service account credentials from environment variables
 * Checks multiple possible sources in order:
 * 1. GCP_TTS_SERVICE_ACCOUNT_JSON (inline JSON or file path)
 * 2. GOOGLE_SERVICE_ACCOUNT_JSON (inline JSON or file path)
 * 3. GOOGLE_APPLICATION_CREDENTIALS (file path)
 */
export function readServiceAccountFromEnv(): ServiceAccount | null {
  const candidates = [
    process.env.GCP_TTS_SERVICE_ACCOUNT_JSON,
    process.env.GOOGLE_SERVICE_ACCOUNT_JSON,
  ].filter(Boolean) as string[];

  let jsonStr: string | null = null;

  // Try reading from inline JSON or file paths
  for (const c of candidates) {
    if (!c) continue;

    // Check if it's inline JSON
    if (c.trim().startsWith('{')) {
      jsonStr = c;
      break;
    }

    // Try reading as file path
    try {
      if (fs.existsSync(c)) {
        jsonStr = fs.readFileSync(c, 'utf8');
        break;
      }
    } catch {
      /* ignore */
    }
  }

  // Fallback to GOOGLE_APPLICATION_CREDENTIALS
  if (!jsonStr) {
    const credPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credPath) {
      try {
        jsonStr = fs.readFileSync(credPath, 'utf8');
      } catch {
        /* ignore */
      }
    }
  }

  if (!jsonStr) return null;

  // Parse and validate JSON
  try {
    const obj = JSON.parse(jsonStr);
    if (obj && obj.client_email && obj.private_key) {
      return obj as ServiceAccount;
    }
  } catch {
    /* ignore */
  }

  return null;
}

/**
 * Gets the GCP project ID from service account credentials
 */
export function getServiceAccountProjectId(): string | undefined {
  const sa = readServiceAccountFromEnv();
  return sa?.project_id;
}

/**
 * Generates a Google OAuth2 access token using service account credentials
 * @param opts - Options including explicit token or scopes
 * @returns Access token or null if authentication fails
 */
export async function getGoogleAccessToken(opts: {
  explicitToken?: string | undefined;
  scopes: string[] | string;
}): Promise<string | null> {
  // If explicit token provided, use it
  if (opts.explicitToken) return opts.explicitToken;

  // Read service account from environment
  const sa = readServiceAccountFromEnv();
  if (!sa) return null;

  // Create JWT for token exchange
  const now = Math.floor(Date.now() / 1000);
  const header = { alg: 'RS256', typ: 'JWT' };
  const scope = Array.isArray(opts.scopes)
    ? opts.scopes.join(' ')
    : opts.scopes;

  const payload = {
    iss: sa.client_email,
    scope,
    aud: 'https://oauth2.googleapis.com/token',
    iat: now,
    exp: now + 3600,
  } as const;

  // Sign JWT
  const unsigned = `${base64url(JSON.stringify(header))}.${base64url(JSON.stringify(payload))}`;
  const signer = createSign('RSA-SHA256');
  signer.update(unsigned);
  const sig = signer.sign(sa.private_key);
  const assertion = `${unsigned}.${base64url(sig)}`;

  // Exchange JWT for access token
  const body = new URLSearchParams({
    grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
    assertion,
  });

  const res = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body,
  });

  if (!res.ok) {
    logger.error(
      '[tts] SA token exchange failed',
      res.status,
      await res.text().catch(() => ''),
    );
    return null;
  }

  const j = await res.json();
  return (j.access_token as string) || null;
}
