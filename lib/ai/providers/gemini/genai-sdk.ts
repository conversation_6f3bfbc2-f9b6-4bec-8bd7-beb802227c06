/**
 * Google GenAI SDK TTS (non-streaming)
 *
 * Uses the Google GenAI SDK's generateContentStream method to synthesize
 * speech with the Gemini TTS model. Despite the method name, this function
 * collects all audio chunks and returns them as a complete buffer.
 *
 * Supports voice selection via prebuilt voice names and handles multiple
 * audio formats (PCM, WAV, MP3) with automatic conversion.
 */

import { GoogleGenAI } from '@google/genai';
import { TtsResult } from '../../tts-types';
import { wrapPcmToWav, wavToMp3 } from '../../audio-utils';
import { appConfig } from '../../../config';
import { logger } from '@/lib/logger';

/**
 * Synthesizes text to speech using Google GenAI SDK (non-streaming collection)
 *
 * @param text - The text to convert to speech
 * @param voice - Voice gender: 'male' or 'female'
 * @param languageCode - Optional language code (not currently used by this provider)
 * @param modelOverride - Optional model override (defaults to env var or 'gemini-2.5-pro-preview-tts')
 * @param apiKeyOverride - Optional API key override for rotation support
 * @param apiKeyIndex - Optional API key index for logging rotation info
 * @returns Audio result with bytes, duration, and format info, or null if failed
 */
export async function ttsViaGenAiSdk(
  text: string,
  voice: string,
  languageCode?: string,
  modelOverride?: string,
  apiKeyOverride?: string,
  apiKeyIndex?: number,
): Promise<TtsResult | null> {
  const apiKey = apiKeyOverride || process.env.GEMINI_API_KEY;
  if (!apiKey) return null;

  const model =
    modelOverride ||
    process.env.GEMINI_TTS_MODEL ||
    'gemini-2.5-pro-preview-tts';
  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;
  const genai = new GoogleGenAI({ apiKey });

  // Build style instructions + actual text as separate parts
  const contents = [
    {
      role: 'user',
      parts: [
        // { text: style },
        { text },
      ],
    },
  ];

  const config: any = {
    temperature: 1,
    responseModalities: ['audio'],
    speechConfig: {},
    generationConfig: { responseMimeType: 'audio/mpeg' },
  };
  if (prebuiltVoiceName) {
    config.speechConfig.voiceConfig = {
      prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
    };
  }

  const keyInfo =
    apiKeyIndex !== undefined
      ? ` [API Key #${apiKeyIndex + 1}]`
      : apiKeyOverride
        ? ' [Rotated Key]'
        : '';
  logger.log(
    `<><><> TTS GenAI SDK request${keyInfo}=`,
    JSON.stringify({ model, config, contents }),
  );

  // Stream audio chunks and stitch together
  let gen: any;
  try {
    gen = await genai.models.generateContentStream({ model, config, contents });
  } catch (e) {
    logger.error(`[tts] generateContentStream failed${keyInfo}:`, {
      error: (e as Error)?.message || e,
      model,
      voice,
      languageCode,
      textLength: text.length,
      textPreview: text.substring(0, 100) + '...',
      apiKeyIndex: apiKeyIndex !== undefined ? apiKeyIndex + 1 : undefined,
    });
    return null;
  }

  logger.log('<><><> TTS GenAI SDK gen=', gen);

  const chunks: Buffer[] = [];
  let detectedMime: string | undefined;
  let fmt: 'pcm' | 'wav' | 'mp3' | null = null;
  let sampleRate = 24000;

  try {
    for await (const chunk of (gen as any).stream ?? gen) {
      const parts = chunk?.candidates?.[0]?.content?.parts as any[] | undefined;
      if (!parts || !parts.length) continue;
      const inline = parts.find((p) => p?.inlineData?.data);
      if (!inline) continue;
      const inlineData = inline.inlineData;
      const m = inlineData?.mimeType as string | undefined;
      if (m && !detectedMime) {
        detectedMime = m;
        const lm = m.toLowerCase();
        if (lm.includes('pcm')) fmt = 'pcm';
        else if (lm.includes('wav')) fmt = 'wav';
        else if (lm.includes('mpeg') || lm.includes('mp3')) fmt = 'mp3';
        // try parse sample rate if provided like audio/pcm;rate=24000
        const rateMatch = lm.match(/rate\s*=\s*(\d{3,6})/);
        if (rateMatch)
          sampleRate = Math.max(
            8000,
            Math.min(48000, parseInt(rateMatch[1], 10) || 24000),
          );
      }
      const b64 = inlineData?.data as string | undefined;
      if (!b64) continue;
      let bytes = Buffer.from(b64, 'base64');
      if (fmt === 'wav') {
        // If multiple WAV chunks arrive, strip header from subsequent chunks and keep data only
        if (bytes.length > 44) bytes = bytes.slice(44);
      }
      chunks.push(bytes);
    }
  } catch (e) {
    logger.error(`[tts] error while reading GenAI stream${keyInfo}:`, {
      error: (e as Error)?.message || e,
      model,
      voice,
      languageCode,
      textLength: text.length,
      chunksReceived: chunks.length,
      detectedMime,
      apiKeyIndex: apiKeyIndex !== undefined ? apiKeyIndex + 1 : undefined,
    });
    return null;
  }

  if (!chunks.length) {
    logger.error(`[tts] no audio chunks received from GenAI stream${keyInfo}`, {
      model,
      voice,
      languageCode,
      textLength: text.length,
      detectedMime,
      apiKeyIndex: apiKeyIndex !== undefined ? apiKeyIndex + 1 : undefined,
    });
    return null;
  }

  // Decide output format and wrap/convert as needed
  let audioBytes: Buffer;
  let contentType: TtsResult['contentType'];
  let ext: TtsResult['ext'];
  if (detectedMime?.includes('mp3') || fmt === 'mp3') {
    audioBytes = Buffer.concat(chunks);
    contentType = 'audio/mpeg';
    ext = 'mp3';
  } else if (detectedMime?.includes('wav') || fmt === 'wav') {
    // Prepend proper WAV header around concatenated PCM data
    const dataBytes = Buffer.concat(chunks);
    const wav = wrapPcmToWav(dataBytes, sampleRate);
    audioBytes = wav;
    contentType = 'audio/wav';
    ext = 'wav';
  } else {
    // Default to PCM wrapped to WAV if unknown or PCM
    const dataBytes = Buffer.concat(chunks);
    const wavBytes = wrapPcmToWav(dataBytes, sampleRate);
    // Convert WAV to MP3 if possible
    const mp3 = await wavToMp3(wavBytes).catch(() => null);
    if (mp3) {
      audioBytes = mp3;
      contentType = 'audio/mpeg';
      ext = 'mp3';
    } else {
      audioBytes = wavBytes;
      contentType = 'audio/wav';
      ext = 'wav';
    }
  }

  // Estimate duration
  let durationSec = 0;
  if (ext === 'wav') {
    const dataLen = Math.max(0, audioBytes.length - 44);
    const bytesPerSample = 2; // 16-bit PCM
    durationSec = Math.max(
      1,
      Math.round(dataLen / (bytesPerSample * sampleRate)),
    );
  } else {
    durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
  }

  logger.log(`[tts] GenAI SDK success${keyInfo}`, {
    model,
    voice,
    languageCode,
    textLength: text.length,
    audioBytes: audioBytes.length,
    durationSec,
    format: ext,
    chunksReceived: chunks.length,
    apiKeyIndex: apiKeyIndex !== undefined ? apiKeyIndex + 1 : undefined,
  });

  return { audioBytes, durationSec, contentType, ext };
}
