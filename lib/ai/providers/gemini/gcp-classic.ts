/**
 * Google Cloud Text-to-Speech v1 API with API Key authentication
 *
 * Uses a simple API key (GCP_TTS_API_KEY) to synthesize speech via the
 * classic Google Cloud TTS API. This is a simpler authentication method
 * than OAuth but requires an API key to be configured in GCP Console.
 *
 * This provider uses SSML for basic pacing and pitch control, and always
 * returns MP3 format audio.
 */

import { TtsResult } from '../../tts-types';
import { logger } from '@/lib/logger';

/**
 * Synthesizes text to speech using Google Cloud TTS v1 with API key authentication
 *
 * @param text - The text to convert to speech
 * @param voice - Voice gender: 'male' or 'female'
 * @param languageCode - Optional language code (e.g., 'en-US', 'vi-VN')
 * @param modelOverride - Optional model override (not used by GCP Classic TTS)
 * @returns Audio result with bytes, duration, and format info, or null if failed
 */
export async function ttsViaClassicGcpApiKey(
  text: string,
  voice: string,
  languageCode?: string,
  modelOverride?: string,
): Promise<TtsResult | null> {
  const gcpKey = process.env.GCP_TTS_API_KEY;
  if (!gcpKey) return null;
  function escapeSsml(s: string) {
    return s.replace(
      /[&<>]/g,
      (c) => (({ '&': '&amp;', '<': '&lt;', '>': '&gt;' }) as any)[c],
    );
  }
  const parts = text.split(/([.!?…])\s+/).filter(Boolean);
  let ssmlContent = '';
  for (let i = 0; i < parts.length; i++) {
    const t = parts[i];
    ssmlContent += escapeSsml(t);
    if (/^[.!?…]$/.test(t) || /[.!?…]$/.test(t))
      ssmlContent += '<break time="200ms"/>';
    ssmlContent += ' ';
  }
  const ssml = `<speak><prosody rate="medium" pitch="0st">${ssmlContent.trim()}</prosody></speak>`;
  const body = {
    input: { ssml },
    voice: {
      languageCode: languageCode || 'en-US',
      ssmlGender: voice === 'male' ? 'MALE' : 'FEMALE',
    },
    audioConfig: { audioEncoding: 'MP3', speakingRate: 1.0, pitch: 0.0 },
  } as const;

  logger.log('<><><> TTS Classic GCP request=', JSON.stringify(body));
  const r = await fetch(
    `https://texttospeech.googleapis.com/v1/text:synthesize?key=${encodeURIComponent(gcpKey)}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    },
  );
  if (!r.ok) {
    logger.error(
      '[tts] GCP TTS request failed',
      r.status,
      await r.text().catch(() => ''),
    );
    return null;
  }
  const j = await r.json();
  const b64 = j.audioContent as string | undefined;
  if (!b64) return null;
  const bytes = Buffer.from(b64, 'base64');
  const durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
  return {
    audioBytes: bytes,
    durationSec,
    contentType: 'audio/mpeg',
    ext: 'mp3',
  };
}
