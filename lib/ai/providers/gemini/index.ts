/**
 * Gemini TTS Providers
 *
 * This module provides multiple TTS (Text-to-Speech) implementations using Google's Gemini API
 * and Google Cloud Text-to-Speech services.
 *
 * Available providers:
 * - REST API: Generative Language API (gemini-1.5-flash)
 * - GenAI SDK: Google GenAI SDK with generateContentStream
 * - GenAI Live: WebSocket-based Live API for real-time streaming
 * - GCP OAuth: Google Cloud TTS v1 with OAuth authentication
 * - GCP Classic: Google Cloud TTS v1 with API key authentication
 */

// Authentication utilities
export { getGoogleAccessToken } from './utils/auth';

// TTS Provider implementations
export { ttsViaGenerativeLanguageAPI } from './rest-api';
export { ttsViaGcpGeminiOAuth } from './gcp-oauth';
export { ttsViaClassicGcpApiKey } from './gcp-classic';
export { ttsViaGenAiSdk } from './genai-sdk';
export { ttsViaGenAiLive } from './genai-live';

// Streaming TTS implementations
export { streamTtsViaGenAiSdk } from './genai-sdk-stream';
export { streamTtsViaGenAiLive } from './genai-live-stream';

// Types (re-export if needed externally)
export type { ServiceAccount } from './types';
