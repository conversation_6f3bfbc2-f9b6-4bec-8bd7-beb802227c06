/**
 * Google Cloud Text-to-Speech v1 API with OAuth authentication
 *
 * Uses Google Cloud service account credentials to generate OAuth tokens
 * and synthesize speech using the classic GCP TTS API. Supports chunking
 * for long text and automatic WAV to MP3 conversion.
 *
 * This provider does not support custom voice style prompts - it uses
 * SSML for basic pacing and pitch control.
 */

import { TtsResult } from '../../tts-types';
import { wrapPcmToWav, wavToMp3 } from '../../audio-utils';
import { appConfig } from '../../../config';
import { getGoogleAccessToken, getServiceAccountProjectId } from './utils/auth';
import { isClassicGcpVoiceName } from './utils/validation';
import { logger } from '@/lib/logger';

/**
 * Synthesizes text to speech using Google Cloud TTS v1 with OAuth authentication
 *
 * @param text - The text to convert to speech
 * @param voice - Voice gender: 'male' or 'female'
 * @param languageCode - Optional language code (e.g., 'en-US', 'vi-VN')
 * @param modelOverride - Optional model override (not used by GCP Classic TTS)
 * @returns Audio result with bytes, duration, and format info, or null if failed
 */
export async function ttsViaGcpGeminiOAuth(
  text: string,
  voice: string,
  languageCode?: string,
  modelOverride?: string,
): Promise<TtsResult | null> {
  // This path uses Google Cloud Text-to-Speech v1 with OAuth (no prompt support).
  const token = await getGoogleAccessToken({
    explicitToken: process.env.GCP_TTS_OAUTH_TOKEN,
    scopes: ['https://www.googleapis.com/auth/cloud-platform'],
  });
  const projectId =
    process.env.GCP_TTS_PROJECT_ID || getServiceAccountProjectId();
  if (!token || !projectId) return null;

  const encodingEnv = (appConfig.gcpTtsAudioEncoding || 'MP3').toUpperCase();
  const preferredEncoding = ['MP3', 'LINEAR16', 'OGG_OPUS'].includes(
    encodingEnv,
  )
    ? encodingEnv
    : 'MP3';
  const lang = languageCode || 'en-US';
  let name = appConfig.gcpTtsVoiceName; // Optional: e.g. en-US-Neural2-A
  if (name && !isClassicGcpVoiceName(name)) {
    logger.warn(
      '[tts] GCP_TTS_VOICE_NAME appears non-classic, ignoring for v1 TTS:',
      name,
    );
    name = undefined;
  }

  // Build a light SSML wrapper to encourage pacing; classic TTS does not accept free-form prompts
  function escapeSsml(s: string) {
    return s.replace(
      /[&<>]/g,
      (c) => (({ '&': '&amp;', '<': '&lt;', '>': '&gt;' }) as any)[c],
    );
  }
  const parts = text.split(/([.!?…])\s+/).filter(Boolean);
  const ssmlWrapper = (inner: string) =>
    `<speak><prosody rate="medium" pitch="0st">${inner.trim()}</prosody></speak>`;

  // Make atomic units and chunk to <=5000 bytes per request
  const units: string[] = [];
  for (let i = 0; i < parts.length; i++) {
    const t = parts[i];
    let unit = escapeSsml(t);
    if (/^[.!?…]$/.test(t) || /[.!?…]$/.test(t))
      unit += '<break time="200ms"/>';
    unit += ' ';
    units.push(unit);
  }
  const MAX_BYTES = 5000;
  const chunks: string[] = [];
  let cur: string[] = [];
  for (const u of units) {
    const candidate = ssmlWrapper(cur.join('') + u);
    if (Buffer.byteLength(candidate, 'utf8') <= MAX_BYTES) {
      cur.push(u);
    } else {
      if (cur.length) chunks.push(ssmlWrapper(cur.join('')));
      cur = [u];
    }
  }
  if (cur.length) chunks.push(ssmlWrapper(cur.join('')));

  const willChunk =
    chunks.length > 1 ||
    (chunks[0] && Buffer.byteLength(chunks[0], 'utf8') > MAX_BYTES);
  const requestEncoding = willChunk ? 'LINEAR16' : preferredEncoding;
  const sampleRateHertz = 24000;

  async function synthesizeOne(ssml: string): Promise<Buffer | null> {
    const body: any = {
      input: { ssml },
      voice: name
        ? { languageCode: lang, name }
        : {
            languageCode: lang,
            ssmlGender: voice === 'male' ? 'MALE' : 'FEMALE',
          },
      audioConfig: {
        audioEncoding: requestEncoding,
        ...(requestEncoding === 'LINEAR16' ? { sampleRateHertz } : {}),
      },
    };
    logger.log(
      '<><><> TTS GCP v1 request (OAuth)=',
      JSON.stringify({
        ...body,
        input: { ssml: `[${Buffer.byteLength(ssml, 'utf8')} bytes SSML]` },
      }),
    );
    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
    if (projectId) headers['x-goog-user-project'] = projectId;
    const r = await fetch(
      'https://texttospeech.googleapis.com/v1/text:synthesize',
      {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      },
    );
    if (!r.ok) {
      logger.error(
        '[tts] GCP TTS v1 (OAuth) failed',
        r.status,
        await r.text().catch(() => ''),
      );
      return null;
    }
    const j = await r.json();
    const b64 = j?.audioContent as string | undefined;
    return b64 ? Buffer.from(b64, 'base64') : null;
  }

  if (!willChunk) {
    const singleSsml = chunks.length ? chunks[0] : ssmlWrapper(units.join(''));
    const bytes = await synthesizeOne(singleSsml);
    if (!bytes) return null;
    let contentType: TtsResult['contentType'] =
      requestEncoding === 'MP3' ? 'audio/mpeg' : 'audio/wav';
    let ext: TtsResult['ext'] = requestEncoding === 'MP3' ? 'mp3' : 'wav';
    let outBytes = bytes;
    if (requestEncoding === 'LINEAR16')
      outBytes = wrapPcmToWav(bytes, sampleRateHertz);
    const durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
    return { audioBytes: outBytes, durationSec, contentType, ext };
  }

  // Chunked: concatenate PCM and wrap, then convert if MP3 preferred
  const pcmChunks: Buffer[] = [];
  for (const ssml of chunks) {
    const buf = await synthesizeOne(ssml);
    if (!buf) return null;
    const isWav =
      buf.length >= 44 &&
      buf.toString('ascii', 0, 4) === 'RIFF' &&
      buf.toString('ascii', 8, 12) === 'WAVE';
    pcmChunks.push(isWav ? buf.slice(44) : buf);
  }
  const pcmAll = Buffer.concat(pcmChunks);
  const wavAll = wrapPcmToWav(pcmAll, sampleRateHertz);
  let audioBytes = wavAll;
  let contentType: TtsResult['contentType'] = 'audio/wav';
  let ext: TtsResult['ext'] = 'wav';
  if (preferredEncoding === 'MP3') {
    const mp3 = await wavToMp3(wavAll).catch(() => null);
    if (mp3) {
      audioBytes = mp3;
      contentType = 'audio/mpeg';
      ext = 'mp3';
    }
  }
  const bytesPerSample = 2;
  const durationSec = Math.max(
    1,
    Math.round(pcmAll.length / (bytesPerSample * sampleRateHertz)),
  );
  return { audioBytes, durationSec, contentType, ext };
}
