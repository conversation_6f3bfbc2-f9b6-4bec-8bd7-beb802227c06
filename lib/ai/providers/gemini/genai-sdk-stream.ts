/**
 * Google GenAI SDK TTS (streaming)
 *
 * Uses the Google GenAI SDK's generateContentStream method to synthesize
 * speech and yield audio chunks as they arrive. This allows for real-time
 * streaming of audio to clients.
 *
 * NOTE: According to Google's official documentation, TTS models may not
 * support streaming via generateContentStream. This implementation attempts
 * it with appropriate error handling and timeouts.
 */

import { GoogleGenAI } from '@google/genai';
import { appConfig } from '../../../config';
import { logger } from '@/lib/logger';

/**
 * Streams text-to-speech audio chunks as they arrive from Gemini
 *
 * @param text - The text to convert to speech
 * @param voice - Voice gender: 'male' or 'female'
 * @param languageCode - Optional language code (not currently used by this provider)
 * @yields Audio data chunks as Buffers
 * @throws Error if streaming fails or no chunks are received
 */
export async function* streamTtsViaGenAiSdk(
  text: string,
  voice: string,
  languageCode?: string,
): AsyncGenerator<Buffer, void, unknown> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    const err = new Error('No GEMINI_API_KEY found');
    logger.error('[tts-stream]', err.message);
    throw err;
  }

  const model = process.env.GEMINI_TTS_MODEL || 'gemini-2.5-pro-preview-tts';
  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;

  if (!prebuiltVoiceName) {
    const err = new Error(`No voice name configured for ${voice}`);
    logger.error('[tts-stream]', err.message);
    throw err;
  }

  const genai = new GoogleGenAI({ apiKey });

  const contents = [
    {
      role: 'user',
      parts: [{ text }],
    },
  ];

  const config: any = {
    temperature: 1,
    responseModalities: ['audio'],
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
      },
    },
    generationConfig: { responseMimeType: 'audio/mpeg' },
  };

  logger.log('[tts-stream] Starting stream', {
    model,
    voice: prebuiltVoiceName,
    textLength: text.length,
    textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
  });

  let gen: any;
  try {
    logger.log('[tts-stream] Calling generateContentStream with config:', {
      model,
      configKeys: Object.keys(config),
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey.substring(0, 10) + '...',
    });

    // IMPORTANT: generateContentStream is NOT officially documented for TTS models
    // According to ai.google.dev/gemini-api/docs/speech-generation, only non-streaming
    // generateContent is supported. We'll try it anyway with a timeout.

    // Add timeout to stream creation (10 seconds)
    const streamCreationTimeout = 10000;
    const streamPromise = genai.models.generateContentStream({
      model,
      config,
      contents,
    });

    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(
        () =>
          reject(
            new Error(
              'Stream creation timeout - generateContentStream may not be supported for TTS models',
            ),
          ),
        streamCreationTimeout,
      ),
    );

    gen = await Promise.race([streamPromise, timeoutPromise]);

    logger.log('[tts-stream] Stream created successfully', {
      genType: typeof gen,
      hasStream: !!(gen as any).stream,
      genKeys: Object.keys(gen || {}),
    });
  } catch (e) {
    logger.error('[tts-stream] generateContentStream failed:', {
      error: (e as Error)?.message || e,
      stack: (e as Error)?.stack,
      model,
      voice: prebuiltVoiceName,
      textLength: text.length,
      note: 'generateContentStream may not be supported for TTS models - only non-streaming generateContent is documented',
    });
    throw new Error(`Failed to create stream: ${(e as Error)?.message || e}`);
  }

  let chunkCount = 0;
  let totalBytes = 0;
  let firstChunkReceived = false;

  try {
    // Access the stream properly - check both .stream and direct iteration
    const iterator = gen.stream || gen;

    logger.log('[tts-stream] Starting to iterate stream', {
      hasStream: !!gen.stream,
      iteratorType: typeof iterator,
    });

    for await (const chunk of iterator) {
      if (!firstChunkReceived) {
        logger.log('[tts-stream] First chunk received', {
          chunkType: typeof chunk,
          hasCandidate: !!chunk?.candidates,
          candidatesLength: chunk?.candidates?.length,
        });
        firstChunkReceived = true;
      }

      const parts = chunk?.candidates?.[0]?.content?.parts as any[] | undefined;
      if (!parts || !parts.length) {
        logger.log('[tts-stream] Chunk has no parts, skipping');
        continue;
      }

      const inline = parts.find((p) => p?.inlineData?.data);
      if (!inline) {
        logger.log('[tts-stream] No inline data in chunk, skipping');
        continue;
      }

      const b64 = inline.inlineData?.data as string | undefined;
      if (!b64) {
        logger.log('[tts-stream] No base64 data in inline, skipping');
        continue;
      }

      let bytes = Buffer.from(b64, 'base64');

      // If it's WAV, strip header from subsequent chunks
      const mimeType = inline.inlineData?.mimeType || '';
      if (chunkCount > 0 && mimeType.includes('wav') && bytes.length > 44) {
        bytes = bytes.slice(44);
      }

      chunkCount++;
      totalBytes += bytes.length;

      logger.log('[tts-stream] Chunk yielded', {
        chunk: chunkCount,
        bytes: bytes.length,
        totalBytes,
        mimeType,
      });

      yield bytes;
    }

    if (chunkCount === 0) {
      throw new Error('Stream completed but no audio chunks were received');
    }

    logger.log('[tts-stream] Stream complete', {
      totalChunks: chunkCount,
      totalBytes,
    });
  } catch (e) {
    logger.error('[tts-stream] Error reading stream:', {
      error: (e as Error)?.message || e,
      stack: (e as Error)?.stack,
      chunksReceived: chunkCount,
      totalBytes,
      firstChunkReceived,
    });
    throw new Error(
      `Stream error after ${chunkCount} chunks: ${(e as Error)?.message || e}`,
    );
  }
}
