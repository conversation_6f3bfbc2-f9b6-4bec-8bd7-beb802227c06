/**
 * Gemini TTS via Generative Language REST API
 */

import { TtsResult } from '../../tts-types';
import { wrapPcmToWav, wavToMp3 } from '../../audio-utils';
import { buildStylePrompt } from '../../voice-style';
import { logger } from '@/lib/logger';

/**
 * Generates TTS audio using the Generative Language REST API
 * Tries multiple audio formats (MP3, WAV, PCM) and returns the first successful result
 *
 * @param text - Text to convert to speech
 * @param voice - Voice gender ('male' or 'female')
 * @param languageCode - Optional language code (e.g., 'en-US')
 * @param modelOverride - Optional model override (defaults to 'gemini-1.5-flash')
 * @param apiKeyOverride - Optional API key override for rotation support
 * @param apiKeyIndex - Optional API key index for logging rotation info
 * @returns TTS result with audio bytes, or null if all formats fail
 */
export async function ttsViaGenerativeLanguageAPI(
  text: string,
  voice: string,
  languageCode?: string,
  modelOverride?: string,
  apiKeyOverride?: string,
  apiKeyIndex?: number,
): Promise<TtsResult | null> {
  const apiKey = apiKeyOverride || process.env.GEMINI_API_KEY;
  if (!apiKey) return null;

  const keyInfo =
    apiKeyIndex !== undefined
      ? ` [API Key #${apiKeyIndex + 1}]`
      : apiKeyOverride
        ? ' [Rotated Key]'
        : '';

  const prompt = buildStylePrompt(text, voice, languageCode);
  const model = modelOverride || 'gemini-1.5-flash';
  const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;

  const tryMimes = [
    { mime: 'audio/mpeg', ext: 'mp3' as const },
    { mime: 'audio/wav', ext: 'wav' as const },
    { mime: 'audio/pcm;rate=24000', ext: 'pcm' as const },
  ];

  // Try each MIME type until one succeeds
  for (const opt of tryMimes) {
    const body = {
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: { response_mime_type: opt.mime },
    } as any;

    logger.log(
      `<><><> TTS Generative Language API request${keyInfo}=`,
      JSON.stringify({ ...body, contents: '[omitted]' }),
    );

    const resp = await fetch(`${endpoint}?key=${encodeURIComponent(apiKey)}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    if (!resp.ok) {
      const msg = await resp.text().catch(() => String(resp.status));
      logger.error(
        `[tts] Gemini TTS REST${keyInfo}`,
        opt.mime,
        resp.status,
        msg,
        apiKeyIndex !== undefined ? { apiKeyIndex: apiKeyIndex + 1 } : {},
      );
      continue;
    }

    const json = await resp.json();
    const parts = json?.candidates?.[0]?.content?.parts || [];
    const inline = parts.find(
      (p: any) => p?.inlineData?.data || p?.inline_data?.data,
    );
    const obj = inline?.inlineData || inline?.inline_data;
    const b64 = obj?.data as string | undefined;
    const returnedMime = obj?.mimeType as string | undefined;

    if (b64) {
      let bytes = Buffer.from(b64, 'base64');
      let contentType: TtsResult['contentType'] = 'audio/mpeg';
      let outExt: TtsResult['ext'] = 'mp3';

      // Handle different audio formats
      if (returnedMime?.includes('wav') || opt.ext === 'wav') {
        contentType = 'audio/wav';
        outExt = 'wav';
      } else if (returnedMime?.includes('pcm') || opt.ext === 'pcm') {
        bytes = wrapPcmToWav(bytes, 24000);
        contentType = 'audio/wav';
        outExt = 'wav';
      }

      // Calculate duration and convert to MP3 if needed
      let durationSec = 0;
      if (outExt === 'wav') {
        const sampleRate = returnedMime?.includes('pcm') ? 24000 : 16000;
        const bytesPerSample = 2;
        const dataLen = bytes.length - 44;
        durationSec = Math.max(
          1,
          Math.round(dataLen / (bytesPerSample * sampleRate)),
        );

        // Enforce MP3 output
        const mp3 = await wavToMp3(bytes).catch(() => null);
        if (mp3) {
          bytes = mp3;
          contentType = 'audio/mpeg';
          outExt = 'mp3';
        }
      } else {
        durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
      }

      return { audioBytes: bytes, durationSec, contentType, ext: outExt };
    }
  }

  logger.error(
    `[tts] Gemini TTS REST did not return inline audio${keyInfo}`,
    apiKeyIndex !== undefined ? { apiKeyIndex: apiKeyIndex + 1 } : {},
  );
  return null;
}
