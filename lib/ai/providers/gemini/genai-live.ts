/**
 * Google GenAI Live TTS (non-streaming, WebSocket-based)
 *
 * Uses the Google GenAI Live API via WebSocket to synthesize speech.
 * This provider connects to Gemini's live audio preview model and
 * collects all audio chunks before returning a complete WAV buffer.
 *
 * NOTE: This implementation has WebSocket bufferUtil/ws dependency issues
 * in some environments. Consider using GenAI SDK instead for better compatibility.
 */

import { GoogleGenAI, Modality, MediaResolution } from '@google/genai';
import { TtsResult } from '../../tts-types';
import { appConfig } from '../../../config';
import { logger } from '@/lib/logger';

/**
 * Synthesizes text to speech using Google GenAI Live API (non-streaming collection)
 *
 * @param text - The text to convert to speech
 * @param voice - Voice gender: 'male' or 'female'
 * @param languageCode - Optional language code (not currently used by this provider)
 * @param modelOverride - Optional model override (defaults to env var or 'models/gemini-2.5-flash-native-audio-preview-09-2025')
 * @param apiKeyOverride - Optional API key override for rotation support
 * @param apiKeyIndex - Optional API key index for logging rotation info
 * @returns Audio result with bytes, duration, and format info, or null if failed
 */
export async function ttsViaGenAiLive(
  text: string,
  voice: string,
  languageCode?: string,
  modelOverride?: string,
  apiKeyOverride?: string,
  apiKeyIndex?: number,
): Promise<TtsResult | null> {
  const apiKey = apiKeyOverride || process.env.GEMINI_API_KEY;
  if (!apiKey) return null;

  const keyInfo =
    apiKeyIndex !== undefined
      ? ` [API Key #${apiKeyIndex + 1}]`
      : apiKeyOverride
        ? ' [Rotated Key]'
        : '';

  // Prefer the sample's native audio preview model unless overridden
  const model =
    modelOverride ||
    process.env.GEMINI_LIVE_TTS_MODEL ||
    'models/gemini-2.5-flash-native-audio-preview-09-2025';

  // Workaround ws optional native deps causing runtime errors in some bundlers
  if (!process.env.WS_NO_BUFFER_UTIL) process.env.WS_NO_BUFFER_UTIL = '1';
  if (!process.env.WS_NO_UTF_8_VALIDATE) process.env.WS_NO_UTF_8_VALIDATE = '1';
  const ai = new GoogleGenAI({ apiKey });

  // Queue and helpers as per the provided sample
  type LiveServerMessage = any;
  const responseQueue: LiveServerMessage[] = [];
  async function waitMessage(): Promise<LiveServerMessage> {
    for (;;) {
      const m = responseQueue.shift();
      if (m) return m;
      await new Promise((r) => setTimeout(r, 100));
    }
  }
  async function handleTurn(): Promise<LiveServerMessage[]> {
    const turn: LiveServerMessage[] = [];
    let done = false;
    while (!done) {
      const message = await waitMessage();
      try {
        logger.debug('[genai-live] message', {
          hasServerContent: !!message?.serverContent,
          hasModelTurn: !!message?.serverContent?.modelTurn,
          turnComplete: !!message?.serverContent?.turnComplete,
          partsCount: message?.serverContent?.modelTurn?.parts?.length ?? 0,
        });
      } catch {}
      turn.push(message);
      if (message.serverContent && message.serverContent.turnComplete) {
        done = true;
      }
    }
    return turn;
  }

  // Collect inline audio parts and detect mime/sample rate
  const audioParts: string[] = [];
  let mimeType: string | undefined;
  function handleModelTurn(message: LiveServerMessage) {
    const parts = message?.serverContent?.modelTurn?.parts || [];
    if (!Array.isArray(parts) || parts.length === 0) return;
    for (const part of parts) {
      if (part?.fileData?.fileUri) {
        logger.debug('[genai-live] file', part.fileData.fileUri);
      }
      if (part?.inlineData) {
        const inline = part.inlineData;
        if (inline.mimeType && !mimeType) mimeType = inline.mimeType;
        audioParts.push(inline.data ?? '');
        try {
          logger.debug('[genai-live] inlineData', {
            mimeType: inline.mimeType,
            len: (inline.data || '').length,
            partsCollected: audioParts.length,
          });
        } catch {}
      }
      if (part?.text) {
        logger.debug('[genai-live] text', String(part.text).slice(0, 80));
      }
    }
  }

  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;
  const config: any = {
    responseModalities: [Modality.AUDIO],
    mediaResolution: MediaResolution.MEDIA_RESOLUTION_MEDIUM,
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
      },
    },
    contextWindowCompression: {
      triggerTokens: '25600',
      slidingWindow: { targetTokens: '12800' },
    },
  };
  try {
    logger.debug('[genai-live] config', {
      model,
      voice: prebuiltVoiceName,
      mediaResolution: 'MEDIUM',
    });
  } catch {}

  let session: any;
  try {
    session = await (ai as any).live.connect({
      model,
      callbacks: {
        onopen: () => logger.debug('[genai-live] Opened'),
        onmessage: (message: LiveServerMessage) => {
          // Push to queue and handle model turn (collect audio)
          try {
            handleModelTurn(message);
          } catch {}
          responseQueue.push(message);
        },
        onerror: (e: any) => {
          logger.debug('[genai-live] Error:', e?.message || e);
        },
        onclose: (e: any) => {
          logger.debug('[genai-live] Close:', e?.reason || '');
        },
      },
      config,
    });
  } catch (e) {
    logger.error(
      `[tts] GenAI Live connect failed${keyInfo}:`,
      (e as Error)?.message || e,
      apiKeyIndex !== undefined ? { apiKeyIndex: apiKeyIndex + 1 } : {},
    );
    return null;
  }

  try {
    logger.debug('[genai-live] send:turns', { chars: text.length });
    session.sendClientContent({ turns: [text] });
    // Important: signal end-of-turn so the model starts responding
    try {
      session.sendClientContent({ turnComplete: true });
      logger.debug('[genai-live] send:turnComplete');
    } catch (e) {
      logger.debug(
        '[genai-live] turnComplete error',
        (e as Error)?.message || e,
      );
    }

    logger.debug('[genai-live] waiting for first turn...');
    const turnMsgs = await handleTurn();
    logger.debug('[genai-live] first turn done', {
      messages: turnMsgs.length,
    });

    if (!audioParts.length) {
      logger.debug('[genai-live] no inline audio parts received');
      return null;
    }

    // Convert collected inline parts to a WAV buffer as per the sample
    type WavOptions = {
      numChannels: number;
      sampleRate: number;
      bitsPerSample: number;
    };
    function parseMimeType(mt: string): WavOptions {
      const [fileType, ...params] = String(mt)
        .split(';')
        .map((s) => s.trim());
      const [, format] = fileType.split('/');
      const options: Partial<WavOptions> = {
        numChannels: 1,
        bitsPerSample: 16,
      };
      if (format && format.startsWith('L')) {
        const bits = parseInt(format.slice(1), 10);
        if (!Number.isNaN(bits)) options.bitsPerSample = bits;
      }
      for (const param of params) {
        const [k, v] = param.split('=').map((s) => s.trim());
        if (k === 'rate') options.sampleRate = parseInt(v, 10);
      }
      if (!options.sampleRate) options.sampleRate = 24000;
      return options as WavOptions;
    }
    function createWavHeader(dataLength: number, opts: WavOptions) {
      const { numChannels, sampleRate, bitsPerSample } = opts;
      const byteRate = (sampleRate * numChannels * bitsPerSample) / 8;
      const blockAlign = (numChannels * bitsPerSample) / 8;
      const buffer = Buffer.alloc(44);
      buffer.write('RIFF', 0);
      buffer.writeUInt32LE(36 + dataLength, 4);
      buffer.write('WAVE', 8);
      buffer.write('fmt ', 12);
      buffer.writeUInt32LE(16, 16);
      buffer.writeUInt16LE(1, 20);
      buffer.writeUInt16LE(numChannels, 22);
      buffer.writeUInt32LE(sampleRate, 24);
      buffer.writeUInt32LE(byteRate, 28);
      buffer.writeUInt16LE(blockAlign, 32);
      buffer.writeUInt16LE(bitsPerSample, 34);
      buffer.write('data', 36);
      buffer.writeUInt32LE(dataLength, 40);
      return buffer;
    }

    const opts = parseMimeType(mimeType || 'audio/L16; rate=24000');
    const decoded = audioParts.map((b64) => Buffer.from(b64, 'base64'));
    const data = Buffer.concat(decoded);
    const header = createWavHeader(data.length, opts);
    const wav = Buffer.concat([header, data]);

    const bytesPerSample = opts.bitsPerSample / 8;
    const durationSec = Math.max(
      1,
      Math.round(
        data.length / (bytesPerSample * opts.numChannels * opts.sampleRate),
      ),
    );

    logger.debug('[genai-live] done', {
      mimeType: mimeType || 'unknown',
      parts: audioParts.length,
      bytes: wav.length,
      durationSec,
    });

    return {
      audioBytes: wav,
      durationSec,
      contentType: 'audio/wav',
      ext: 'wav',
    };
  } finally {
    try {
      session?.close();
    } catch {}
  }
}
