/**
 * Google GenAI Live TTS (streaming, WebSocket-based)
 *
 * Uses the Google GenAI Live API via WebSocket to synthesize speech and
 * stream audio chunks as they arrive. This enables real-time audio streaming
 * to clients with minimal latency.
 *
 * NOTE: This implementation has WebSocket bufferUtil/ws dependency issues
 * in some environments. Consider using streamTtsViaGenAiSdk instead for
 * better compatibility.
 */

import { GoogleGenAI, Modality, MediaResolution } from '@google/genai';
import { appConfig } from '../../../config';
import { logger } from '@/lib/logger';

/**
 * Streams text-to-speech audio chunks as they arrive from Gemini Live API
 *
 * @param text - The text to convert to speech
 * @param voice - Voice gender: 'male' or 'female'
 * @param languageCode - Optional language code (not currently used by this provider)
 * @yields Audio data chunks as Buffers
 * @throws Error if streaming fails or API key is missing
 */
export async function* streamTtsViaGenAiLive(
  text: string,
  voice: string,
  languageCode?: string,
): AsyncGenerator<Buffer, void, unknown> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    logger.error('[tts-stream] No GEMINI_API_KEY found');
    return;
  }

  const model =
    process.env.GEMINI_LIVE_TTS_MODEL ||
    'models/gemini-2.5-flash-native-audio-preview-09-2025';

  if (!process.env.WS_NO_BUFFER_UTIL) process.env.WS_NO_BUFFER_UTIL = '1';
  if (!process.env.WS_NO_UTF_8_VALIDATE) process.env.WS_NO_UTF_8_VALIDATE = '1';
  const ai = new GoogleGenAI({ apiKey });

  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;

  const config: any = {
    responseModalities: [Modality.AUDIO],
    mediaResolution: MediaResolution.MEDIA_RESOLUTION_MEDIUM,
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
      },
    },
    contextWindowCompression: {
      triggerTokens: '25600',
      slidingWindow: { targetTokens: '12800' },
    },
  };

  let session: any;
  try {
    logger.debug('[tts-stream] connecting...', {
      model,
      voice: prebuiltVoiceName,
    });

    // Use a promise-based approach to handle incoming messages
    const messageQueue: any[] = [];
    let resolveNext: ((msg: any) => void) | null = null;

    session = await (ai as any).live.connect({
      model,
      callbacks: {
        onopen: () => logger.debug('[tts-stream] opened'),
        onmessage: (message: any) => {
          // Check if this message contains audio data
          const parts = message?.serverContent?.modelTurn?.parts || [];
          const hasAudio = parts.some((p: any) => p?.inlineData);

          if (hasAudio) {
            if (resolveNext) {
              resolveNext(message);
              resolveNext = null;
            } else {
              messageQueue.push(message);
            }
          }

          // Also push turn complete messages
          if (message?.serverContent?.turnComplete) {
            if (resolveNext) {
              resolveNext(message);
              resolveNext = null;
            } else {
              messageQueue.push(message);
            }
          }
        },
        onerror: (e: any) => {
          logger.error('[tts-stream] error:', e?.message || e);
        },
        onclose: (e: any) => {
          logger.debug('[tts-stream] closed:', e?.reason || '');
        },
      },
      config,
    });

    // Send the text to be synthesized
    logger.debug('[tts-stream] sending text', { chars: text.length });
    session.sendClientContent({ turns: [text] });
    session.sendClientContent({ turnComplete: true });

    // Stream audio chunks as they arrive
    let turnComplete = false;
    let chunkCount = 0;

    while (!turnComplete) {
      // Wait for next message
      const message = await new Promise<any>((resolve) => {
        if (messageQueue.length > 0) {
          resolve(messageQueue.shift());
        } else {
          resolveNext = resolve;
        }
      });

      // Check for turn complete
      if (message?.serverContent?.turnComplete) {
        logger.debug('[tts-stream] turn complete');
        turnComplete = true;
        break;
      }

      // Extract and yield audio chunks
      const parts = message?.serverContent?.modelTurn?.parts || [];
      for (const part of parts) {
        if (part?.inlineData) {
          const b64 = part.inlineData.data;
          if (b64) {
            const audioChunk = Buffer.from(b64, 'base64');
            chunkCount++;
            logger.debug('[tts-stream] yielding chunk', {
              chunk: chunkCount,
              bytes: audioChunk.length,
            });
            yield audioChunk;
          }
        }
      }
    }

    logger.debug('[tts-stream] complete', { totalChunks: chunkCount });
  } catch (e) {
    logger.error('[tts-stream] failed:', (e as Error)?.message || e);
    throw e;
  } finally {
    try {
      session?.close();
    } catch {}
  }
}
