import { TtsResult } from '../tts-types';
import { buildStylePrompt, wordsPerSecondEstimate } from '../voice-style';
import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

// OpenAI Audio Speech API (ChatGPT TTS)
export async function ttsViaOpenAIAudioSpeech(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) return null;

  // Model and output format
  const model = process.env.OPENAI_TTS_MODEL || 'gpt-4o-mini-tts';
  const formatEnv = (process.env.OPENAI_TTS_FORMAT || 'mp3').toLowerCase();
  const format: 'mp3' | 'wav' = formatEnv === 'wav' ? 'wav' : 'mp3';

  // Voice mapping — allow explicit override, else use config defaults for male/female
  const explicitVoice = process.env.OPENAI_TTS_VOICE;
  const mappedVoice =
    voice === 'male'
      ? appConfig.openaiTtsVoiceNameMale || 'onyx'
      : appConfig.openaiTtsVoiceNameFemale || 'nova';
  const voiceName = explicitVoice || mappedVoice;

  // const style = buildStylePrompt(text, voice, languageCode);

  const body = {
    model,
    input: text,
    voice: voiceName,
    format,
  } as const;

  logger.log('[TTS] GPT req body=', JSON.stringify(body));

  const r = await fetch('https://api.openai.com/v1/audio/speech', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });

  logger.log('[TTS] GPT res=', JSON.stringify(r));

  if (!r.ok) {
    const msg = await r.text().catch(() => String(r.status));
    logger.error('[tts] OpenAI audio/speech failed', r.status, msg);
    return null;
  }

  const arrbuf = await r.arrayBuffer();
  const bytes = Buffer.from(arrbuf);
  const contentType: TtsResult['contentType'] =
    format === 'wav' ? 'audio/wav' : 'audio/mpeg';
  const ext: TtsResult['ext'] = format === 'wav' ? 'wav' : 'mp3';
  const durationSec = wordsPerSecondEstimate(text);
  return { audioBytes: bytes, durationSec, contentType, ext };
}
