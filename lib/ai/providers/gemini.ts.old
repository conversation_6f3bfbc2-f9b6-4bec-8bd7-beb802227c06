import { createSign } from 'crypto';
import fs from 'fs';
import { GoogleGenAI, Modality, MediaResolution } from '@google/genai';
import { TtsResult } from '../tts-types';
import { appConfig } from '../../config';
import { wrapPcmToWav, wavToMp3 } from '../audio-utils';
import { buildStylePrompt } from '../voice-style';

type ServiceAccount = {
  client_email: string;
  private_key: string;
  project_id?: string;
};

function base64url(input: Buffer | string) {
  const b64 = (typeof input === 'string' ? Buffer.from(input) : input).toString(
    'base64',
  );
  return b64.replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
}

function readServiceAccountFromEnv(): ServiceAccount | null {
  const candidates = [
    process.env.GCP_TTS_SERVICE_ACCOUNT_JSON,
    process.env.GOOGLE_SERVICE_ACCOUNT_JSON,
  ].filter(Boolean) as string[];
  let jsonStr: string | null = null;
  for (const c of candidates) {
    if (!c) continue;
    if (c.trim().startsWith('{')) {
      jsonStr = c;
      break;
    }
    try {
      if (fs.existsSync(c)) {
        jsonStr = fs.readFileSync(c, 'utf8');
        break;
      }
    } catch {
      /* ignore */
    }
  }
  if (!jsonStr) {
    const credPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credPath) {
      try {
        jsonStr = fs.readFileSync(credPath, 'utf8');
      } catch {
        /* ignore */
      }
    }
  }
  if (!jsonStr) return null;
  try {
    const obj = JSON.parse(jsonStr);
    if (obj && obj.client_email && obj.private_key)
      return obj as ServiceAccount;
  } catch {
    /* ignore */
  }
  return null;
}

function getServiceAccountProjectId(): string | undefined {
  const sa = readServiceAccountFromEnv();
  return sa?.project_id;
}

export async function getGoogleAccessToken(opts: {
  explicitToken?: string | undefined;
  scopes: string[] | string;
}): Promise<string | null> {
  if (opts.explicitToken) return opts.explicitToken;
  const sa = readServiceAccountFromEnv();
  if (!sa) return null;
  const now = Math.floor(Date.now() / 1000);
  const header = { alg: 'RS256', typ: 'JWT' };
  const scope = Array.isArray(opts.scopes)
    ? opts.scopes.join(' ')
    : opts.scopes;
  const payload = {
    iss: sa.client_email,
    scope,
    aud: 'https://oauth2.googleapis.com/token',
    iat: now,
    exp: now + 3600,
  } as const;
  const unsigned = `${base64url(JSON.stringify(header))}.${base64url(JSON.stringify(payload))}`;
  const signer = createSign('RSA-SHA256');
  signer.update(unsigned);
  const sig = signer.sign(sa.private_key);
  const assertion = `${unsigned}.${base64url(sig)}`;
  const body = new URLSearchParams({
    grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
    assertion,
  });
  const res = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body,
  });
  if (!res.ok) {
    console.error(
      '[tts] SA token exchange failed',
      res.status,
      await res.text().catch(() => ''),
    );
    return null;
  }
  const j = await res.json();
  return (j.access_token as string) || null;
}

export async function ttsViaGenerativeLanguageAPI(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) return null;

  const prompt = buildStylePrompt(text, voice, languageCode);
  const endpoint =
    'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';
  const tryMimes = [
    { mime: 'audio/mpeg', ext: 'mp3' as const },
    { mime: 'audio/wav', ext: 'wav' as const },
    { mime: 'audio/pcm;rate=24000', ext: 'pcm' as const },
  ];
  for (const opt of tryMimes) {
    const body = {
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: { response_mime_type: opt.mime },
    } as any;

    console.log(
      '<><><> TTS Generative Language API request=',
      JSON.stringify({ ...body, contents: '[omitted]' }),
    );
    const resp = await fetch(`${endpoint}?key=${encodeURIComponent(apiKey)}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });
    if (!resp.ok) {
      const msg = await resp.text().catch(() => String(resp.status));
      console.error('[tts] Gemini TTS REST', opt.mime, resp.status, msg);
      continue;
    }
    const json = await resp.json();
    const parts = json?.candidates?.[0]?.content?.parts || [];
    const inline = parts.find(
      (p: any) => p?.inlineData?.data || p?.inline_data?.data,
    );
    const obj = inline?.inlineData || inline?.inline_data;
    const b64 = obj?.data as string | undefined;
    const returnedMime = obj?.mimeType as string | undefined;
    if (b64) {
      let bytes = Buffer.from(b64, 'base64');
      let contentType: TtsResult['contentType'] = 'audio/mpeg';
      let outExt: TtsResult['ext'] = 'mp3';
      if (returnedMime?.includes('wav') || opt.ext === 'wav') {
        contentType = 'audio/wav';
        outExt = 'wav';
      } else if (returnedMime?.includes('pcm') || opt.ext === 'pcm') {
        bytes = wrapPcmToWav(bytes, 24000);
        contentType = 'audio/wav';
        outExt = 'wav';
      }
      let durationSec = 0;
      if (outExt === 'wav') {
        const sampleRate = returnedMime?.includes('pcm') ? 24000 : 16000;
        const bytesPerSample = 2;
        const dataLen = bytes.length - 44;
        durationSec = Math.max(
          1,
          Math.round(dataLen / (bytesPerSample * sampleRate)),
        );
        // Enforce MP3 output
        const mp3 = await wavToMp3(bytes).catch(() => null);
        if (mp3) {
          bytes = mp3;
          contentType = 'audio/mpeg';
          outExt = 'mp3';
        }
      } else {
        durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
      }
      return { audioBytes: bytes, durationSec, contentType, ext: outExt };
    }
  }
  console.error('[tts] Gemini TTS REST did not return inline audio');
  return null;
}

function isClassicGcpVoiceName(name: string): boolean {
  // Rough validation for classic Cloud TTS voices, e.g., en-US-Neural2-A, vi-VN-Standard-B
  return /^[a-z]{2,3}-[A-Z]{2}(?:-[A-Za-z0-9]+){1,3}$/.test(name);
}

export async function ttsViaGcpGeminiOAuth(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  // This path uses Google Cloud Text-to-Speech v1 with OAuth (no prompt support).
  const token = await getGoogleAccessToken({
    explicitToken: process.env.GCP_TTS_OAUTH_TOKEN,
    scopes: ['https://www.googleapis.com/auth/cloud-platform'],
  });
  const projectId =
    process.env.GCP_TTS_PROJECT_ID || getServiceAccountProjectId();
  if (!token || !projectId) return null;

  const encodingEnv = (appConfig.gcpTtsAudioEncoding || 'MP3').toUpperCase();
  const preferredEncoding = ['MP3', 'LINEAR16', 'OGG_OPUS'].includes(
    encodingEnv,
  )
    ? encodingEnv
    : 'MP3';
  const lang = languageCode || 'en-US';
  let name = appConfig.gcpTtsVoiceName; // Optional: e.g. en-US-Neural2-A
  if (name && !isClassicGcpVoiceName(name)) {
    console.warn(
      '[tts] GCP_TTS_VOICE_NAME appears non-classic, ignoring for v1 TTS:',
      name,
    );
    name = undefined;
  }

  // Build a light SSML wrapper to encourage pacing; classic TTS does not accept free-form prompts
  function escapeSsml(s: string) {
    return s.replace(
      /[&<>]/g,
      (c) => (({ '&': '&amp;', '<': '&lt;', '>': '&gt;' }) as any)[c],
    );
  }
  const parts = text.split(/([.!?…])\s+/).filter(Boolean);
  const ssmlWrapper = (inner: string) =>
    `<speak><prosody rate="medium" pitch="0st">${inner.trim()}</prosody></speak>`;

  // Make atomic units and chunk to <=5000 bytes per request
  const units: string[] = [];
  for (let i = 0; i < parts.length; i++) {
    const t = parts[i];
    let unit = escapeSsml(t);
    if (/^[.!?…]$/.test(t) || /[.!?…]$/.test(t))
      unit += '<break time="200ms"/>';
    unit += ' ';
    units.push(unit);
  }
  const MAX_BYTES = 5000;
  const chunks: string[] = [];
  let cur: string[] = [];
  for (const u of units) {
    const candidate = ssmlWrapper(cur.join('') + u);
    if (Buffer.byteLength(candidate, 'utf8') <= MAX_BYTES) {
      cur.push(u);
    } else {
      if (cur.length) chunks.push(ssmlWrapper(cur.join('')));
      cur = [u];
    }
  }
  if (cur.length) chunks.push(ssmlWrapper(cur.join('')));

  const willChunk =
    chunks.length > 1 ||
    (chunks[0] && Buffer.byteLength(chunks[0], 'utf8') > MAX_BYTES);
  const requestEncoding = willChunk ? 'LINEAR16' : preferredEncoding;
  const sampleRateHertz = 24000;

  async function synthesizeOne(ssml: string): Promise<Buffer | null> {
    const body: any = {
      input: { ssml },
      voice: name
        ? { languageCode: lang, name }
        : {
            languageCode: lang,
            ssmlGender: voice === 'male' ? 'MALE' : 'FEMALE',
          },
      audioConfig: {
        audioEncoding: requestEncoding,
        ...(requestEncoding === 'LINEAR16' ? { sampleRateHertz } : {}),
      },
    };
    console.log(
      '<><><> TTS GCP v1 request (OAuth)=',
      JSON.stringify({
        ...body,
        input: { ssml: `[${Buffer.byteLength(ssml, 'utf8')} bytes SSML]` },
      }),
    );
    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
    if (projectId) headers['x-goog-user-project'] = projectId;
    const r = await fetch(
      'https://texttospeech.googleapis.com/v1/text:synthesize',
      {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      },
    );
    if (!r.ok) {
      console.error(
        '[tts] GCP TTS v1 (OAuth) failed',
        r.status,
        await r.text().catch(() => ''),
      );
      return null;
    }
    const j = await r.json();
    const b64 = j?.audioContent as string | undefined;
    return b64 ? Buffer.from(b64, 'base64') : null;
  }

  if (!willChunk) {
    const singleSsml = chunks.length ? chunks[0] : ssmlWrapper(units.join(''));
    const bytes = await synthesizeOne(singleSsml);
    if (!bytes) return null;
    let contentType: TtsResult['contentType'] =
      requestEncoding === 'MP3' ? 'audio/mpeg' : 'audio/wav';
    let ext: TtsResult['ext'] = requestEncoding === 'MP3' ? 'mp3' : 'wav';
    let outBytes = bytes;
    if (requestEncoding === 'LINEAR16')
      outBytes = wrapPcmToWav(bytes, sampleRateHertz);
    const durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
    return { audioBytes: outBytes, durationSec, contentType, ext };
  }

  // Chunked: concatenate PCM and wrap, then convert if MP3 preferred
  const pcmChunks: Buffer[] = [];
  for (const ssml of chunks) {
    const buf = await synthesizeOne(ssml);
    if (!buf) return null;
    const isWav =
      buf.length >= 44 &&
      buf.toString('ascii', 0, 4) === 'RIFF' &&
      buf.toString('ascii', 8, 12) === 'WAVE';
    pcmChunks.push(isWav ? buf.slice(44) : buf);
  }
  const pcmAll = Buffer.concat(pcmChunks);
  const wavAll = wrapPcmToWav(pcmAll, sampleRateHertz);
  let audioBytes = wavAll;
  let contentType: TtsResult['contentType'] = 'audio/wav';
  let ext: TtsResult['ext'] = 'wav';
  if (preferredEncoding === 'MP3') {
    const mp3 = await wavToMp3(wavAll).catch(() => null);
    if (mp3) {
      audioBytes = mp3;
      contentType = 'audio/mpeg';
      ext = 'mp3';
    }
  }
  const bytesPerSample = 2;
  const durationSec = Math.max(
    1,
    Math.round(pcmAll.length / (bytesPerSample * sampleRateHertz)),
  );
  return { audioBytes, durationSec, contentType, ext };
}

export async function ttsViaClassicGcpApiKey(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const gcpKey = process.env.GCP_TTS_API_KEY;
  if (!gcpKey) return null;
  function escapeSsml(s: string) {
    return s.replace(
      /[&<>]/g,
      (c) => (({ '&': '&amp;', '<': '&lt;', '>': '&gt;' }) as any)[c],
    );
  }
  const parts = text.split(/([.!?…])\s+/).filter(Boolean);
  let ssmlContent = '';
  for (let i = 0; i < parts.length; i++) {
    const t = parts[i];
    ssmlContent += escapeSsml(t);
    if (/^[.!?…]$/.test(t) || /[.!?…]$/.test(t))
      ssmlContent += '<break time="200ms"/>';
    ssmlContent += ' ';
  }
  const ssml = `<speak><prosody rate="medium" pitch="0st">${ssmlContent.trim()}</prosody></speak>`;
  const body = {
    input: { ssml },
    voice: {
      languageCode: languageCode || 'en-US',
      ssmlGender: voice === 'male' ? 'MALE' : 'FEMALE',
    },
    audioConfig: { audioEncoding: 'MP3', speakingRate: 1.0, pitch: 0.0 },
  } as const;

  console.log('<><><> TTS Classic GCP request=', JSON.stringify(body));
  const r = await fetch(
    `https://texttospeech.googleapis.com/v1/text:synthesize?key=${encodeURIComponent(gcpKey)}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    },
  );
  if (!r.ok) {
    console.error(
      '[tts] GCP TTS request failed',
      r.status,
      await r.text().catch(() => ''),
    );
    return null;
  }
  const j = await r.json();
  const b64 = j.audioContent as string | undefined;
  if (!b64) return null;
  const bytes = Buffer.from(b64, 'base64');
  const durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
  return {
    audioBytes: bytes,
    durationSec,
    contentType: 'audio/mpeg',
    ext: 'mp3',
  };
}

// Google GenAI SDK streaming TTS (per Google sample)
export async function ttsViaGenAiSdk(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) return null;

  const model = process.env.GEMINI_TTS_MODEL || 'gemini-2.5-pro-preview-tts';
  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;
  const genai = new GoogleGenAI({ apiKey });

  // Build style instructions + actual text as separate parts
  const style = buildStylePrompt(text, voice, languageCode);
  const contents = [
    {
      role: 'user',
      parts: [
        // { text: style },
        { text },
      ],
    },
  ];

  const config: any = {
    temperature: 1,
    responseModalities: ['audio'],
    speechConfig: {},
    generationConfig: { responseMimeType: 'audio/mpeg' },
  };
  if (prebuiltVoiceName) {
    config.speechConfig.voiceConfig = {
      prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
    };
  }

  console.log(
    '<><><> TTS GenAI SDK request=',
    JSON.stringify({ model, config, contents }),
  );

  // Stream audio chunks and stitch together
  let gen: any;
  try {
    gen = await genai.models.generateContentStream({ model, config, contents });
  } catch (e) {
    console.error('[tts] generateContentStream failed:', {
      error: (e as Error)?.message || e,
      model,
      voice,
      languageCode,
      textLength: text.length,
      textPreview: text.substring(0, 100) + '...',
    });
    return null;
  }

  console.log('<><><> TTS GenAI SDK gen=', gen);

  const chunks: Buffer[] = [];
  let detectedMime: string | undefined;
  let fmt: 'pcm' | 'wav' | 'mp3' | null = null;
  let sampleRate = 24000;

  try {
    for await (const chunk of (gen as any).stream ?? gen) {
      const parts = chunk?.candidates?.[0]?.content?.parts as any[] | undefined;
      if (!parts || !parts.length) continue;
      const inline = parts.find((p) => p?.inlineData?.data);
      if (!inline) continue;
      const inlineData = inline.inlineData;
      const m = inlineData?.mimeType as string | undefined;
      if (m && !detectedMime) {
        detectedMime = m;
        const lm = m.toLowerCase();
        if (lm.includes('pcm')) fmt = 'pcm';
        else if (lm.includes('wav')) fmt = 'wav';
        else if (lm.includes('mpeg') || lm.includes('mp3')) fmt = 'mp3';
        // try parse sample rate if provided like audio/pcm;rate=24000
        const rateMatch = lm.match(/rate\s*=\s*(\d{3,6})/);
        if (rateMatch)
          sampleRate = Math.max(
            8000,
            Math.min(48000, parseInt(rateMatch[1], 10) || 24000),
          );
      }
      const b64 = inlineData?.data as string | undefined;
      if (!b64) continue;
      let bytes = Buffer.from(b64, 'base64');
      if (fmt === 'wav') {
        // If multiple WAV chunks arrive, strip header from subsequent chunks and keep data only
        if (bytes.length > 44) bytes = bytes.slice(44);
      }
      chunks.push(bytes);
    }
  } catch (e) {
    console.error('[tts] error while reading GenAI stream:', {
      error: (e as Error)?.message || e,
      model,
      voice,
      languageCode,
      textLength: text.length,
      chunksReceived: chunks.length,
      detectedMime,
    });
    return null;
  }

  if (!chunks.length) {
    console.error('[tts] no audio chunks received from GenAI stream', {
      model,
      voice,
      languageCode,
      textLength: text.length,
      detectedMime,
    });
    return null;
  }

  // Decide output format and wrap/convert as needed
  let audioBytes: Buffer;
  let contentType: TtsResult['contentType'];
  let ext: TtsResult['ext'];
  if (detectedMime?.includes('mp3') || fmt === 'mp3') {
    audioBytes = Buffer.concat(chunks);
    contentType = 'audio/mpeg';
    ext = 'mp3';
  } else if (detectedMime?.includes('wav') || fmt === 'wav') {
    // Prepend proper WAV header around concatenated PCM data
    const dataBytes = Buffer.concat(chunks);
    const wav = wrapPcmToWav(dataBytes, sampleRate);
    audioBytes = wav;
    contentType = 'audio/wav';
    ext = 'wav';
  } else {
    // Default to PCM wrapped to WAV if unknown or PCM
    const dataBytes = Buffer.concat(chunks);
    const wavBytes = wrapPcmToWav(dataBytes, sampleRate);
    // Convert WAV to MP3 if possible
    const mp3 = await wavToMp3(wavBytes).catch(() => null);
    if (mp3) {
      audioBytes = mp3;
      contentType = 'audio/mpeg';
      ext = 'mp3';
    } else {
      audioBytes = wavBytes;
      contentType = 'audio/wav';
      ext = 'wav';
    }
  }

  // Estimate duration
  let durationSec = 0;
  if (ext === 'wav') {
    const dataLen = Math.max(0, audioBytes.length - 44);
    const bytesPerSample = 2; // 16-bit PCM
    durationSec = Math.max(
      1,
      Math.round(dataLen / (bytesPerSample * sampleRate)),
    );
  } else {
    durationSec = Math.max(1, Math.round(text.split(/\s+/).length / 2.5));
  }

  console.log('[tts] GenAI SDK success', {
    model,
    voice,
    languageCode,
    textLength: text.length,
    audioBytes: audioBytes.length,
    durationSec,
    format: ext,
    chunksReceived: chunks.length,
  });

  return { audioBytes, durationSec, contentType, ext };
}

// Streaming version using GenAI SDK (HTTP streaming, no WebSocket issues)
export async function* streamTtsViaGenAiSdk(
  text: string,
  voice: string,
  languageCode?: string,
): AsyncGenerator<Buffer, void, unknown> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    const err = new Error('No GEMINI_API_KEY found');
    console.error('[tts-stream]', err.message);
    throw err;
  }

  const model = process.env.GEMINI_TTS_MODEL || 'gemini-2.5-pro-preview-tts';
  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;

  if (!prebuiltVoiceName) {
    const err = new Error(`No voice name configured for ${voice}`);
    console.error('[tts-stream]', err.message);
    throw err;
  }

  const genai = new GoogleGenAI({ apiKey });

  const contents = [
    {
      role: 'user',
      parts: [{ text }],
    },
  ];

  const config: any = {
    temperature: 1,
    responseModalities: ['audio'],
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
      },
    },
    generationConfig: { responseMimeType: 'audio/mpeg' },
  };

  console.log('[tts-stream] Starting stream', {
    model,
    voice: prebuiltVoiceName,
    textLength: text.length,
    textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
  });

  let gen: any;
  try {
    console.log('[tts-stream] Calling generateContentStream with config:', {
      model,
      configKeys: Object.keys(config),
      hasApiKey: !!apiKey,
      apiKeyPrefix: apiKey.substring(0, 10) + '...',
    });

    // IMPORTANT: generateContentStream is NOT officially documented for TTS models
    // According to ai.google.dev/gemini-api/docs/speech-generation, only non-streaming
    // generateContent is supported. We'll try it anyway with a timeout.

    // Add timeout to stream creation (10 seconds)
    const streamCreationTimeout = 10000;
    const streamPromise = genai.models.generateContentStream({
      model,
      config,
      contents,
    });

    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(
        () =>
          reject(
            new Error(
              'Stream creation timeout - generateContentStream may not be supported for TTS models',
            ),
          ),
        streamCreationTimeout,
      ),
    );

    gen = await Promise.race([streamPromise, timeoutPromise]);

    console.log('[tts-stream] Stream created successfully', {
      genType: typeof gen,
      hasStream: !!(gen as any).stream,
      genKeys: Object.keys(gen || {}),
    });
  } catch (e) {
    console.error('[tts-stream] generateContentStream failed:', {
      error: (e as Error)?.message || e,
      stack: (e as Error)?.stack,
      model,
      voice: prebuiltVoiceName,
      textLength: text.length,
      note: 'generateContentStream may not be supported for TTS models - only non-streaming generateContent is documented',
    });
    throw new Error(`Failed to create stream: ${(e as Error)?.message || e}`);
  }

  let chunkCount = 0;
  let totalBytes = 0;
  let firstChunkReceived = false;

  try {
    // Access the stream properly - check both .stream and direct iteration
    const iterator = gen.stream || gen;

    console.log('[tts-stream] Starting to iterate stream', {
      hasStream: !!gen.stream,
      iteratorType: typeof iterator,
    });

    for await (const chunk of iterator) {
      if (!firstChunkReceived) {
        console.log('[tts-stream] First chunk received', {
          chunkType: typeof chunk,
          hasCandidate: !!chunk?.candidates,
          candidatesLength: chunk?.candidates?.length,
        });
        firstChunkReceived = true;
      }

      const parts = chunk?.candidates?.[0]?.content?.parts as any[] | undefined;
      if (!parts || !parts.length) {
        console.log('[tts-stream] Chunk has no parts, skipping');
        continue;
      }

      const inline = parts.find((p) => p?.inlineData?.data);
      if (!inline) {
        console.log('[tts-stream] No inline data in chunk, skipping');
        continue;
      }

      const b64 = inline.inlineData?.data as string | undefined;
      if (!b64) {
        console.log('[tts-stream] No base64 data in inline, skipping');
        continue;
      }

      let bytes = Buffer.from(b64, 'base64');

      // If it's WAV, strip header from subsequent chunks
      const mimeType = inline.inlineData?.mimeType || '';
      if (chunkCount > 0 && mimeType.includes('wav') && bytes.length > 44) {
        bytes = bytes.slice(44);
      }

      chunkCount++;
      totalBytes += bytes.length;

      console.log('[tts-stream] Chunk yielded', {
        chunk: chunkCount,
        bytes: bytes.length,
        totalBytes,
        mimeType,
      });

      yield bytes;
    }

    if (chunkCount === 0) {
      throw new Error('Stream completed but no audio chunks were received');
    }

    console.log('[tts-stream] Stream complete', {
      totalChunks: chunkCount,
      totalBytes,
    });
  } catch (e) {
    console.error('[tts-stream] Error reading stream:', {
      error: (e as Error)?.message || e,
      stack: (e as Error)?.stack,
      chunksReceived: chunkCount,
      totalBytes,
      firstChunkReceived,
    });
    throw new Error(
      `Stream error after ${chunkCount} chunks: ${(e as Error)?.message || e}`,
    );
  }
}

// Streaming version: yields audio chunks as they arrive from Gemini Live API
// NOTE: This has WebSocket bufferUtil issues, use streamTtsViaGenAiSdk instead
export async function* streamTtsViaGenAiLive(
  text: string,
  voice: string,
  languageCode?: string,
): AsyncGenerator<Buffer, void, unknown> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error('[tts-stream] No GEMINI_API_KEY found');
    return;
  }

  const model =
    process.env.GEMINI_LIVE_TTS_MODEL ||
    'models/gemini-2.5-flash-native-audio-preview-09-2025';

  if (!process.env.WS_NO_BUFFER_UTIL) process.env.WS_NO_BUFFER_UTIL = '1';
  if (!process.env.WS_NO_UTF_8_VALIDATE) process.env.WS_NO_UTF_8_VALIDATE = '1';
  const ai = new GoogleGenAI({ apiKey });

  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;

  const config: any = {
    responseModalities: [Modality.AUDIO],
    mediaResolution: MediaResolution.MEDIA_RESOLUTION_MEDIUM,
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
      },
    },
    contextWindowCompression: {
      triggerTokens: '25600',
      slidingWindow: { targetTokens: '12800' },
    },
  };

  let session: any;
  try {
    console.debug('[tts-stream] connecting...', {
      model,
      voice: prebuiltVoiceName,
    });

    // Use a promise-based approach to handle incoming messages
    const messageQueue: any[] = [];
    let resolveNext: ((msg: any) => void) | null = null;

    session = await (ai as any).live.connect({
      model,
      callbacks: {
        onopen: () => console.debug('[tts-stream] opened'),
        onmessage: (message: any) => {
          // Check if this message contains audio data
          const parts = message?.serverContent?.modelTurn?.parts || [];
          const hasAudio = parts.some((p: any) => p?.inlineData);

          if (hasAudio) {
            if (resolveNext) {
              resolveNext(message);
              resolveNext = null;
            } else {
              messageQueue.push(message);
            }
          }

          // Also push turn complete messages
          if (message?.serverContent?.turnComplete) {
            if (resolveNext) {
              resolveNext(message);
              resolveNext = null;
            } else {
              messageQueue.push(message);
            }
          }
        },
        onerror: (e: any) => {
          console.error('[tts-stream] error:', e?.message || e);
        },
        onclose: (e: any) => {
          console.debug('[tts-stream] closed:', e?.reason || '');
        },
      },
      config,
    });

    // Send the text to be synthesized
    console.debug('[tts-stream] sending text', { chars: text.length });
    session.sendClientContent({ turns: [text] });
    session.sendClientContent({ turnComplete: true });

    // Stream audio chunks as they arrive
    let turnComplete = false;
    let chunkCount = 0;

    while (!turnComplete) {
      // Wait for next message
      const message = await new Promise<any>((resolve) => {
        if (messageQueue.length > 0) {
          resolve(messageQueue.shift());
        } else {
          resolveNext = resolve;
        }
      });

      // Check for turn complete
      if (message?.serverContent?.turnComplete) {
        console.debug('[tts-stream] turn complete');
        turnComplete = true;
        break;
      }

      // Extract and yield audio chunks
      const parts = message?.serverContent?.modelTurn?.parts || [];
      for (const part of parts) {
        if (part?.inlineData) {
          const b64 = part.inlineData.data;
          if (b64) {
            const audioChunk = Buffer.from(b64, 'base64');
            chunkCount++;
            console.debug('[tts-stream] yielding chunk', {
              chunk: chunkCount,
              bytes: audioChunk.length,
            });
            yield audioChunk;
          }
        }
      }
    }

    console.debug('[tts-stream] complete', { totalChunks: chunkCount });
  } catch (e) {
    console.error('[tts-stream] failed:', (e as Error)?.message || e);
    throw e;
  } finally {
    try {
      session?.close();
    } catch {}
  }
}

// GenAI Live TTS via WebSocket-like session (non-streaming version)
export async function ttsViaGenAiLive(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) return null;

  // Prefer the sample's native audio preview model unless overridden
  const model =
    process.env.GEMINI_LIVE_TTS_MODEL ||
    'models/gemini-2.5-flash-native-audio-preview-09-2025';

  // Workaround ws optional native deps causing runtime errors in some bundlers
  if (!process.env.WS_NO_BUFFER_UTIL) process.env.WS_NO_BUFFER_UTIL = '1';
  if (!process.env.WS_NO_UTF_8_VALIDATE) process.env.WS_NO_UTF_8_VALIDATE = '1';
  const ai = new GoogleGenAI({ apiKey });

  // Queue and helpers as per the provided sample
  type LiveServerMessage = any;
  const responseQueue: LiveServerMessage[] = [];
  async function waitMessage(): Promise<LiveServerMessage> {
    for (;;) {
      const m = responseQueue.shift();
      if (m) return m;
      await new Promise((r) => setTimeout(r, 100));
    }
  }
  async function handleTurn(): Promise<LiveServerMessage[]> {
    const turn: LiveServerMessage[] = [];
    let done = false;
    while (!done) {
      const message = await waitMessage();
      try {
        console.debug('[genai-live] message', {
          hasServerContent: !!message?.serverContent,
          hasModelTurn: !!message?.serverContent?.modelTurn,
          turnComplete: !!message?.serverContent?.turnComplete,
          partsCount: message?.serverContent?.modelTurn?.parts?.length ?? 0,
        });
      } catch {}
      turn.push(message);
      if (message.serverContent && message.serverContent.turnComplete) {
        done = true;
      }
    }
    return turn;
  }

  // Collect inline audio parts and detect mime/sample rate
  const audioParts: string[] = [];
  let mimeType: string | undefined;
  function handleModelTurn(message: LiveServerMessage) {
    const parts = message?.serverContent?.modelTurn?.parts || [];
    if (!Array.isArray(parts) || parts.length === 0) return;
    for (const part of parts) {
      if (part?.fileData?.fileUri) {
        console.debug('[genai-live] file', part.fileData.fileUri);
      }
      if (part?.inlineData) {
        const inline = part.inlineData;
        if (inline.mimeType && !mimeType) mimeType = inline.mimeType;
        audioParts.push(inline.data ?? '');
        try {
          console.debug('[genai-live] inlineData', {
            mimeType: inline.mimeType,
            len: (inline.data || '').length,
            partsCollected: audioParts.length,
          });
        } catch {}
      }
      if (part?.text) {
        console.debug('[genai-live] text', String(part.text).slice(0, 80));
      }
    }
  }

  const prebuiltVoiceName =
    voice === 'male'
      ? appConfig.geminiTtsVoiceNameMale
      : appConfig.geminiTtsVoiceNameFemale;
  const config: any = {
    responseModalities: [Modality.AUDIO],
    mediaResolution: MediaResolution.MEDIA_RESOLUTION_MEDIUM,
    speechConfig: {
      voiceConfig: {
        prebuiltVoiceConfig: { voiceName: prebuiltVoiceName },
      },
    },
    contextWindowCompression: {
      triggerTokens: '25600',
      slidingWindow: { targetTokens: '12800' },
    },
  };
  try {
    console.debug('[genai-live] config', {
      model,
      voice: prebuiltVoiceName,
      mediaResolution: 'MEDIUM',
    });
  } catch {}

  let session: any;
  try {
    session = await (ai as any).live.connect({
      model,
      callbacks: {
        onopen: () => console.debug('[genai-live] Opened'),
        onmessage: (message: LiveServerMessage) => {
          // Push to queue and handle model turn (collect audio)
          try {
            handleModelTurn(message);
          } catch {}
          responseQueue.push(message);
        },
        onerror: (e: any) => {
          console.debug('[genai-live] Error:', e?.message || e);
        },
        onclose: (e: any) => {
          console.debug('[genai-live] Close:', e?.reason || '');
        },
      },
      config,
    });
  } catch (e) {
    console.error(
      '[tts] GenAI Live connect failed:',
      (e as Error)?.message || e,
    );
    return null;
  }

  try {
    console.debug('[genai-live] send:turns', { chars: text.length });
    session.sendClientContent({ turns: [text] });
    // Important: signal end-of-turn so the model starts responding
    try {
      session.sendClientContent({ turnComplete: true });
      console.debug('[genai-live] send:turnComplete');
    } catch (e) {
      console.debug(
        '[genai-live] turnComplete error',
        (e as Error)?.message || e,
      );
    }

    console.debug('[genai-live] waiting for first turn...');
    const turnMsgs = await handleTurn();
    console.debug('[genai-live] first turn done', {
      messages: turnMsgs.length,
    });

    if (!audioParts.length) {
      console.debug('[genai-live] no inline audio parts received');
      return null;
    }

    // Convert collected inline parts to a WAV buffer as per the sample
    type WavOptions = {
      numChannels: number;
      sampleRate: number;
      bitsPerSample: number;
    };
    function parseMimeType(mt: string): WavOptions {
      const [fileType, ...params] = String(mt)
        .split(';')
        .map((s) => s.trim());
      const [, format] = fileType.split('/');
      const options: Partial<WavOptions> = {
        numChannels: 1,
        bitsPerSample: 16,
      };
      if (format && format.startsWith('L')) {
        const bits = parseInt(format.slice(1), 10);
        if (!Number.isNaN(bits)) options.bitsPerSample = bits;
      }
      for (const param of params) {
        const [k, v] = param.split('=').map((s) => s.trim());
        if (k === 'rate') options.sampleRate = parseInt(v, 10);
      }
      if (!options.sampleRate) options.sampleRate = 24000;
      return options as WavOptions;
    }
    function createWavHeader(dataLength: number, opts: WavOptions) {
      const { numChannels, sampleRate, bitsPerSample } = opts;
      const byteRate = (sampleRate * numChannels * bitsPerSample) / 8;
      const blockAlign = (numChannels * bitsPerSample) / 8;
      const buffer = Buffer.alloc(44);
      buffer.write('RIFF', 0);
      buffer.writeUInt32LE(36 + dataLength, 4);
      buffer.write('WAVE', 8);
      buffer.write('fmt ', 12);
      buffer.writeUInt32LE(16, 16);
      buffer.writeUInt16LE(1, 20);
      buffer.writeUInt16LE(numChannels, 22);
      buffer.writeUInt32LE(sampleRate, 24);
      buffer.writeUInt32LE(byteRate, 28);
      buffer.writeUInt16LE(blockAlign, 32);
      buffer.writeUInt16LE(bitsPerSample, 34);
      buffer.write('data', 36);
      buffer.writeUInt32LE(dataLength, 40);
      return buffer;
    }

    const opts = parseMimeType(mimeType || 'audio/L16; rate=24000');
    const decoded = audioParts.map((b64) => Buffer.from(b64, 'base64'));
    const data = Buffer.concat(decoded);
    const header = createWavHeader(data.length, opts);
    const wav = Buffer.concat([header, data]);

    const bytesPerSample = opts.bitsPerSample / 8;
    const durationSec = Math.max(
      1,
      Math.round(
        data.length / (bytesPerSample * opts.numChannels * opts.sampleRate),
      ),
    );

    console.debug('[genai-live] done', {
      mimeType: mimeType || 'unknown',
      parts: audioParts.length,
      bytes: wav.length,
      durationSec,
    });

    return {
      audioBytes: wav,
      durationSec,
      contentType: 'audio/wav',
      ext: 'wav',
    };
  } finally {
    try {
      session?.close();
    } catch {}
  }
}
