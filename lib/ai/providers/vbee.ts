/**
 * Vbee TTS Provider with Webhook Callback
 * Documentation: https://documenter.getpostman.com/view/12951168/Uz5FHbSd
 */

import { TtsResult } from '../tts-types';
import { wavToMp3 } from '../audio-utils';
import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

/**
 * Generates TTS audio using Vbee API with async webhook callback
 *
 * This function sends a request to <PERSON><PERSON> with a callback URL.
 * <PERSON>bee will process the audio asynchronously and POST to the callback URL
 * when the audio is ready.
 *
 * @param text - Text to convert to speech
 * @param voice - Voice gender ('male' or 'female')
 * @param chunkId - Unique identifier for this chunk (for webhook routing)
 * @param languageCode - Optional language code (e.g., 'vi-VN', 'en-US')
 * @returns null (indicates async processing) or throws error
 */
export async function ttsViaVbeeAsync(
  text: string,
  voice: string,
  chunkId: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const token = process.env.VBEE_TOKEN;
  const appId = process.env.VBEE_APP_ID;

  if (!token) {
    logger.warn('[tts] Vbee: No API key configured, skipping');
    return null;
  }

  // Map voice gender to Vbee voice ID
  const voiceId =
    voice === 'male'
      ? appConfig.vbeeVoiceNameMale
      : appConfig.vbeeVoiceNameFemale;

  if (!voiceId) {
    logger.error('[tts] Vbee: Voice ID not configured for', voice);
    return null;
  }

  // Build callback URL - Vbee will POST to this when audio is ready
  const baseUrl = appConfig.baseUrl;
  const callbackUrl = `${baseUrl}/api/vbee/webhook?chunkId=${encodeURIComponent(chunkId)}`;

  // Build request body
  const requestBody: any = {
    input_text: text,
    voice_code: voiceId,
    speed_rate: '1.0',
    callback_url: callbackUrl,
  };

  // Add app_id if configured
  if (appId) {
    requestBody.app_id = appId;
  }

  // Add audio format if configured
  if (appConfig.vbeeAudioFormat) {
    requestBody.audio_type = appConfig.vbeeAudioFormat;
  }

  // Add bit rate if configured
  if (appConfig.vbeeBitRate) {
    requestBody.bitrate = appConfig.vbeeBitRate;
  }

  const endpoint = `${appConfig.vbeeBaseUrl}/api/v1/tts`;

  logger.log('[tts] Vbee async request:', {
    endpoint,
    voice: voiceId,
    textLength: text.length,
    languageCode,
    chunkId,
    callbackUrl,
  });

  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    const resp = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!resp.ok) {
      const msg = await resp.text().catch(() => String(resp.status));
      logger.error('[tts] Vbee async request failed:', resp.status, msg);
      return null;
    }

    const json = await resp.json();
    const requestId = json.request_id || json.id;

    logger.log('[tts] Vbee async request sent:', {
      success: json.success,
      requestId,
      chunkId,
    });

    // Return requestId for tracking
    return {
      requestId,
      isAsync: true,
      audioBytes: null,
      durationSec: 0,
      contentType: 'audio/mpeg' as const,
      ext: 'mp3' as const,
    };
  } catch (error) {
    logger.error(
      '[tts] Vbee async request exception:',
      (error as Error)?.message || error,
    );
    return null;
  }
}

/**
 * Downloads audio from Vbee audio link
 * Used by webhook handler
 */
export async function downloadVbeeAudio(
  audioLink: string,
): Promise<TtsResult | null> {
  try {
    logger.log('[tts] Vbee: Downloading audio from link:', audioLink);

    const audioResp = await fetch(audioLink);
    if (!audioResp.ok) {
      logger.error('[tts] Vbee: Failed to download audio:', audioResp.status);
      return null;
    }

    const arrayBuf = await audioResp.arrayBuffer();
    let audioBytes = Buffer.from(arrayBuf) as Buffer;

    // Determine audio format and convert if needed
    const audioFormat = appConfig.vbeeAudioFormat || 'mp3';
    let contentType: TtsResult['contentType'] = 'audio/mpeg';
    let ext: TtsResult['ext'] = 'mp3';

    if (audioFormat === 'wav') {
      contentType = 'audio/wav';
      ext = 'wav';

      // Convert WAV to MP3 for consistency
      const mp3 = await wavToMp3(audioBytes).catch((e) => {
        logger.error('[tts] Vbee: WAV to MP3 conversion failed:', e);
        return null;
      });
      if (mp3) {
        audioBytes = mp3;
        contentType = 'audio/mpeg';
        ext = 'mp3';
      }
    }

    // Get duration from audio bytes (TODO: use proper audio duration detection)
    const durationSec = Math.max(1, Math.round(audioBytes.length / 32000)); // Rough estimate

    logger.log('[tts] Vbee audio downloaded:', {
      bytes: audioBytes.length,
      format: ext,
      duration: durationSec,
    });

    return {
      audioBytes,
      durationSec,
      contentType,
      ext,
    };
  } catch (error) {
    logger.error(
      '[tts] Vbee audio download exception:',
      (error as Error)?.message || error,
    );
    return null;
  }
}

/**
 * Attempts to retrieve TTS result from Vbee by request_id
 * Tries multiple common API patterns since official docs unavailable
 */
export async function pollVbeeResult(
  requestId: string,
): Promise<{ audioLink?: string; status?: string } | null> {
  const token = process.env.VBEE_TOKEN;
  if (!token) return null;

  const baseUrl = appConfig.vbeeBaseUrl;
  const headers = { Authorization: `Bearer ${token}` };

  // Try common REST API patterns
  const endpoints = [
    `${baseUrl}/api/v1/tts/${requestId}/result`,
    `${baseUrl}/api/v1/tts/${requestId}/status`,
    `${baseUrl}/api/v1/tts/${requestId}`,
    `${baseUrl}/api/v1/result/${requestId}`,
  ];

  for (const endpoint of endpoints) {
    try {
      logger.log('[vbee-poll] Trying endpoint:', endpoint);
      const resp = await fetch(endpoint, { headers });

      if (resp.ok) {
        const json = await resp.json();
        logger.log('[vbee-poll] Response:', { endpoint, json });

        // Extract audio_link from response (try multiple field names)
        const audioLink =
          json.audio_link || json.url || json.audioUrl || json.download_url;

        if (audioLink) {
          return {
            audioLink,
            status: json.status || 'completed',
          };
        }

        // If status indicates processing, return status
        if (json.status === 'processing' || json.status === 'pending') {
          return { status: json.status };
        }
      }
    } catch (error) {
      logger.warn('[vbee-poll] Endpoint failed:', endpoint, error);
      // Continue to next endpoint
    }
  }

  logger.warn('[vbee-poll] All endpoints failed for request_id:', requestId);
  return null;
}

/**
 * Generates TTS audio using Vbee API synchronously (no webhook)
 * Attempts to get audio directly in response or polls for short period
 */
export async function ttsViaVbeeSync(
  text: string,
  voice: string,
  languageCode?: string,
): Promise<TtsResult | null> {
  const token = process.env.VBEE_TOKEN;
  const appId = process.env.VBEE_APP_ID;

  if (!token) {
    logger.warn('[tts] Vbee: No API key configured, skipping');
    return null;
  }

  const voiceId =
    voice === 'male'
      ? appConfig.vbeeVoiceNameMale
      : appConfig.vbeeVoiceNameFemale;

  if (!voiceId) {
    logger.error('[tts] Vbee: Voice ID not configured for', voice);
    return null;
  }

  // Build request WITH callback_url (required by Vbee)
  // We'll poll for results instead of waiting for webhook
  const dummyCallbackUrl = `${appConfig.vbeeBaseUrl}/webhook/dummy`; // Vbee requires this field

  const requestBody: any = {
    input_text: text,
    voice_code: voiceId,
    speed_rate: '1.0',
    callback_url: dummyCallbackUrl, // Required by Vbee API
  };

  if (appId) requestBody.app_id = appId;
  if (appConfig.vbeeAudioFormat)
    requestBody.audio_type = appConfig.vbeeAudioFormat;
  if (appConfig.vbeeBitRate) requestBody.bitrate = appConfig.vbeeBitRate;

  const endpoint = `${appConfig.vbeeBaseUrl}/api/v1/tts`;

  logger.log('[tts] Vbee sync request (with polling):', {
    endpoint,
    voice: voiceId,
    textLength: text.length,
  });

  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };

    const resp = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!resp.ok) {
      const msg = await resp.text().catch(() => String(resp.status));
      logger.error('[tts] Vbee sync request failed:', resp.status, msg);
      return null;
    }

    const json = await resp.json();
    logger.log('[tts] Vbee sync response:', json);

    // Check if audio_link is immediately available
    const audioLink =
      json.audio_link || json.url || json.audioUrl || json.download_url;

    if (audioLink) {
      // Download audio directly
      return await downloadVbeeAudio(audioLink);
    }

    // If request_id returned but no audio yet, poll briefly (max 30s)
    const requestId = json.request_id || json.id;
    if (requestId) {
      logger.log(
        '[tts] Vbee sync: polling for result with request_id:',
        requestId,
      );

      const maxAttempts = 6; // 30 seconds total
      for (let i = 0; i < maxAttempts; i++) {
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5s between polls

        const pollResult = await pollVbeeResult(requestId);
        if (pollResult?.audioLink) {
          return await downloadVbeeAudio(pollResult.audioLink);
        }
      }
    }

    logger.error('[tts] Vbee sync: no audio_link in response');
    return null;
  } catch (error) {
    logger.error(
      '[tts] Vbee sync request exception:',
      (error as Error)?.message || error,
    );
    return null;
  }
}
