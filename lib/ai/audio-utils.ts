import { spawn } from 'child_process';
import fs from 'fs';
import os from 'os';
import path from 'path';
import { logger } from '@/lib/logger';

export function generateSilentWav(durationSec = 2, sampleRate = 16000): Buffer {
  const numChannels = 1;
  const bitsPerSample = 16;
  const byteRate = (sampleRate * numChannels * bitsPerSample) / 8;
  const blockAlign = (numChannels * bitsPerSample) / 8;
  const totalSamples = Math.max(1, Math.floor(durationSec * sampleRate));
  const dataSize = totalSamples * numChannels * (bitsPerSample / 8);
  const buffer = Buffer.alloc(44 + dataSize);
  let offset = 0;

  function writeString(str: string) {
    buffer.write(str, offset, 'ascii');
    offset += str.length;
  }
  function writeUInt32LE(n: number) {
    buffer.writeUInt32LE(n, offset);
    offset += 4;
  }
  function writeUInt16LE(n: number) {
    buffer.writeUInt16LE(n, offset);
    offset += 2;
  }

  // RIFF header
  writeString('RIFF');
  writeUInt32LE(36 + dataSize);
  writeString('WAVE');

  // fmt chunk
  writeString('fmt ');
  writeUInt32LE(16); // PCM
  writeUInt16LE(1); // PCM format
  writeUInt16LE(numChannels);
  writeUInt32LE(sampleRate);
  writeUInt32LE(byteRate);
  writeUInt16LE(blockAlign);
  writeUInt16LE(bitsPerSample);

  // data chunk
  writeString('data');
  writeUInt32LE(dataSize);
  // body already zero-filled (silence)

  return buffer;
}

export function wrapPcmToWav(bytes: Buffer, sampleRate = 24000) {
  const numChannels = 1;
  const bitsPerSample = 16;
  const dataSize = bytes.length;
  const header = Buffer.alloc(44);
  let offset = 0;
  function writeString(str: string) {
    header.write(str, offset, 'ascii');
    offset += str.length;
  }
  function writeUInt32LE(n: number) {
    header.writeUInt32LE(n, offset);
    offset += 4;
  }
  function writeUInt16LE(n: number) {
    header.writeUInt16LE(n, offset);
    offset += 2;
  }
  writeString('RIFF');
  writeUInt32LE(36 + dataSize);
  writeString('WAVE');
  writeString('fmt ');
  writeUInt32LE(16);
  writeUInt16LE(1);
  writeUInt16LE(numChannels);
  writeUInt32LE(sampleRate);
  writeUInt32LE(sampleRate * numChannels * (bitsPerSample / 8));
  writeUInt16LE(numChannels * (bitsPerSample / 8));
  writeUInt16LE(bitsPerSample);
  writeString('data');
  writeUInt32LE(dataSize);
  return Buffer.concat([header, bytes]);
}

export async function wavToMp3(wavBytes: Buffer): Promise<Buffer | null> {
  const ffmpegPath = process.env.FFMPEG_PATH || 'ffmpeg';
  const tmpDir = os.tmpdir();
  const inPath = path.join(
    tmpDir,
    `genai-tts-${Date.now()}-${Math.random().toString(36).slice(2)}.wav`,
  );
  const outPath = path.join(
    tmpDir,
    `genai-tts-${Date.now()}-${Math.random().toString(36).slice(2)}.mp3`,
  );
  await fs.promises.writeFile(inPath, wavBytes);
  try {
    await new Promise<void>((resolve, reject) => {
      const p = spawn(
        ffmpegPath,
        ['-y', '-i', inPath, '-codec:a', 'libmp3lame', '-b:a', '128k', outPath],
        { stdio: 'ignore' },
      );
      p.on('error', reject);
      p.on('exit', (code) => {
        if (code === 0) resolve();
        else reject(new Error(`ffmpeg exit ${code}`));
      });
    });
    const out = await fs.promises.readFile(outPath);
    return out;
  } catch (e) {
    logger.error('[tts] ffmpeg wav->mp3 failed', (e as Error)?.message || e);
    return null;
  } finally {
    // cleanup best-effort
    try {
      await fs.promises.unlink(inPath);
    } catch {}
    try {
      await fs.promises.unlink(outPath);
    } catch {}
  }
}

/**
 * Get the real duration of an audio file in seconds using ffprobe
 * @param audioBytes - The audio file buffer (MP3, WAV, etc.)
 * @returns Duration in seconds, or null if failed
 */
export async function getAudioDuration(
  audioBytes: Buffer,
): Promise<number | null> {
  const ffprobePath = process.env.FFPROBE_PATH || 'ffprobe';
  const tmpDir = os.tmpdir();
  const tmpPath = path.join(
    tmpDir,
    `audio-probe-${Date.now()}-${Math.random().toString(36).slice(2)}.mp3`,
  );

  await fs.promises.writeFile(tmpPath, audioBytes);

  try {
    const output = await new Promise<string>((resolve, reject) => {
      const chunks: string[] = [];
      const p = spawn(
        ffprobePath,
        [
          '-v',
          'error',
          '-show_entries',
          'format=duration',
          '-of',
          'default=noprint_wrappers=1:nokey=1',
          tmpPath,
        ],
        { stdio: ['ignore', 'pipe', 'pipe'] },
      );

      p.stdout.on('data', (chunk) => chunks.push(chunk.toString()));
      p.on('error', reject);
      p.on('exit', (code) => {
        if (code === 0) resolve(chunks.join(''));
        else reject(new Error(`ffprobe exit ${code}`));
      });
    });

    const duration = parseFloat(output.trim());
    if (isNaN(duration) || duration <= 0) {
      logger.warn('[audio-utils] ffprobe returned invalid duration:', output);
      return null;
    }

    return Math.round(duration);
  } catch (e) {
    logger.error(
      '[audio-utils] ffprobe failed to get duration',
      (e as Error)?.message || e,
    );
    return null;
  } finally {
    try {
      await fs.promises.unlink(tmpPath);
    } catch {}
  }
}

export async function concatMp3Segments(buffers: Buffer[]): Promise<Buffer> {
  if (buffers.length === 0) return Buffer.alloc(0);
  if (buffers.length === 1) return buffers[0];
  const ffmpegPath = process.env.FFMPEG_PATH || 'ffmpeg';
  const tmpDir = os.tmpdir();
  // Write each segment to a temp file and build a list file
  const files: string[] = [];
  for (const b of buffers) {
    const p = path.join(
      tmpDir,
      `tts-chunk-${Date.now()}-${Math.random().toString(36).slice(2)}.mp3`,
    );
    await fs.promises.writeFile(p, b);
    files.push(p);
  }
  const listPath = path.join(
    tmpDir,
    `tts-concat-${Date.now()}-${Math.random().toString(36).slice(2)}.txt`,
  );
  const listContent = files
    .map((p) => `file '${p.replace(/'/g, "'\\''")}'`)
    .join('\n');
  await fs.promises.writeFile(listPath, listContent, 'utf-8');
  const outPath = path.join(
    tmpDir,
    `tts-merged-${Date.now()}-${Math.random().toString(36).slice(2)}.mp3`,
  );
  try {
    await new Promise<void>((resolve, reject) => {
      const p = spawn(
        ffmpegPath,
        [
          '-y',
          '-f',
          'concat',
          '-safe',
          '0',
          '-i',
          listPath,
          '-c',
          'copy',
          outPath,
        ],
        { stdio: 'ignore' },
      );
      p.on('error', reject);
      p.on('exit', (code) => {
        if (code === 0) resolve();
        else reject(new Error(`ffmpeg concat exit ${code}`));
      });
    });
    const out = await fs.promises.readFile(outPath);
    return out;
  } catch (e) {
    logger.error(
      '[tts] ffmpeg concat failed, falling back to naive join',
      (e as Error)?.message || e,
    );
    // Fallback: naive concatenation may work for many players, not guaranteed gapless
    return Buffer.concat(buffers);
  } finally {
    try {
      await fs.promises.unlink(listPath);
    } catch {}
    for (const p of files) {
      try {
        await fs.promises.unlink(p);
      } catch {}
    }
    try {
      await fs.promises.unlink(outPath);
    } catch {}
  }
}
