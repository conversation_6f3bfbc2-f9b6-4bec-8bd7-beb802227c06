import { GoogleGenerativeAI } from '@google/generative-ai';
import { formatMs } from '@/lib/time';
import { logger } from '@/lib/logger';

const MODEL = 'gemini-2.5-pro';

export interface PageAnalysis {
  pageNumber: number;
  role: 'front_matter' | 'toc' | 'content' | 'appendix' | 'index' | 'unknown';
  confidence: number;
  reasoning: string;
  isChapterStart: boolean;
  detectedHeading: string | null;
}

export interface StructureAnalysis {
  pages: PageAnalysis[];
  suggestedStartPage: number;
  suggestedStartReasoning: string;
  documentType: string;
  hasFrontMatter: boolean;
  hasTableOfContents: boolean;
}

function buildBatchAnalysisPrompt(
  pageCount: number,
  targetLanguage: string,
): string {
  return `You are a document structure analyst. I'm providing the first ${pageCount} pages of a PDF document.

Your task is to analyze the document structure and classify each page into one of these roles:
- front_matter: Copyright, dedication, foreword, preface, acknowledgments
- toc: Table of contents, list of figures/tables
- content: Main book content (chapters, prologue, author's note meant to be read)
- appendix: Supplementary material at the end
- index: Back-of-book index
- unknown: Cannot determine

CRITICAL DECISION: Determine the FIRST page of meaningful content that a reader should start with.

Guidelines:
1. "Introduction", "Prologue", "Author's Note" are usually CONTENT (not front matter)
2. Front matter typically includes: copyright, publication info, dedication, foreword, preface, acknowledgments
3. Table of Contents is clearly labeled and has page numbers or chapter listings
4. First chapter is often "Chapter 1", "Part I", or story text without a heading
5. Consider typography: front matter often has different formatting/smaller text
6. Academic papers: abstract and introduction are CONTENT
7. Novels: prologue and chapter 1 are CONTENT

Return a JSON object with this structure:
{
  "pages": [
    {
      "pageNumber": 1,
      "role": "front_matter",
      "confidence": 0.95,
      "reasoning": "Copyright and publication information",
      "isChapterStart": false,
      "detectedHeading": "Copyright © 2024"
    },
    {
      "pageNumber": 2,
      "role": "toc",
      "confidence": 0.90,
      "reasoning": "Table of Contents with chapter listings",
      "isChapterStart": false,
      "detectedHeading": "Contents"
    },
    {
      "pageNumber": 5,
      "role": "content",
      "confidence": 1.0,
      "reasoning": "First chapter begins - 'Chapter 1: The Beginning'",
      "isChapterStart": true,
      "detectedHeading": "Chapter 1: The Beginning"
    }
  ],
  "suggestedStartPage": 5,
  "suggestedStartReasoning": "First meaningful content starts at Chapter 1",
  "documentType": "novel|textbook|academic|technical|other",
  "hasFrontMatter": true,
  "hasTableOfContents": true
}

Return ONLY valid JSON, no other text.`;
}

/**
 * Analyze a single multi-page PDF for document structure
 *
 * This is a simpler version for fast-track processing where we have
 * one PDF with multiple pages instead of individual page PDFs.
 */
export async function analyzeDocumentStructure(params: {
  pdfBytes: Buffer;
  pageCount: number;
}): Promise<StructureAnalysis> {
  const { pdfBytes, pageCount } = params;

  logger.log('[structure-analyzer] Starting single-PDF analysis', {
    pageCount,
    bufferSize: pdfBytes.length,
  });

  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    logger.warn('[structure-analyzer] No API key, using mock data');
    return mockStructureAnalysis(pageCount);
  }

  const t1 = Date.now();

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: MODEL });

  const prompt = buildBatchAnalysisPrompt(pageCount, 'en'); // Default to English

  const contentParts = [
    { text: prompt },
    {
      inlineData: {
        mimeType: 'application/pdf',
        data: pdfBytes.toString('base64'),
      },
    },
  ];

  logger.log('[structure-analyzer] Sending multi-page PDF to Gemini', {
    pageCount,
    model: MODEL,
  });

  const result = await model.generateContent(contentParts);
  const t2 = Date.now();

  logger.log('[structure-analyzer] Received response from Gemini', {
    elapsed: formatMs(t2 - t1),
  });

  const text = result.response.text();
  const parsed = safeParseJson(text);

  logger.log('[structure-analyzer] Parsed analysis', {
    pagesAnalyzed: parsed.pages?.length || 0,
    suggestedStartPage: parsed.suggestedStartPage,
    documentType: parsed.documentType,
    hasFrontMatter: parsed.hasFrontMatter,
  });

  return {
    pages: parsed.pages || [],
    suggestedStartPage: parsed.suggestedStartPage || 1,
    suggestedStartReasoning: parsed.suggestedStartReasoning || '',
    documentType: parsed.documentType || 'unknown',
    hasFrontMatter: parsed.hasFrontMatter ?? false,
    hasTableOfContents: parsed.hasTableOfContents ?? false,
  };
}

function safeParseJson(text: string) {
  const trimmed = text.trim();
  const start = trimmed.indexOf('{');
  const end = trimmed.lastIndexOf('}');
  const slice =
    start >= 0 && end >= start ? trimmed.slice(start, end + 1) : trimmed;
  return JSON.parse(slice);
}

function mockStructureAnalysis(pageCount: number): StructureAnalysis {
  // Mock for development without API key
  const pages: PageAnalysis[] = [];

  for (let i = 1; i <= pageCount; i++) {
    if (i <= 4) {
      pages.push({
        pageNumber: i,
        role: 'front_matter',
        confidence: 0.9,
        reasoning: 'Mock front matter',
        isChapterStart: false,
        detectedHeading: null,
      });
    } else {
      pages.push({
        pageNumber: i,
        role: 'content',
        confidence: 1.0,
        reasoning: 'Mock content',
        isChapterStart: i === 5,
        detectedHeading: i === 5 ? 'Chapter 1' : null,
      });
    }
  }

  return {
    pages,
    suggestedStartPage: 5,
    suggestedStartReasoning: 'Mock: First content page',
    documentType: 'novel',
    hasFrontMatter: true,
    hasTableOfContents: false,
  };
}

export async function analyzeDocumentStructureWithGemini(params: {
  pagePdfs: Array<{ pageNumber: number; pageId: string; buffer: Buffer }>;
  targetLanguage: string;
}): Promise<StructureAnalysis> {
  const { pagePdfs, targetLanguage } = params;

  logger.log('[structure-analyzer] Starting batch analysis', {
    pageCount: pagePdfs.length,
    targetLanguage,
  });

  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    logger.warn('[structure-analyzer] No API key, using mock data');
    // Mock response for development
    return mockStructureAnalysis(pagePdfs.length);
  }

  const t1 = Date.now();

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: MODEL });

  const prompt = buildBatchAnalysisPrompt(pagePdfs.length, targetLanguage);

  // Build multi-file content array
  const contentParts: any[] = [{ text: prompt }];

  // Add each page PDF
  for (const { pageNumber, buffer } of pagePdfs) {
    contentParts.push({
      text: `Page ${pageNumber}:`,
    });
    contentParts.push({
      inlineData: {
        mimeType: 'application/pdf',
        data: buffer.toString('base64'),
      },
    });
  }

  logger.log('[structure-analyzer] Sending batch to Gemini', {
    pageCount: pagePdfs.length,
    model: MODEL,
    contentPartsCount: contentParts.length,
  });

  const result = await model.generateContent(contentParts);

  const t2 = Date.now();

  logger.log('[structure-analyzer] Received response from Gemini', {
    elapsed: formatMs(t2 - t1),
  });

  const text = result.response.text();

  // Parse JSON response
  const parsed = safeParseJson(text);

  logger.log('[structure-analyzer] Parsed analysis', {
    pagesAnalyzed: parsed.pages?.length || 0,
    suggestedStartPage: parsed.suggestedStartPage,
    documentType: parsed.documentType,
    hasFrontMatter: parsed.hasFrontMatter,
  });

  return {
    pages: parsed.pages || [],
    suggestedStartPage: parsed.suggestedStartPage || 1,
    suggestedStartReasoning: parsed.suggestedStartReasoning || '',
    documentType: parsed.documentType || 'unknown',
    hasFrontMatter: parsed.hasFrontMatter ?? false,
    hasTableOfContents: parsed.hasTableOfContents ?? false,
  };
}
