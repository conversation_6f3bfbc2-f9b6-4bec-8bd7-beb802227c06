import { GoogleGenerativeAI } from '@google/generative-ai';
import { formatMs } from '@/lib/time';
import { logger } from '@/lib/logger';

const MODEL = 'gemini-2.5-pro';

export interface TranslateResult {
  sentences: string[];
  translatedSentences: string[];
  endsWithCompleteSentence: boolean;
  isChapterStart: boolean;
  isSkippable: boolean;
  detectedHeading: string | null;
  headingConfidence: number;
  modelVersion: string;
}

function buildPrompt(opts: {
  pageNumber: number;
  targetLanguage: string;
  carryoverText?: string;
}) {
  const { pageNumber, targetLanguage, carryoverText } = opts;

  // Enhanced carryover instruction with explicit merge semantics
  const carry = carryoverText
    ? `\n**CRITICAL CARRYOVER HANDLING:**
The previous page ended with an incomplete sentence fragment: "${carryoverText}"

You MUST merge this fragment with the FIRST sentence on the current page to form one complete sentence.

How to handle this:
1. Identify the first sentence on this page
2. Concatenate the carryover fragment with the first sentence (they form ONE sentence together)
3. This merged sentence becomes sentences[0] in your response
4. Translate the complete merged sentence as translatedSentences[0]

Example:
- Carryover from previous page: "She wanted to hear"
- First text on current page: "everything."
- Result sentences[0]: "She wanted to hear everything."
- Result translatedSentences[0]: "[translation of the complete merged sentence]"

The carryover and first sentence are NOT two separate sentences - they are ONE sentence split across pages.
\n`
    : '';

  const prompt = `You are a professional translator. Analyze this PDF page and provide a JSON response with the following structure:\n\n${carry}{\n  "sentences": ["...", "..."],\n  "translatedSentences": ["...", "..."],\n  "endsWithCompleteSentence": true,\n  "isChapterStart": false,\n  "isSkippable": false,\n  "detectedHeading": null,\n  "headingConfidence": 0.0\n}\n\nInstruction:\n- Preserve formatting and structure\n- "sentences": Split the original text into an array of sentences in the ORIGINAL language. ${carryoverText ? 'Remember to merge the carryover fragment with the first sentence as instructed above. ' : ''}Preserve the original sentence structure and punctuation.\n- "translatedSentences": Translate each sentence to ${targetLanguage} and return an array of translated sentences, same order, 1:1 mapping with "sentences". Ensure the output is clean with no annotations or metadata.\n- **CONTENT FILTERING**: When translating, soften any profanity or offensive language:\n  * Replace profanity with milder euphemisms (e.g., "damn" → "darn", "hell" → "heck", "f*ck" → "fudge" or contextual alternatives)\n  * Maintain emotional intensity by using strong but non-profane words (e.g., "furious" instead of "f*cking angry", "terrible" instead of "damn bad")\n  * Apply this consistently in all contexts - dialogue, narration, and descriptive text\n  * The goal is family-friendly content that passes content filters while preserving the author's intended emotional impact\n- "endsWithCompleteSentence": true if last sentence ends with a period, question mark, or exclamation point; otherwise false\n- "isChapterStart": true if this page appears to begin a new section/chapter; try keywords like "Chapter", "Section", roman numerals, numbered headings, and typographic cues like large/bold title, centered heading, whitespace above/below. Do not rely solely on font size or weight. Consider section headers, large titles\n- "isSkippable": true if this page is front matter, table of contents, or other non-content (e.g. "Contents", "Preface", "Acknowledgments", "Introduction", "About the Author", "Copyright", "Index", etc.)\n- "detectedHeading": Extract the chapter/section title if found\n- "headingConfidence": Rate 0.0-1.0 based on typography and content clues\n- Handle page ${pageNumber} context appropriately\n- Return ONLY valid JSON, no other text.`;

  return prompt;
}

function safeParseJson(text: string) {
  const trimmed = text.trim();
  const start = trimmed.indexOf('{');
  const end = trimmed.lastIndexOf('}');
  const slice =
    start >= 0 && end >= start ? trimmed.slice(start, end + 1) : trimmed;
  return JSON.parse(slice);
}

/**
 * Validate if the last sentence actually ends with a complete sentence marker
 */
function validateEndsWithCompleteSentence(
  sentences: string[],
  geminiFlag: boolean,
): boolean {
  if (sentences.length === 0) return true;

  const lastSentence = sentences[sentences.length - 1].trim();
  if (!lastSentence) return true;

  const lastChar = lastSentence[lastSentence.length - 1];
  const actuallyComplete = ['.', '?', '!'].includes(lastChar);

  // Log mismatch for debugging
  if (actuallyComplete !== geminiFlag) {
    logger.warn('[translate] endsWithCompleteSentence mismatch', {
      geminiFlag,
      actuallyComplete,
      lastSentence: lastSentence.substring(
        Math.max(0, lastSentence.length - 50),
      ),
      lastChar,
    });
  }

  return actuallyComplete;
}

export async function translatePdfPageWithGemini(params: {
  fileBytes: Buffer;
  pageNumber: number;
  targetLanguage: string;
  carryoverText?: string;
}): Promise<TranslateResult> {
  const { fileBytes, pageNumber, targetLanguage, carryoverText } = params;

  logger.log(
    '<><><> GEMINI translate params=',
    JSON.stringify({
      pageNumber,
      targetLanguage,
      carryoverText,
      hasCarryover: !!carryoverText,
    }),
  );

  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    // Mock when no API key available
    return {
      sentences: [],
      translatedSentences: [
        `Mock translation for page ${pageNumber} in ${targetLanguage}.`,
      ],
      endsWithCompleteSentence: true,
      isChapterStart: pageNumber === 1,
      isSkippable: pageNumber <= 2 ? true : false,
      detectedHeading: pageNumber === 1 ? 'Introduction' : null,
      headingConfidence: pageNumber === 1 ? 0.8 : 0.0,
      modelVersion: 'mock',
    };
  }

  const t1 = Date.now();

  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ model: MODEL });
  const prompt = buildPrompt({ pageNumber, targetLanguage, carryoverText });
  const result = await model.generateContent([
    { text: prompt },
    {
      inlineData: {
        mimeType: 'application/pdf',
        data: fileBytes.toString('base64'),
      },
    },
  ] as any);

  const t2 = Date.now();

  logger.log('<><><> GEMINI translate result=', JSON.stringify(result));
  logger.log('<><><> GEMINI translate done', {
    ok: !!result,
    elapsed: formatMs(t2 - t1),
  });

  const text = result.response.text();
  const parsed = safeParseJson(text);

  // Log Gemini response details
  logger.log('<><><> GEMINI translate response=', {
    pageNumber,
    sentenceCount: parsed.sentences?.length || 0,
    firstSentence: parsed.sentences?.[0]?.substring(0, 100) || null,
    carryoverPresent: !!carryoverText,
    carryoverInFirstSentence: carryoverText
      ? parsed.sentences?.[0]?.includes(carryoverText)
      : null,
  });

  let sentences = Array.isArray(parsed.sentences)
    ? parsed.sentences.map((s: any) => String(s))
    : [];
  let translatedSentences = Array.isArray(parsed.translatedSentences)
    ? parsed.translatedSentences.map((s: any) => String(s))
    : [];

  // Post-processing: Validate carryover merge
  if (carryoverText && sentences.length > 0) {
    const firstSentence = sentences[0];
    const carryoverTrimmed = carryoverText.trim();

    // Check if first sentence starts with carryover (successful merge)
    const startsWithCarryover = firstSentence
      .trim()
      .startsWith(carryoverTrimmed);

    // Check if carryover appears as separate sentence at index 0
    const isCarryoverSeparate = firstSentence.trim() === carryoverTrimmed;

    if (!startsWithCarryover) {
      // Gemini failed to merge despite explicit prompt instruction
      logger.error(
        '[gemini] CRITICAL: Carryover not merged by Gemini despite enhanced prompt',
        {
          pageNumber,
          carryoverText,
          firstSentence: firstSentence.substring(0, 100),
          expectedToStartWith: carryoverTrimmed,
        },
      );

      // DO NOT manually merge - it would create mismatched original/translated arrays
      // The enhanced prompt should prevent this from happening
      // If this error occurs, the prompt needs to be improved further
    } else if (isCarryoverSeparate && sentences.length > 1) {
      // Gemini returned carryover as separate sentence - merge with next
      logger.warn(
        '[gemini] Carryover returned as separate sentence, merging with next',
        {
          pageNumber,
          carryoverText,
          secondSentence: sentences[1].substring(0, 100),
        },
      );

      // Merge sentences[0] (carryover) with sentences[1]
      sentences[0] = sentences[0] + ' ' + sentences[1];
      sentences.splice(1, 1);

      // Merge translations too
      translatedSentences[0] =
        translatedSentences[0] + ' ' + translatedSentences[1];
      translatedSentences.splice(1, 1);

      logger.log('[gemini] Carryover sentences merged', {
        pageNumber,
        mergedSentence: sentences[0].substring(0, 100),
      });
    } else {
      // Success - carryover was properly merged
      logger.log('[gemini] Carryover successfully merged by Gemini', {
        pageNumber,
        firstSentence: firstSentence.substring(0, 100),
      });
    }
  }

  return {
    sentences,
    translatedSentences,
    endsWithCompleteSentence: validateEndsWithCompleteSentence(
      sentences,
      Boolean(parsed.endsWithCompleteSentence),
    ),
    isChapterStart: Boolean(parsed.isChapterStart),
    isSkippable: Boolean(parsed.isSkippable),
    detectedHeading: parsed.detectedHeading ?? null,
    headingConfidence: Number(parsed.headingConfidence ?? 0),
    modelVersion: MODEL,
  };
}
