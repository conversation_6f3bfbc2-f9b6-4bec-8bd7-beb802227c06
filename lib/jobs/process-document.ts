import { Job } from 'bullmq';
import { PDFDocument } from 'pdf-lib';
import { ProcessDocumentJobData } from '@/lib/queue/jobs';
import { prisma } from '@/lib/prisma';
import { readBufferFlexible } from '@/lib/file-service';
import { savePagePdf } from '@/lib/storage';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { logger } from '@/lib/logger';

/**
 * Job handler: process-document
 *
 * Responsibilities:
 * 1. Load the original PDF from storage
 * 2. Split into single-page PDFs
 * 3. Save each page PDF to storage
 * 4. Update Page records with s3KeySinglePagePdf
 * 5. Enqueue translate-first-page job
 */
export async function processDocumentJob(job: Job<ProcessDocumentJobData>) {
  const { docId, targetLanguage, voice } = job.data;

  logger.log('[process-document] Starting job', { docId });

  // Load document from DB
  const doc = await prisma.document.findUnique({
    where: { id: docId },
    include: { pages: { orderBy: { pageNumber: 'asc' } } },
  });

  if (!doc) {
    throw new Error(`Document ${docId} not found`);
  }

  if (!doc.s3KeyOriginal) {
    throw new Error(`Document ${docId} has no original PDF`);
  }

  // Load original PDF from storage
  const originalBuffer = await readBufferFlexible(doc.s3KeyOriginal);
  const pdfDoc = await PDFDocument.load(originalBuffer);
  const pageCount = pdfDoc.getPageCount();

  logger.log('[process-document] PDF loaded', { docId, pageCount });

  // Split PDF into single-page PDFs and save to storage
  for (const page of doc.pages) {
    const pageNumber = page.pageNumber;

    // Create single-page PDF
    const singlePageDoc = await PDFDocument.create();
    const [copiedPage] = await singlePageDoc.copyPages(pdfDoc, [
      pageNumber - 1,
    ]);
    singlePageDoc.addPage(copiedPage);

    const pageBytes = await singlePageDoc.save();
    const pageBuffer = Buffer.from(pageBytes);

    // Save to storage
    const s3Key = await savePagePdf(docId, pageNumber, pageBuffer);

    // Update Page record
    await prisma.page.update({
      where: { id: page.id },
      data: { s3KeySinglePagePdf: s3Key },
    });

    logger.log('[process-document] Page PDF saved', {
      docId,
      pageNumber,
      s3Key,
    });
  }

  logger.log('[process-document] All pages split and saved', { docId });

  // Decide next job based on page count
  // For documents >10 pages: analyze structure first
  // For documents ≤10 pages: skip directly to translation
  if (pageCount > 10) {
    await documentQueue.add(
      JOB_TYPES.ANALYZE_DOCUMENT_STRUCTURE,
      { docId, targetLanguage, maxPagesToAnalyze: 20 },
      {
        jobId: `${JOB_TYPES.ANALYZE_DOCUMENT_STRUCTURE}-${docId}`,
      },
    );

    logger.log('[process-document] Enqueued analyze-structure job', {
      docId,
      pageCount,
    });
  } else {
    // Short document - skip analysis, go directly to translation
    await documentQueue.add(
      JOB_TYPES.TRANSLATE_PAGE,
      { docId, targetLanguage, voice },
      {
        jobId: `${JOB_TYPES.TRANSLATE_PAGE}-${docId}`,
      },
    );

    logger.log(
      '[process-document] Short document, skipping analysis, enqueued translate-page job',
      { docId, pageCount },
    );
  }

  return { success: true, docId, pageCount };
}
