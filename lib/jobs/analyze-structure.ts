import { Job } from 'bullmq';
import { AnalyzeDocumentStructureJobData } from '@/lib/queue/jobs';
import { prisma } from '@/lib/prisma';
import { readBufferFlexible } from '@/lib/file-service';
import { analyzeDocumentStructureWithGemini } from '@/lib/ai/structure-analyzer';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { logger } from '@/lib/logger';

/**
 * Job handler: analyze-document-structure
 *
 * Responsibilities:
 * 1. Load first N page PDFs from storage (adaptive: 20 → 30)
 * 2. Send batch to Gemini for structure analysis
 * 3. Update Page records with role assignments
 * 4. Set Document suggestedStartPageId
 * 5. Create Chapter records for detected chapter starts
 * 6. Enqueue translate-page job
 */
export async function analyzeDocumentStructureJob(
  job: Job<AnalyzeDocumentStructureJobData>,
) {
  const { docId, targetLanguage, maxPagesToAnalyze = 20 } = job.data;

  logger.log('[analyze-structure] Starting job', { docId, maxPagesToAnalyze });

  // Load document and pages
  const doc = await prisma.document.findUnique({
    where: { id: docId },
    include: { pages: { orderBy: { pageNumber: 'asc' } } },
  });

  if (!doc) {
    throw new Error(`Document ${docId} not found`);
  }

  // Get first N pages that have single-page PDFs
  const pagesToAnalyze = doc.pages
    .filter((p) => p.s3KeySinglePagePdf)
    .slice(0, maxPagesToAnalyze);

  if (pagesToAnalyze.length === 0) {
    throw new Error('No pages with PDFs found for analysis');
  }

  logger.log('[analyze-structure] Loading page PDFs', {
    docId,
    pageCount: pagesToAnalyze.length,
  });

  // Load all page PDFs
  const pagePdfs = await Promise.all(
    pagesToAnalyze.map(async (page) => ({
      pageNumber: page.pageNumber,
      pageId: page.id,
      buffer: await readBufferFlexible(page.s3KeySinglePagePdf!),
    })),
  );

  // Send to Gemini for batch analysis
  logger.log('[analyze-structure] Sending to Gemini for analysis', { docId });

  const analysis = await analyzeDocumentStructureWithGemini({
    pagePdfs,
    targetLanguage,
  });

  logger.log('[analyze-structure] Analysis complete', {
    docId,
    suggestedStartPage: analysis.suggestedStartPage,
    documentType: analysis.documentType,
    hasFrontMatter: analysis.hasFrontMatter,
  });

  // Check if we need to expand analysis (adaptive strategy)
  const hasContentPage = analysis.pages.some((p) => p.role === 'content');

  if (!hasContentPage && maxPagesToAnalyze === 20 && doc.pages.length > 20) {
    logger.log(
      '[analyze-structure] No content found in first 20 pages, expanding to 30',
      { docId },
    );

    // Re-run with 30 pages
    return analyzeDocumentStructureJob({
      ...job,
      data: { ...job.data, maxPagesToAnalyze: 30 },
    } as Job<AnalyzeDocumentStructureJobData>);
  }

  // Update pages with role assignments
  for (const pageAnalysis of analysis.pages) {
    const page = pagesToAnalyze.find(
      (p) => p.pageNumber === pageAnalysis.pageNumber,
    );
    if (page) {
      await prisma.page.update({
        where: { id: page.id },
        data: {
          role: pageAnalysis.role,
          isChapterStart: pageAnalysis.isChapterStart,
          isSkippableByDefault:
            pageAnalysis.role === 'front_matter' || pageAnalysis.role === 'toc',
        },
      });

      logger.log('[analyze-structure] Page role updated', {
        docId,
        pageNumber: pageAnalysis.pageNumber,
        role: pageAnalysis.role,
        isChapterStart: pageAnalysis.isChapterStart,
      });

      // Create chapter if this is a chapter start
      if (pageAnalysis.isChapterStart && pageAnalysis.detectedHeading) {
        await prisma.chapter.create({
          data: {
            documentId: docId,
            name: pageAnalysis.detectedHeading,
            startPageId: page.id,
            order: pageAnalysis.pageNumber,
            level: 1,
            source: 'ai_batch_analysis',
          },
        });

        logger.log('[analyze-structure] Chapter created', {
          docId,
          pageNumber: pageAnalysis.pageNumber,
          heading: pageAnalysis.detectedHeading,
        });
      }
    }
  }

  // Set suggested start page
  const suggestedPage = pagesToAnalyze.find(
    (p) => p.pageNumber === analysis.suggestedStartPage,
  );

  if (suggestedPage) {
    await prisma.document.update({
      where: { id: docId },
      data: {
        suggestedStartPageId: suggestedPage.id,
        tocSource: 'ai_batch_analysis',
      },
    });

    logger.log('[analyze-structure] Suggested start page set', {
      docId,
      pageNumber: analysis.suggestedStartPage,
      reasoning: analysis.suggestedStartReasoning,
    });
  } else {
    // Fallback: if suggested page not found, use first content page
    const firstContentPage = pagesToAnalyze.find((p) => {
      const pageAnalysis = analysis.pages.find(
        (pa) => pa.pageNumber === p.pageNumber,
      );
      return pageAnalysis?.role === 'content';
    });

    if (firstContentPage) {
      await prisma.document.update({
        where: { id: docId },
        data: {
          suggestedStartPageId: firstContentPage.id,
          tocSource: 'ai_batch_analysis',
        },
      });

      logger.log('[analyze-structure] Fallback: Using first content page', {
        docId,
        pageNumber: firstContentPage.pageNumber,
      });
    } else {
      // No content page found, use page 1
      const firstPage = doc.pages[0];
      if (firstPage) {
        await prisma.document.update({
          where: { id: docId },
          data: {
            suggestedStartPageId: firstPage.id,
            tocSource: 'ai_batch_analysis',
          },
        });

        logger.warn(
          '[analyze-structure] No content page found, defaulting to page 1',
          { docId },
        );
      }
    }
  }

  logger.log('[analyze-structure] Database updated', { docId });

  // Enqueue translation job
  const voice = (doc as any).voice || 'female'; // Fallback to female voice if not set

  await documentQueue.add(
    JOB_TYPES.TRANSLATE_PAGE,
    { docId, targetLanguage, voice },
    {
      jobId: `${JOB_TYPES.TRANSLATE_PAGE}-${docId}`,
    },
  );

  logger.log('[analyze-structure] Enqueued translate-page job', { docId });

  return {
    success: true,
    docId,
    suggestedStartPage: analysis.suggestedStartPage,
    pagesAnalyzed: pagesToAnalyze.length,
    documentType: analysis.documentType,
  };
}
