import { Job } from 'bullmq';
import { TranslatePageJobData, DOCUMENT_STATUS } from '@/lib/queue/jobs';
import { prisma } from '@/lib/prisma';
import { readBufferFlexible } from '@/lib/file-service';
import { translatePdfPageWithGemini } from '@/lib/ai/gemini';
import {
  saveTranslatedSentences,
  saveTranslationMeta,
  deletePagePdf,
} from '@/lib/storage';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { logger } from '@/lib/logger';
import { appConfig } from '@/lib/config';

/**
 * Job handler: translate-page
 *
 * Responsibilities:
 * 1. Translate a page using Gemini (finds first non-skippable if pageId not provided)
 * 2. Save translation to storage
 * 3. Create/update PageAudio record
 * 4. If first page: Update document status and set suggestedStartPageId
 * 5. Enqueue generate-page-audio job
 */
export async function translatePageJob(job: Job<TranslatePageJobData>) {
  const { docId, targetLanguage, voice, pageId } = job.data;

  logger.log('[translate-page] Starting job', { docId, pageId });

  const doc = await prisma.document.findUnique({
    where: { id: docId },
    include: { pages: { orderBy: { pageNumber: 'asc' } } },
  });

  if (!doc) {
    throw new Error(`Document ${docId} not found`);
  }

  // Determine if this is the first page job (no pageId provided)
  const isFirstPageJob = !pageId;

  if (isFirstPageJob) {
    // Update status only for first page
    await prisma.document.update({
      where: { id: docId },
      data: { status: DOCUMENT_STATUS.TRANSLATING_FIRST_PAGE },
    });
  }

  const pageCount = doc.pages.length;
  const maxPagesToCheck = 15; // Check up to 15 pages to find first non-skippable

  let foundPage: {
    page: { id: string; pageNumber: number };
    translation: any;
  } | null = null;

  // Process pages SEQUENTIALLY (one by one) to respect carryover dependencies
  // This ensures page 1 is always translated before page 2, etc.
  for (let i = 0; i < Math.min(pageCount, maxPagesToCheck) && !foundPage; i++) {
    const page = doc.pages[i];

    if (!page.s3KeySinglePagePdf) {
      logger.warn('[translate-page] Page missing PDF', {
        docId,
        pageId: page.id,
        pageNumber: page.pageNumber,
      });
      continue;
    }

    logger.log('[translate-page] Translating page sequentially', {
      docId,
      pageNumber: page.pageNumber,
    });

    try {
      // Load page PDF
      const pageBuffer = await readBufferFlexible(page.s3KeySinglePagePdf);

      // Get carryover text from previous page if it exists
      let carryoverText: string | undefined;
      if (i > 0) {
        const prevPage = doc.pages[i - 1];
        // Check if previous page ended with incomplete sentence
        const prevPageData = await prisma.page.findUnique({
          where: { id: prevPage.id },
          select: { endsWithCompleteSentence: true },
        });

        if (prevPageData && prevPageData.endsWithCompleteSentence === false) {
          // Load previous page's translation metadata to get last sentence
          const prevMetaPath = `documents/${docId}/pages/page-${prevPage.pageNumber}/translations/${targetLanguage}/meta.json`;
          try {
            const prevMetaBuffer = await readBufferFlexible(prevMetaPath);
            const prevMeta = JSON.parse(prevMetaBuffer.toString());
            if (prevMeta.sentences && prevMeta.sentences.length > 0) {
              carryoverText = prevMeta.sentences[prevMeta.sentences.length - 1];
              logger.log(
                '[translate-page] Using carryover text from previous page',
                {
                  docId,
                  pageNumber: page.pageNumber,
                  prevPageNumber: prevPage.pageNumber,
                },
              );
            }
          } catch (error) {
            logger.warn('[translate-page] Could not load carryover text', {
              docId,
              pageNumber: page.pageNumber,
              error,
            });
          }
        }
      }

      // Translate using Gemini with carryover text
      const translation = await translatePdfPageWithGemini({
        fileBytes: pageBuffer,
        pageNumber: page.pageNumber,
        targetLanguage,
        carryoverText,
      });

      // Save translation
      const textPath = await saveTranslatedSentences(
        docId,
        page.pageNumber,
        targetLanguage,
        translation.translatedSentences || [],
      );

      await saveTranslationMeta(docId, page.pageNumber, targetLanguage, {
        sentences: translation.sentences ?? [],
        endsWithCompleteSentence: translation.endsWithCompleteSentence,
        isChapterStart: translation.isChapterStart,
        isSkippable: translation.isSkippable ?? false,
        detectedHeading: translation.detectedHeading,
        headingConfidence: translation.headingConfidence,
        modelVersion: translation.modelVersion,
      });

      // Cleanup page PDF after successful translation (saves ~40-50% storage)
      if (appConfig.cleanupPagePdfsAfterTranslation) {
        await deletePagePdf(docId, page.pageNumber);
      }

      // Create or update PageAudio record
      const modelVersion = translation.modelVersion || 'v1';
      await prisma.pageAudio.upsert({
        where: {
          pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
            pageId: page.id,
            language: targetLanguage,
            voice,
            ttsEngine: 'gemini',
            ttsParamsHash: 'default',
            modelVersion,
          },
        },
        update: { s3KeyTranslatedText: textPath },
        create: {
          pageId: page.id,
          language: targetLanguage,
          voice,
          ttsEngine: 'gemini',
          ttsParamsHash: 'default',
          modelVersion,
          s3KeyTranslatedText: textPath,
        },
      });

      // Update page metadata
      await prisma.page.update({
        where: { id: page.id },
        data: {
          endsWithCompleteSentence: translation.endsWithCompleteSentence,
          isChapterStart: translation.isChapterStart ?? false,
          isSkippableByDefault: translation.isSkippable ?? false,
        },
      });

      logger.log('[translate-page] Page translated', {
        docId,
        pageNumber: page.pageNumber,
        isSkippable: translation.isSkippable,
        endsWithCompleteSentence: translation.endsWithCompleteSentence,
      });

      // If this is the first non-skippable page, we're done
      if (!translation.isSkippable) {
        foundPage = {
          page: { id: page.id, pageNumber: page.pageNumber },
          translation,
        };
        logger.log('[translate-page] Found first non-skippable page', {
          docId,
          pageNumber: page.pageNumber,
        });
        break;
      }
    } catch (error) {
      logger.error('[translate-page] Translation failed for page', {
        docId,
        pageNumber: page.pageNumber,
        error,
      });
      // Continue to next page even if this one fails
    }
  }

  if (!foundPage) {
    throw new Error(
      `No non-skippable page found in first ${maxPagesToCheck} pages`,
    );
  }

  logger.log('[translate-page] Found first meaningful page', {
    docId,
    pageId: foundPage.page.id,
    pageNumber: foundPage.page.pageNumber,
  });

  if (isFirstPageJob) {
    // Update document with suggested start page and status (only for first page)
    await prisma.document.update({
      where: { id: docId },
      data: {
        suggestedStartPageId: foundPage.page.id,
        status: DOCUMENT_STATUS.TRANSLATED_FIRST_PAGE,
      },
    });
  }

  // Enqueue next job: generate-page-audio
  await documentQueue.add(
    JOB_TYPES.GENERATE_PAGE_AUDIO,
    {
      docId,
      pageId: foundPage.page.id,
      targetLanguage,
      voice,
    },
    {
      jobId: `${JOB_TYPES.GENERATE_PAGE_AUDIO}-${docId}-${foundPage.page.pageNumber}`,
    },
  );

  logger.log('[translate-page] Enqueued generate-page-audio job', {
    docId,
    pageId: foundPage.page.id,
  });

  return {
    success: true,
    docId,
    pageId: foundPage.page.id,
    pageNumber: foundPage.page.pageNumber,
  };
}
