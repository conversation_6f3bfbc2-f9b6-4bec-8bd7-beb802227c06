import { Job } from 'bullmq';
import { ProcessPageJobData } from '@/lib/queue/jobs';
import { prisma } from '@/lib/prisma';
import { appConfig } from '@/lib/config';
import path from 'path';
import {
  readBufferFlexible,
  readTextFlexible,
  saveFile,
  fileExistsFlexible,
} from '@/lib/file-service';
import { translatePdfPageWithGemini } from '@/lib/ai/gemini';
import {
  saveTranslatedSentences,
  saveTranslationMeta,
  deletePagePdf,
} from '@/lib/storage';
import { prepareTtsText } from '@/lib/api/page/tts-preparation';
import {
  mapLanguageCode,
  applyTwoTierChunkingStrategy,
  applyVbeeChunkingStrategy,
} from '@/lib/api/page/chunking';
import {
  synthesizeFirstChunk,
  runBackgroundSynthesis,
} from '@/lib/api/page/audio-synthesis';
import { logger } from '@/lib/logger';
import {
  pollVbeeResult,
  downloadVbeeAudio,
  ttsViaVbeeSync,
} from '@/lib/ai/providers/vbee';

/**
 * Job handler: process-page
 *
 * Processes a single page (translation + audio generation)
 * Used for pages beyond the first page
 *
 * Responsibilities:
 * 1. Load page PDF
 * 2. Translate with Gemini
 * 3. Save translation
 * 4. Generate audio chunks
 * 5. Save chunks to storage
 */
export async function processPageJob(job: Job<ProcessPageJobData>) {
  const { docId, pageId, pageNumber, targetLanguage, voice } = job.data;

  logger.log('[process-page] Starting job', {
    docId,
    pageId,
    pageNumber,
  });

  // Get page
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: {
      document: true,
    },
  });

  if (!page) {
    throw new Error(`Page ${pageId} not found`);
  }

  if (!page.s3KeySinglePagePdf) {
    throw new Error(`Page ${pageId} has no single-page PDF`);
  }

  // Check if page is already skippable by default
  if (page.isSkippableByDefault) {
    logger.log('[process-page] Page is skippable by default, skipping', {
      docId,
      pageId,
      pageNumber,
    });
    return { success: true, skipped: true, docId, pageId, pageNumber };
  }

  // Check if audio already exists (idempotency - early exit)
  const chunksDir = [
    'documents',
    docId,
    'pages',
    `page-${pageNumber}`,
    'audio',
    targetLanguage,
    voice,
    'chunks',
  ].join('/');
  const firstChunkPath = `${chunksDir}/chunk-1.mp3`;

  if (await fileExistsFlexible(firstChunkPath)) {
    logger.log('[process-page] Audio already exists, skipping', {
      docId,
      pageId,
      pageNumber,
    });
    // Ensure page status is ready
    await prisma.page.update({
      where: { id: pageId },
      data: { status: 'ready' },
    });
    return {
      success: true,
      skipped: true,
      reason: 'audio_exists',
      docId,
      pageId,
      pageNumber,
    };
  }

  // STEP 1: Translation
  logger.log('[process-page] Starting translation', {
    docId,
    pageId,
    pageNumber,
  });

  // Check if translation already exists in database
  let audio = await prisma.pageAudio.findFirst({
    where: {
      pageId,
      language: targetLanguage,
      s3KeyTranslatedText: { not: null },
    },
  });

  // Verify translation file actually exists in S3 (not just DB record)
  let translationFileExists = false;
  if (audio?.s3KeyTranslatedText) {
    translationFileExists = await fileExistsFlexible(audio.s3KeyTranslatedText);

    // If DB has path but file is missing, clear the DB field
    if (!translationFileExists) {
      logger.warn(
        '[process-page] Translation file missing from S3, will re-translate',
        {
          docId,
          pageId,
          pageNumber,
          s3Key: audio.s3KeyTranslatedText,
        },
      );

      // Clear the path to trigger re-translation
      await prisma.pageAudio.update({
        where: { id: audio.id },
        data: { s3KeyTranslatedText: null },
      });

      audio = null; // Treat as if no translation exists
    }
  }

  if (!audio) {
    // Update progress: starting translation
    await job.updateProgress({ stage: 'translating', percent: 0 });

    // Update page status
    await prisma.page.update({
      where: { id: pageId },
      data: { status: 'translating' },
    });
    // Translate page
    const pageBuffer = await readBufferFlexible(page.s3KeySinglePagePdf);

    // Get previous page for carryover if needed
    let carryoverText: string | null = null;
    if (pageNumber > 1) {
      const prevPage = await prisma.page.findFirst({
        where: {
          documentId: docId,
          pageNumber: pageNumber - 1,
        },
      });

      if (prevPage && !prevPage.endsWithCompleteSentence) {
        const prevAudio = await prisma.pageAudio.findFirst({
          where: {
            pageId: prevPage.id,
            language: targetLanguage,
          },
        });

        if (prevAudio?.s3KeyTranslatedText) {
          try {
            // Read from meta.json to get ORIGINAL language sentences
            const metaDir = path.posix.dirname(prevAudio.s3KeyTranslatedText);
            const metaPath = path.posix.join(metaDir, 'meta.json');
            const metaText = await readTextFlexible(metaPath, 'utf-8');
            const meta = JSON.parse(metaText);

            // Only use carryover if previous page ended mid-sentence
            if (
              meta &&
              meta.endsWithCompleteSentence === false &&
              Array.isArray(meta.sentences) &&
              meta.sentences.length > 0
            ) {
              carryoverText = meta.sentences[meta.sentences.length - 1];
            }
          } catch (err) {
            // Log but don't fail - carryover is enhancement not critical
            logger.warn('[process-page] Could not load carryover text', {
              docId,
              pageNumber,
              error: err,
            });
          }
        }
      }
    }

    const translationResult = await translatePdfPageWithGemini({
      fileBytes: pageBuffer,
      pageNumber,
      targetLanguage,
      carryoverText: carryoverText || undefined,
    });

    // Update page metadata
    await prisma.page.update({
      where: { id: pageId },
      data: {
        isChapterStart: translationResult.isChapterStart || false,
        isSkippableByDefault: translationResult.isSkippable || false,
        endsWithCompleteSentence:
          translationResult.endsWithCompleteSentence ?? true,
      },
    });

    // Save heading detection separately if present
    if (translationResult.detectedHeading) {
      const existingDetection = await prisma.pageHeadingDetection.findFirst({
        where: {
          pageId,
          language: targetLanguage,
        },
      });

      if (existingDetection) {
        await prisma.pageHeadingDetection.update({
          where: { id: existingDetection.id },
          data: {
            detectedHeading: translationResult.detectedHeading,
            headingConfidence: translationResult.headingConfidence || 0,
            modelVersion: translationResult.modelVersion || 'unknown',
          },
        });
      } else {
        await prisma.pageHeadingDetection.create({
          data: {
            documentId: docId,
            pageId,
            language: targetLanguage,
            detectedHeading: translationResult.detectedHeading,
            headingConfidence: translationResult.headingConfidence || 0,
            processingMode: 'worker',
            modelVersion: translationResult.modelVersion || 'unknown',
          },
        });
      }
    }

    // Check again if page is skippable after translation
    if (translationResult.isSkippable) {
      logger.log('[process-page] Page marked as skippable after translation', {
        docId,
        pageId,
        pageNumber,
      });
      return { success: true, skipped: true, docId, pageId, pageNumber };
    }

    // Save translation
    const translationsDir = [
      'documents',
      docId,
      'pages',
      `page-${pageNumber}`,
      'translations',
      targetLanguage,
    ].join('/');

    const sentencesPath = `${translationsDir}/sentences.json`;
    const metaPath = `${translationsDir}/meta.json`;

    await saveTranslatedSentences(
      docId,
      pageNumber,
      targetLanguage,
      translationResult.translatedSentences,
    );

    await saveTranslationMeta(docId, pageNumber, targetLanguage, {
      sentences: translationResult.sentences ?? [],
      endsWithCompleteSentence: translationResult.endsWithCompleteSentence,
      isChapterStart: translationResult.isChapterStart,
      isSkippable: translationResult.isSkippable,
      detectedHeading: translationResult.detectedHeading,
      headingConfidence: translationResult.headingConfidence,
      modelVersion: translationResult.modelVersion,
    });

    // Cleanup page PDF after successful translation (saves ~40-50% storage)
    if (appConfig.cleanupPagePdfsAfterTranslation) {
      await deletePagePdf(docId, pageNumber);
    }

    // Create or update PageAudio record
    const ttsEngine = process.env.VBEE_TOKEN ? 'vbee' : 'gemini';
    const ttsParamsHash = 'default';
    const modelVersion =
      translationResult.modelVersion || 'gemini-2.5-flash-lite';

    audio = await prisma.pageAudio.upsert({
      where: {
        pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
          pageId,
          language: targetLanguage,
          voice,
          ttsEngine,
          ttsParamsHash,
          modelVersion,
        },
      },
      create: {
        pageId,
        language: targetLanguage,
        voice,
        ttsEngine,
        ttsParamsHash,
        modelVersion,
        s3KeyTranslatedText: sentencesPath,
      },
      update: {
        s3KeyTranslatedText: sentencesPath,
      },
    });

    logger.log('[process-page] Translation completed', {
      docId,
      pageId,
      pageNumber,
    });

    // Update progress: translation complete
    await job.updateProgress({ stage: 'translating', percent: 100 });
  } else {
    logger.log('[process-page] Translation already exists, skipping to audio', {
      docId,
      pageId,
      pageNumber,
    });

    // Update progress: skip to audio generation
    await job.updateProgress({ stage: 'generating_audio', percent: 0 });
  }

  // STEP 2: Audio Generation
  logger.log('[process-page] Starting audio generation', {
    docId,
    pageId,
    pageNumber,
  });

  // Update progress and page status
  await job.updateProgress({ stage: 'generating_audio', percent: 0 });
  await prisma.page.update({
    where: { id: pageId },
    data: { status: 'generating_audio' },
  });

  // Ensure audio record exists for the voice
  if (audio.voice !== voice) {
    const ttsEngine = process.env.VBEE_TOKEN ? 'vbee' : 'gemini';
    const ttsParamsHash = 'default';
    const modelVersion = audio.modelVersion || 'gemini-2.5-flash-lite';

    audio = await prisma.pageAudio.upsert({
      where: {
        pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
          pageId,
          language: targetLanguage,
          voice,
          ttsEngine,
          ttsParamsHash,
          modelVersion,
        },
      },
      create: {
        pageId,
        language: targetLanguage,
        voice,
        ttsEngine,
        ttsParamsHash,
        modelVersion,
        s3KeyTranslatedText: audio.s3KeyTranslatedText,
      },
      update: {},
    });
  }

  if (!audio.s3KeyTranslatedText) {
    throw new Error(`No translation found for page ${pageId}`);
  }

  // Build audio paths
  const chunksMetaPath = `${chunksDir}/meta.json`;

  // Check if audio already exists
  if (await fileExistsFlexible(firstChunkPath)) {
    logger.log('[process-page] Audio already exists', {
      docId,
      pageId,
      pageNumber,
    });
    return { success: true, docId, pageId, pageNumber };
  }

  // Prepare TTS text
  const { ttsSentences } = await prepareTtsText(
    audio.s3KeyTranslatedText,
    Boolean(page.endsWithCompleteSentence),
  );

  // Apply chunking strategy
  const useVbee = !!process.env.VBEE_TOKEN;
  const { ttsChunks, chunkModels } = useVbee
    ? applyVbeeChunkingStrategy(ttsSentences)
    : applyTwoTierChunkingStrategy(ttsSentences);

  logger.log('[process-page] TTS chunks prepared', {
    docId,
    pageId,
    pageNumber,
    chunkCount: ttsChunks.length,
  });

  // Map language code
  const languageCode = mapLanguageCode(page.document?.targetLanguage);

  // Write chunks metadata
  await saveFile(
    chunksMetaPath,
    JSON.stringify({ total: ttsChunks.length }, null, 2),
    'application/json',
  );

  // Synthesize all chunks (in background for this job)
  const { firstBytes, firstDurationSec, isAsync, requestId } =
    await synthesizeFirstChunk(
      ttsChunks,
      audio.voice,
      languageCode,
      chunkModels,
      page.pageNumber,
      targetLanguage,
      docId,
      chunksDir,
    );

  // If Vbee async, store request_id for tracking
  if (isAsync && requestId) {
    await prisma.vbeeTtsRequest.create({
      data: {
        requestId,
        chunkId: firstChunkPath,
        pageId,
        status: 'pending',
      },
    });
    logger.info('[process-page] Stored Vbee request_id', {
      requestId,
      chunkId: firstChunkPath,
      pageId,
    });
  }

  // Save first chunk if synchronous
  if (firstBytes) {
    await saveFile(firstChunkPath, firstBytes, 'audio/mpeg');
    logger.log('[process-page] First chunk saved', {
      docId,
      pageId,
      pageNumber,
    });
  }

  // If async (Vbee), wait for chunk 1
  if (isAsync) {
    const maxWaitMs = appConfig.vbeeFirstChunkTimeoutMs;
    const pollIntervalMs = 500;
    const startTime = Date.now();

    logger.log('[process-page] Waiting for Vbee chunk 1...', {
      docId,
      pageId,
      maxWaitMs,
    });

    while (Date.now() - startTime < maxWaitMs) {
      if (await fileExistsFlexible(firstChunkPath)) {
        logger.log('[process-page] Vbee chunk 1 ready', {
          docId,
          pageId,
          waitedMs: Date.now() - startTime,
        });
        break;
      }
      await new Promise((resolve) => setTimeout(resolve, pollIntervalMs));
    }

    if (!(await fileExistsFlexible(firstChunkPath))) {
      logger.warn(
        `[process-page] Webhook timeout after ${maxWaitMs}ms, attempting recovery`,
        { pageId, page: pageNumber },
      );

      // Look up request_id from database
      const vbeeRequest = await prisma.vbeeTtsRequest.findFirst({
        where: { chunkId: firstChunkPath },
        orderBy: { createdAt: 'desc' },
      });

      let audioResult: any = null;

      // Step 1: Try polling with original request_id
      if (vbeeRequest?.requestId) {
        logger.info('[process-page] Step 1: Polling Vbee with request_id', {
          requestId: vbeeRequest.requestId,
        });

        const pollResult = await pollVbeeResult(vbeeRequest.requestId);

        // Check response status
        if (pollResult?.audioLink) {
          logger.info('[process-page] Polling succeeded, downloading audio');
          audioResult = await downloadVbeeAudio(pollResult.audioLink);
        } else if (pollResult?.status) {
          logger.warn(
            '[process-page] Poll returned status:',
            pollResult.status,
          );

          // If status indicates request expired/failed/not-found, try fresh request
          const failedStatuses = ['expired', 'failed', 'not_found', 'error'];
          if (failedStatuses.includes(pollResult.status.toLowerCase())) {
            logger.info(
              '[process-page] Request expired/failed, sending fresh sync request',
            );
            audioResult = null; // Force fallback to Step 2
          }
        }
      }

      // Step 2: If polling failed or request not found, send fresh synchronous request
      if (!audioResult) {
        logger.info(
          '[process-page] Step 2: Sending fresh synchronous Vbee request',
        );

        const firstChunkText = ttsChunks[0];
        audioResult = await ttsViaVbeeSync(
          firstChunkText,
          audio.voice,
          languageCode,
        );
      }

      // Save audio if we got it
      if (audioResult) {
        await saveFile(firstChunkPath, audioResult.audioBytes, 'audio/mpeg');
        logger.log('[process-page] First chunk saved via recovery', {
          docId,
          pageId,
          durationSec: audioResult.durationSec,
        });

        // Update database if we have the original request
        if (vbeeRequest) {
          await prisma.vbeeTtsRequest.update({
            where: { id: vbeeRequest.id },
            data: { status: 'completed' },
          });
        }

        logger.info('[process-page] Recovery succeeded');
      } else {
        // Both polling and sync request failed
        throw new Error(
          'Timeout waiting for Vbee chunk 1 (all recovery attempts failed)',
        );
      }
    }
  }

  // Process remaining chunks in background
  if (ttsChunks.length > 1) {
    logger.log('[process-page] Processing remaining chunks', {
      docId,
      pageId,
      remainingChunks: ttsChunks.length - 1,
    });

    try {
      await runBackgroundSynthesis(
        ttsChunks,
        chunkModels,
        audio.voice,
        languageCode,
        chunksDir,
        chunksMetaPath,
        audio.id,
        page.id,
        docId,
        page.pageNumber,
        audio.language,
        firstDurationSec,
        null, // dbUserId - not tracking usage
      );
      logger.log('[process-page] All chunks completed', {
        docId,
        pageId,
        pageNumber,
      });
    } catch (error) {
      logger.error('[process-page] Background synthesis failed', {
        docId,
        pageId,
        error,
      });
      throw error;
    }
  }

  // Update progress: audio generation complete
  await job.updateProgress({ stage: 'generating_audio', percent: 100 });

  // Update page status to ready
  await prisma.page.update({
    where: { id: pageId },
    data: { status: 'ready' },
  });

  logger.log('[process-page] Job completed successfully', {
    docId,
    pageId,
    pageNumber,
    chunkCount: ttsChunks.length,
  });

  return {
    success: true,
    docId,
    pageId,
    pageNumber,
    chunkCount: ttsChunks.length,
  };
}
