import { Job } from 'bullmq';
import { PDFDocument } from 'pdf-lib';
import { prisma } from '@/lib/prisma';
import { readBufferFlexible } from '@/lib/file-service';
import { analyzeDocumentStructure } from '@/lib/ai/structure-analyzer';
import { translatePdfPageWithGemini } from '@/lib/ai/gemini';
import { saveTranslatedSentences, saveTranslationMeta } from '@/lib/storage';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { DOCUMENT_STATUS } from '@/lib/queue/jobs';
import { logger } from '@/lib/logger';

export type FastAnalyzeAndPrimeJobData = {
  docId: string;
  targetLanguage: string;
  voice: string;
};

/**
 * Fast-track job to minimize time to first audio
 *
 * Flow:
 * 1. Extract first 20 pages as ONE PDF (not individual pages)
 * 2. Ask <PERSON> which page is first meaningful (structure analysis)
 * 3. Extract, translate, and TTS only that specific page
 * 4. Enqueue background job to split all pages
 *
 * This avoids splitting ALL pages before finding first meaningful page,
 * reducing time-to-first-audio from ~80s to ~30s for 50-page PDFs.
 */
export async function fastAnalyzeAndPrimeJob(
  job: Job<FastAnalyzeAndPrimeJobData>,
) {
  const { docId, targetLanguage, voice } = job.data;

  logger.log('[fast-prime] Starting for doc:', docId);

  // 1. Load original PDF
  const doc = await prisma.document.findUnique({
    where: { id: docId },
    include: { pages: { orderBy: { pageNumber: 'asc' } } },
  });

  if (!doc) {
    throw new Error(`Document ${docId} not found`);
  }

  const originalBytes = await readBufferFlexible(doc.s3KeyOriginal!);
  const pdfDoc = await PDFDocument.load(originalBytes);

  // 2. Extract first 20 pages into a SINGLE PDF for structure analysis
  const pagesToAnalyze = Math.min(20, doc.pageCount!);
  const analysisPdf = await PDFDocument.create();

  logger.log('[fast-prime] Extracting first', pagesToAnalyze, 'pages');

  for (let i = 0; i < pagesToAnalyze; i++) {
    const [page] = await analysisPdf.copyPages(pdfDoc, [i]);
    analysisPdf.addPage(page);
  }

  const analysisPdfBytes = await analysisPdf.save();

  // 3. Ask Gemini: "Which page is the first meaningful page?"
  // This is THE ONLY blocking step - everything else waits for this answer
  logger.log('[fast-prime] Analyzing document structure with Gemini');

  const structureResult = await analyzeDocumentStructure({
    pdfBytes: Buffer.from(analysisPdfBytes),
    pageCount: pagesToAnalyze,
  });

  const firstMeaningfulPageNumber = structureResult.suggestedStartPage || 1;

  logger.log(
    '[fast-prime] First meaningful page is:',
    firstMeaningfulPageNumber,
  );

  // 4. Extract ONLY the first meaningful page (not all pages, just this one)
  const singlePagePdf = await PDFDocument.create();
  const [targetPage] = await singlePagePdf.copyPages(pdfDoc, [
    firstMeaningfulPageNumber - 1,
  ]);
  singlePagePdf.addPage(targetPage);
  const targetPageBytes = await singlePagePdf.save();

  // 5. Translate ONLY this page
  logger.log('[fast-prime] Translating page', firstMeaningfulPageNumber);

  const translation = await translatePdfPageWithGemini({
    fileBytes: Buffer.from(targetPageBytes),
    pageNumber: firstMeaningfulPageNumber,
    targetLanguage,
    carryoverText: undefined, // First meaningful page, no carryover
  });

  // 6. Save translation
  const targetPageRecord = doc.pages.find(
    (p) => p.pageNumber === firstMeaningfulPageNumber,
  );

  if (!targetPageRecord) {
    throw new Error(
      `Page record not found for page ${firstMeaningfulPageNumber}`,
    );
  }

  await saveTranslatedSentences(
    docId,
    firstMeaningfulPageNumber,
    targetLanguage,
    translation.translatedSentences || [],
  );

  await saveTranslationMeta(docId, firstMeaningfulPageNumber, targetLanguage, {
    sentences: translation.sentences ?? [],
    endsWithCompleteSentence: translation.endsWithCompleteSentence,
    isChapterStart: translation.isChapterStart,
    isSkippable: translation.isSkippable ?? false,
    detectedHeading: translation.detectedHeading,
    headingConfidence: translation.headingConfidence,
    modelVersion: translation.modelVersion,
  });

  // NOTE: Page PDF was never saved to S3 (exists only in memory),
  // so no cleanup needed. The split-all-pages background job will
  // save and potentially clean up page PDFs later.

  logger.log('[fast-prime] Translation saved');

  // 7. Create PageAudio record (TTS will be generated on-demand via ensure-tts endpoint)
  const modelVersion = translation.modelVersion || 'v1';
  const textPath = `documents/${docId}/pages/page-${firstMeaningfulPageNumber}/translations/${targetLanguage}/sentences.json`;

  await prisma.pageAudio.upsert({
    where: {
      pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
        pageId: targetPageRecord.id,
        language: targetLanguage,
        voice,
        ttsEngine: 'gemini',
        ttsParamsHash: 'default',
        modelVersion,
      },
    },
    update: {
      s3KeyTranslatedText: textPath,
    },
    create: {
      pageId: targetPageRecord.id,
      language: targetLanguage,
      voice,
      ttsEngine: 'gemini',
      ttsParamsHash: 'default',
      modelVersion,
      s3KeyTranslatedText: textPath,
    },
  });

  // 8. Update page status
  await prisma.page.update({
    where: { id: targetPageRecord.id },
    data: {
      status: 'ready',
      endsWithCompleteSentence: translation.endsWithCompleteSentence,
      isChapterStart: translation.isChapterStart,
      isSkippableByDefault: translation.isSkippable ?? false,
    },
  });

  // 9. Update document status and set suggested start page
  await prisma.document.update({
    where: { id: docId },
    data: {
      status: DOCUMENT_STATUS.TRANSLATED_FIRST_PAGE,
      suggestedStartPageId: targetPageRecord.id,
    },
  });

  logger.log(
    '[fast-prime] Document status updated to',
    DOCUMENT_STATUS.TRANSLATED_FIRST_PAGE,
  );

  // 10. Enqueue TTS generation job for first page
  await documentQueue.add(
    JOB_TYPES.GENERATE_PAGE_AUDIO,
    {
      docId,
      pageId: targetPageRecord.id,
      targetLanguage,
      voice,
    },
    { priority: 10 }, // High priority - user is waiting for audio
  );

  logger.log('[fast-prime] Enqueued GENERATE_PAGE_AUDIO job for first page');

  // 11. Enqueue background job to split all pages (low priority, non-blocking)
  await documentQueue.add(
    JOB_TYPES.SPLIT_ALL_PAGES,
    { docId },
    { priority: 5 }, // Low priority - happens in background
  );

  logger.log('[fast-prime] Enqueued SPLIT_ALL_PAGES background job');
  logger.log('[fast-prime] Completed for doc:', docId);
}
