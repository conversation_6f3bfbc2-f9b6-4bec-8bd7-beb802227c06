import { Job } from 'bullmq';
import { PDFDocument } from 'pdf-lib';
import { prisma } from '@/lib/prisma';
import { readBufferFlexible } from '@/lib/file-service';
import { savePagePdf } from '@/lib/storage';
import { logger } from '@/lib/logger';

export type SplitAllPagesJobData = {
  docId: string;
};

/**
 * Background job to split all pages into individual PDFs
 *
 * This runs in the background with low priority after the first page
 * is already available to the user. It ensures all pages are pre-split
 * and saved to S3 for faster on-demand access.
 *
 * If pages are already split, they are skipped (idempotent).
 */
export async function splitAllPagesJob(job: Job<SplitAllPagesJobData>) {
  const { docId } = job.data;

  logger.log('[split-pages] Starting background split for doc:', docId);

  const doc = await prisma.document.findUnique({
    where: { id: docId },
    include: { pages: { orderBy: { pageNumber: 'asc' } } },
  });

  if (!doc) {
    throw new Error(`Document ${docId} not found`);
  }

  const originalBytes = await readBufferFlexible(doc.s3KeyOriginal!);
  const pdfDoc = await PDFDocument.load(originalBytes);

  // Split all pages in batches of 10 for better throughput
  const batchSize = 10;
  const totalPages = doc.pageCount!;
  let splitCount = 0;
  let skippedCount = 0;

  for (let batch = 0; batch < Math.ceil(totalPages / batchSize); batch++) {
    const batchStart = batch * batchSize;
    const batchEnd = Math.min((batch + 1) * batchSize, totalPages);

    logger.log(
      '[split-pages] Processing batch',
      batch + 1,
      `(pages ${batchStart + 1}-${batchEnd})`,
    );

    // Process batch in parallel
    const results = await Promise.allSettled(
      Array.from({ length: batchEnd - batchStart }, async (_, i) => {
        const pageNum = batchStart + i + 1;
        const pageRecord = doc.pages.find((p) => p.pageNumber === pageNum);

        if (!pageRecord) {
          logger.warn('[split-pages] Page record not found for:', pageNum);
          return { skipped: true };
        }

        // Skip if already split
        if (pageRecord.s3KeySinglePagePdf) {
          return { skipped: true };
        }

        // Extract page
        const singlePagePdf = await PDFDocument.create();
        const [page] = await singlePagePdf.copyPages(pdfDoc, [pageNum - 1]);
        singlePagePdf.addPage(page);
        const pageBytes = await singlePagePdf.save();

        // Save to S3
        const s3Key = await savePagePdf(docId, pageNum, Buffer.from(pageBytes));

        // Update DB
        await prisma.page.update({
          where: { id: pageRecord.id },
          data: { s3KeySinglePagePdf: s3Key },
        });

        return { skipped: false };
      }),
    );

    // Count results
    results.forEach((result) => {
      if (result.status === 'fulfilled') {
        if (result.value.skipped) {
          skippedCount++;
        } else {
          splitCount++;
        }
      } else {
        logger.error('[split-pages] Page split failed:', result.reason);
      }
    });
  }

  logger.log(
    '[split-pages] Completed:',
    splitCount,
    'split,',
    skippedCount,
    'skipped',
  );
  logger.log('[split-pages] All pages processed for doc:', docId);
}
