import { Job } from 'bullmq';
import { GeneratePageAudioJobData, DOCUMENT_STATUS } from '@/lib/queue/jobs';
import { prisma } from '@/lib/prisma';
import { appConfig } from '@/lib/config';
import { fileExistsFlexible, saveFile } from '@/lib/file-service';
import { prepareTtsText } from '@/lib/api/page/tts-preparation';
import {
  mapLanguageCode,
  applyTwoTierChunkingStrategy,
  applyVbeeChunkingStrategy,
} from '@/lib/api/page/chunking';
import {
  synthesizeFirstChunk,
  runBackgroundSynthesis,
} from '@/lib/api/page/audio-synthesis';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { logger } from '@/lib/logger';
import { pollVbeeResult, downloadVbeeAudio, ttsViaVbeeSync } from '@/lib/ai/providers/vbee';

/**
 * Job handler: generate-page-audio
 *
 * Responsibilities:
 * 1. Generate audio for a page using TTS
 * 2. Save audio chunks to storage
 * 3. If first page: Update document status to 'generating_audio_first_page' → 'generated_audio_first_page'
 * 4. If other pages: Don't update document status
 */
export async function generatePageAudioJob(job: Job<GeneratePageAudioJobData>) {
  const { docId, pageId, targetLanguage, voice } = job.data;

  logger.log('[generate-page-audio] Starting job', { docId, pageId });

  // Fetch document to check if this is the first page
  const doc = await prisma.document.findUnique({
    where: { id: docId },
    select: { id: true, suggestedStartPageId: true },
  });

  if (!doc) {
    throw new Error(`Document ${docId} not found`);
  }

  const isFirstPage = doc.suggestedStartPageId === pageId;

  // Get page info early for idempotency check
  const pageInfo = await prisma.page.findUnique({
    where: { id: pageId },
    select: { id: true, pageNumber: true },
  });

  if (!pageInfo) {
    throw new Error(`Page ${pageId} not found`);
  }

  // Check if audio already exists (idempotency)
  const chunksDir = [
    'documents',
    docId,
    'pages',
    `page-${pageInfo.pageNumber}`,
    'audio',
    targetLanguage,
    voice,
    'chunks',
  ].join('/');
  const firstChunkPath = `${chunksDir}/chunk-1.mp3`;

  if (await fileExistsFlexible(firstChunkPath)) {
    logger.log('[generate-page-audio] Audio already exists, skipping', {
      docId,
      pageId,
      pageNumber: pageInfo.pageNumber,
    });

    // Ensure page status is ready
    await prisma.page.update({
      where: { id: pageId },
      data: { status: 'ready' },
    });

    return {
      success: true,
      skipped: true,
      docId,
      pageId,
      pageNumber: pageInfo.pageNumber,
    };
  }

  if (isFirstPage) {
    // Update status only for first page
    await prisma.document.update({
      where: { id: docId },
      data: { status: DOCUMENT_STATUS.GENERATING_AUDIO_FIRST_PAGE },
    });
  }

  // Update progress and page status
  await job.updateProgress({ stage: 'generating_audio', percent: 0 });
  await prisma.page.update({
    where: { id: pageId },
    data: { status: 'generating_audio' },
  });

  // Get page and audio record
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: {
      document: true,
      pageAudios: {
        where: {
          language: targetLanguage,
        },
      },
    },
  });

  if (!page) {
    throw new Error(`Page ${pageId} not found`);
  }

  const audio = page.pageAudios.find(
    (a) => a.language === targetLanguage && a.voice === voice,
  );

  if (!audio || !audio.s3KeyTranslatedText) {
    throw new Error(
      `No translation found for page ${pageId} in language ${targetLanguage}`,
    );
  }

  // Prepare TTS text
  const { ttsSentences } = await prepareTtsText(
    audio.s3KeyTranslatedText,
    Boolean(page.endsWithCompleteSentence),
  );

  // Apply chunking strategy
  const useVbee = !!process.env.VBEE_TOKEN;
  const { ttsChunks, chunkModels } = useVbee
    ? applyVbeeChunkingStrategy(ttsSentences)
    : applyTwoTierChunkingStrategy(ttsSentences);

  logger.log('[generate-page-audio] TTS chunks prepared', {
    docId,
    pageId,
    pageNumber: page.pageNumber,
    chunkCount: ttsChunks.length,
  });

  // Build audio paths
  const chunksMetaPath = `${chunksDir}/meta.json`;
  const mp3Path = [
    'documents',
    docId,
    'pages',
    `page-${page.pageNumber}`,
    'audio',
    targetLanguage,
    voice,
    'audio.mp3',
  ].join('/');

  // Map language code
  const languageCode = mapLanguageCode(page.document?.targetLanguage);

  // Write chunks metadata
  try {
    await saveFile(
      chunksMetaPath,
      JSON.stringify({ total: ttsChunks.length }, null, 2),
      'application/json',
    );
  } catch (error) {
    logger.warn('[generate-page-audio] Failed to save chunks meta', {
      error,
    });
  }

  // Synthesize first chunk
  const { firstBytes, firstDurationSec, isAsync, requestId } =
    await synthesizeFirstChunk(
      ttsChunks,
      audio.voice,
      languageCode,
      chunkModels,
      page.pageNumber,
      targetLanguage,
      docId,
      chunksDir,
    );

  const firstPath = `${chunksDir}/chunk-1.mp3`;
  const firstChunkId = firstPath;

  // If Vbee async, store request_id for tracking
  if (isAsync && requestId) {
    await prisma.vbeeTtsRequest.create({
      data: {
        requestId,
        chunkId: firstChunkId,
        pageId,
        status: 'pending',
      },
    });
    logger.info('[generate-page-audio] Stored Vbee request_id', {
      requestId,
      chunkId: firstChunkId,
      pageId,
    });
  }

  // Save first chunk only if it's synchronous (Gemini)
  if (firstBytes) {
    await saveFile(firstPath, firstBytes, 'audio/mpeg');
    logger.log('[generate-page-audio] First chunk saved', {
      docId,
      pageId,
      pageNumber: page.pageNumber,
      durationSec: firstDurationSec,
    });
  }

  // If async (Vbee), wait for chunk 1 to be ready
  if (isAsync) {
    const maxWaitMs = appConfig.vbeeFirstChunkTimeoutMs;
    const pollIntervalMs = 500;
    const startTime = Date.now();

    logger.log('[generate-page-audio] Waiting for Vbee chunk 1...', {
      docId,
      pageId,
      maxWaitMs,
    });

    while (Date.now() - startTime < maxWaitMs) {
      if (await fileExistsFlexible(firstPath)) {
        logger.log('[generate-page-audio] Vbee chunk 1 ready', {
          docId,
          pageId,
          waitedMs: Date.now() - startTime,
        });
        break;
      }
      await new Promise((resolve) => setTimeout(resolve, pollIntervalMs));
    }

    if (!(await fileExistsFlexible(firstPath))) {
      logger.warn(
        `[generate-page-audio] Webhook timeout after ${maxWaitMs}ms, attempting recovery`,
        { pageId, page: page.pageNumber },
      );

      // Look up request_id from database
      const vbeeRequest = await prisma.vbeeTtsRequest.findFirst({
        where: { chunkId: firstChunkId },
        orderBy: { createdAt: 'desc' },
      });

      let audioResult: any = null;

      // Step 1: Try polling with original request_id
      if (vbeeRequest?.requestId) {
        logger.info(
          '[generate-page-audio] Step 1: Polling Vbee with request_id',
          {
            requestId: vbeeRequest.requestId,
          },
        );

        const pollResult = await pollVbeeResult(vbeeRequest.requestId);

        // Check response status
        if (pollResult?.audioLink) {
          logger.info(
            '[generate-page-audio] Polling succeeded, downloading audio',
          );
          audioResult = await downloadVbeeAudio(pollResult.audioLink);
        } else if (pollResult?.status) {
          logger.warn(
            '[generate-page-audio] Poll returned status:',
            pollResult.status,
          );

          // If status indicates request expired/failed/not-found, try fresh request
          const failedStatuses = ['expired', 'failed', 'not_found', 'error'];
          if (failedStatuses.includes(pollResult.status.toLowerCase())) {
            logger.info(
              '[generate-page-audio] Request expired/failed, sending fresh sync request',
            );
            audioResult = null; // Force fallback to Step 2
          }
        }
      }

      // Step 2: If polling failed or request not found, send fresh synchronous request
      if (!audioResult) {
        logger.info(
          '[generate-page-audio] Step 2: Sending fresh synchronous Vbee request',
        );

        const firstChunkText = ttsChunks[0];
        audioResult = await ttsViaVbeeSync(
          firstChunkText,
          audio.voice,
          languageCode,
        );
      }

      // Save audio if we got it
      if (audioResult) {
        await saveFile(firstPath, audioResult.audioBytes, 'audio/mpeg');
        logger.log('[generate-page-audio] First chunk saved via recovery', {
          docId,
          pageId,
          durationSec: audioResult.durationSec,
        });

        // Update database if we have the original request
        if (vbeeRequest) {
          await prisma.vbeeTtsRequest.update({
            where: { id: vbeeRequest.id },
            data: { status: 'completed' },
          });
        }

        logger.info('[generate-page-audio] Recovery succeeded');
      } else {
        // Both polling and sync request failed
        throw new Error(
          'Timeout waiting for Vbee chunk 1 (all recovery attempts failed)',
        );
      }
    }
  }

  if (isFirstPage) {
    // Update document status to 'generated_audio_first_page' (only for first page)
    await prisma.document.update({
      where: { id: docId },
      data: { status: DOCUMENT_STATUS.GENERATED_AUDIO_FIRST_PAGE },
    });

    logger.log(
      '[generate-page-audio] Document status updated to generated_audio_first_page',
      {
        docId,
      },
    );
  }

  // Continue processing remaining chunks
  if (ttsChunks.length > 1) {
    logger.log('[generate-page-audio] Processing remaining chunks', {
      docId,
      pageId,
      remainingChunks: ttsChunks.length - 1,
    });

    try {
      await runBackgroundSynthesis(
        ttsChunks,
        chunkModels,
        audio.voice,
        languageCode,
        chunksDir,
        chunksMetaPath,
        audio.id,
        page.id,
        docId,
        page.pageNumber,
        audio.language,
        firstDurationSec,
        null, // dbUserId - not tracking usage during background processing
      );
      logger.log('[generate-page-audio] All chunks completed', {
        docId,
        pageId,
      });
    } catch (error) {
      logger.error('[generate-page-audio] Background synthesis failed', {
        docId,
        pageId,
        error,
      });
      throw error;
    }
  }

  // Update progress: audio generation complete
  await job.updateProgress({ stage: 'generating_audio', percent: 100 });

  // Update page status to ready
  await prisma.page.update({
    where: { id: pageId },
    data: { status: 'ready' },
  });

  logger.log('[generate-page-audio] Job completed successfully', {
    docId,
    pageId,
    pageNumber: page.pageNumber,
    isFirstPage,
    chunkCount: ttsChunks.length,
  });

  return {
    success: true,
    docId,
    pageId,
    pageNumber: page.pageNumber,
    chunkCount: ttsChunks.length,
  };
}
