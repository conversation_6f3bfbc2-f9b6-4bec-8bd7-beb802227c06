import FingerprintJS from '@fingerprintjs/fingerprintjs';

// Client-side anonymous identity using FingerprintJS (npm) with fallback.
// Generates and persists a stable ID in localStorage under 'transread_anon_id'.
export type AnonCandidates = {
  stableId: string;
  fpjsId?: string;
  coarseId?: string;
};

export async function getAnonCandidates(): Promise<AnonCandidates | null> {
  if (typeof window === 'undefined') return null;
  const KEY = 'transread_anon_id';
  const KEY_FP = 'transread_anon_fp_id';
  const KEY_COARSE = 'transread_anon_coarse_id';
  try {
    const existing = localStorage.getItem(KEY);
    const fpExisting = localStorage.getItem(KEY_FP) || undefined;
    const coarseExisting = localStorage.getItem(KEY_COARSE) || undefined;
    if (existing && typeof existing === 'string' && existing.length > 0) {
      return {
        stableId: existing,
        fpjsId: fpExisting || undefined,
        coarseId: coarseExisting || undefined,
      };
    }
  } catch {}

  // Try to load FingerprintJS from npm package (client-side only)
  let fpjsId: string | undefined;
  try {
    if (
      typeof window !== 'undefined' &&
      FingerprintJS &&
      typeof FingerprintJS.load === 'function'
    ) {
      const fp = await FingerprintJS.load();
      const result: any = await fp.get({ extendedResult: true } as any);
      fpjsId = result?.visitorId || undefined;
      if (fpjsId) {
        try {
          localStorage.setItem(KEY_FP, fpjsId);
        } catch {}
      }
      // Build a coarse, cross-context ID from stable-ish components
      try {
        const c = result?.components || {};
        const coarseRaw = [
          c.platform?.value,
          c.osCpu?.value,
          c.hardwareConcurrency?.value,
          c.deviceMemory?.value,
          c.screenResolution?.value?.join?.('x') || c.screenResolution?.value,
          c.colorDepth?.value,
          c.timezone?.value,
          c.vendor?.value,
          c.vendorFlavors?.value?.join?.('-') || c.vendorFlavors?.value,
          (navigator as any)?.language,
          ((navigator as any)?.languages || [])?.join('-'),
        ].join('|');
        const coarseId = simpleHash(`coarse|${coarseRaw}`);
        if (coarseId) {
          try {
            localStorage.setItem(KEY_COARSE, coarseId);
          } catch {}
        }
      } catch {}
    }
  } catch {}

  // Fallback: generate a coarse, storage-agnostic ID from stable UA/device traits only
  let coarseId: string | undefined;
  try {
    const ua = navigator.userAgent || '';
    const lang = navigator.language || '';
    const tz = Intl.DateTimeFormat().resolvedOptions().timeZone || '';
    const scr = `${window.screen?.width || 0}x${window.screen?.height || 0}x${window.screen?.colorDepth || 0}`;
    const vendor = (navigator as any)?.vendor || '';
    const plat = (navigator as any)?.platform || '';
    const hwc = (navigator as any)?.hardwareConcurrency || '';
    const mem = (navigator as any)?.deviceMemory || '';
    const langs = ((navigator as any)?.languages || [])?.join('-');
    const raw = `coarse|${ua}|${lang}|${langs}|${tz}|${scr}|${vendor}|${plat}|${hwc}|${mem}`;
    coarseId = simpleHash(raw);
    try {
      localStorage.setItem(KEY_COARSE, coarseId);
    } catch {}
  } catch {
    // absolute last resort
    coarseId = `${Date.now().toString(36)}-${Math.random().toString(36).slice(2)}`;
    try {
      localStorage.setItem(KEY_COARSE, coarseId);
    } catch {}
  }

  // Choose a stable ID: prefer FingerprintJS ID if present; otherwise coarse
  // Keep FingerprintJS ID as a candidate, but prefer coarse ID for cross-context stability
  const stableId = fpjsId || coarseId || '';
  try {
    if (stableId) localStorage.setItem(KEY, stableId);
  } catch {}
  if (!stableId) return null;
  // Persist cookies for server-side authorization fallback
  try {
    const ids = Array.from(
      new Set([stableId, fpjsId, coarseId].filter(Boolean) as string[]),
    );
    const maxAge = 60 * 60 * 24 * 365; // 1 year
    document.cookie = `tr_anon_sid=${encodeURIComponent(
      stableId,
    )}; Path=/; Max-Age=${maxAge}; SameSite=Lax`;
    document.cookie = `tr_anon_sids=${encodeURIComponent(
      ids.join(','),
    )}; Path=/; Max-Age=${maxAge}; SameSite=Lax`;
  } catch {}
  return { stableId, fpjsId, coarseId };
}

// Backward-compatible helper used by older code; returns chosen stable ID
export async function getAnonId(): Promise<string | null> {
  const c = await getAnonCandidates();
  return c?.stableId || null;
}

function simpleHash(str: string): string {
  let h1 = 0x811c9dc5;
  for (let i = 0; i < str.length; i++) {
    h1 ^= str.charCodeAt(i);
    h1 =
      (h1 + ((h1 << 1) + (h1 << 4) + (h1 << 7) + (h1 << 8) + (h1 << 24))) >>> 0;
  }
  // format as base36
  return `fp_${h1.toString(36)}`;
}
