import { redisConnection } from '@/lib/queue/config';
import { logger } from '@/lib/logger';
import { appConfig } from '@/lib/config';

/**
 * Redis-based file existence cache
 *
 * Reduces S3 HEAD request costs by caching file existence checks.
 *
 * Features:
 * - Automatic TTL expiration (configurable via lib/config.ts)
 * - Graceful degradation on Redis errors
 * - Feature flag support
 * - Cache invalidation on file writes
 *
 * Expected impact: 90% reduction in S3 HEAD requests
 */

const CACHE_PREFIX = 'file:exists:';
const DEFAULT_TTL_SECONDS = appConfig.fileExistenceCacheTtlSeconds;
const CHUNK_1_TTL_SECONDS = 30; // Frequently polled
const NON_EXISTENT_TTL_SECONDS = 10; // Allow faster detection during generation

interface CacheStats {
  hits: number;
  misses: number;
  errors: number;
  enabled: boolean;
}

class FileExistenceCache {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    errors: 0,
    enabled: true,
  };

  constructor() {
    // Check feature flag from config
    if (!appConfig.enableFileCache) {
      this.stats.enabled = false;
      logger.log(
        '[FileCache] Cache disabled via appConfig.enableFileCache=false',
      );
    }
  }

  /**
   * Get cache key for a file pointer
   */
  private getCacheKey(pointer: string): string {
    return `${CACHE_PREFIX}${pointer}`;
  }

  /**
   * Determine appropriate TTL for a file
   */
  private getTTL(pointer: string, exists: boolean): number {
    // Non-existent files: short TTL to detect generation quickly
    if (!exists) {
      return NON_EXISTENT_TTL_SECONDS;
    }

    // chunk-1.mp3: frequently polled, moderate TTL
    if (pointer.includes('chunk-1.mp3')) {
      return CHUNK_1_TTL_SECONDS;
    }

    // Regular files: default TTL
    return DEFAULT_TTL_SECONDS;
  }

  /**
   * Check if a file exists in cache
   *
   * @returns true if exists, false if doesn't exist, null if cache miss
   */
  async get(pointer: string): Promise<boolean | null> {
    if (!this.stats.enabled) {
      return null;
    }

    try {
      const key = this.getCacheKey(pointer);
      const value = await redisConnection.get(key);

      if (value === null) {
        this.stats.misses++;
        return null; // Cache miss
      }

      this.stats.hits++;
      return value === '1'; // '1' = exists, '0' = doesn't exist
    } catch (error) {
      this.stats.errors++;
      logger.warn('[FileCache] Error reading cache, falling back to S3:', {
        pointer,
        error: (error as Error)?.message,
      });
      return null; // Graceful degradation
    }
  }

  /**
   * Cache a file existence result
   *
   * @param pointer File path
   * @param exists Whether the file exists
   * @param ttl Optional TTL in seconds (overrides automatic TTL selection)
   */
  async set(pointer: string, exists: boolean, ttl?: number): Promise<void> {
    if (!this.stats.enabled) {
      return;
    }

    try {
      const key = this.getCacheKey(pointer);
      const value = exists ? '1' : '0';
      const actualTTL = ttl ?? this.getTTL(pointer, exists);

      await redisConnection.setex(key, actualTTL, value);

      logger.log('[FileCache] Cached:', {
        pointer: pointer.substring(0, 80), // Truncate long paths
        exists,
        ttl: actualTTL,
      });
    } catch (error) {
      this.stats.errors++;
      logger.warn('[FileCache] Error writing cache (non-fatal):', {
        pointer,
        error: (error as Error)?.message,
      });
      // Non-fatal - don't throw
    }
  }

  /**
   * Invalidate a single file from cache
   *
   * Called after file writes to ensure cache freshness
   */
  async delete(pointer: string): Promise<void> {
    if (!this.stats.enabled) {
      return;
    }

    try {
      const key = this.getCacheKey(pointer);
      await redisConnection.del(key);

      logger.log('[FileCache] Invalidated:', {
        pointer: pointer.substring(0, 80),
      });
    } catch (error) {
      this.stats.errors++;
      logger.warn('[FileCache] Error deleting cache (non-fatal):', {
        pointer,
        error: (error as Error)?.message,
      });
      // Non-fatal - don't throw
    }
  }

  /**
   * Invalidate multiple files matching a pattern
   *
   * Example: invalidatePattern('documents/abc123/*')
   *
   * Note: Uses SCAN for safety (doesn't block Redis)
   */
  async invalidatePattern(pattern: string): Promise<number> {
    if (!this.stats.enabled) {
      return 0;
    }

    try {
      const fullPattern = `${CACHE_PREFIX}${pattern}`;
      let cursor = '0';
      let deletedCount = 0;

      do {
        // Use SCAN to avoid blocking Redis
        const [nextCursor, keys] = await redisConnection.scan(
          cursor,
          'MATCH',
          fullPattern,
          'COUNT',
          100,
        );

        cursor = nextCursor;

        if (keys.length > 0) {
          await redisConnection.del(...keys);
          deletedCount += keys.length;
        }
      } while (cursor !== '0');

      if (deletedCount > 0) {
        logger.log('[FileCache] Invalidated pattern:', {
          pattern,
          count: deletedCount,
        });
      }

      return deletedCount;
    } catch (error) {
      this.stats.errors++;
      logger.warn('[FileCache] Error invalidating pattern (non-fatal):', {
        pattern,
        error: (error as Error)?.message,
      });
      return 0;
    }
  }

  /**
   * Get cache statistics for monitoring
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get hit rate percentage
   */
  getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    if (total === 0) return 0;
    return (this.stats.hits / total) * 100;
  }

  /**
   * Reset statistics (for testing)
   */
  resetStats(): void {
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.stats.errors = 0;
  }
}

// Singleton instance
export const fileExistenceCache = new FileExistenceCache();
