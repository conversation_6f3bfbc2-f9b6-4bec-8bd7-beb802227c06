'use client';

let loading: Promise<void> | null = null;

function loadScript(
  siteKey: string,
  mode: 'classic' | 'enterprise',
): Promise<void> {
  if (typeof window === 'undefined') return Promise.resolve();
  if (window.grecaptcha) return Promise.resolve();
  if (loading) return loading;
  loading = new Promise<void>((resolve, reject) => {
    try {
      const s = document.createElement('script');
      const base =
        mode === 'enterprise'
          ? 'https://www.google.com/recaptcha/enterprise.js'
          : 'https://www.google.com/recaptcha/api.js';
      s.src = `${base}?render=${encodeURIComponent(siteKey)}`;
      s.async = true;
      s.defer = true;
      s.onload = () => resolve();
      s.onerror = () => reject(new Error('Failed to load reCAPTCHA script'));
      document.head.appendChild(s);
    } catch (e) {
      reject(e as any);
    }
  });
  return loading;
}

export async function getRecaptchaToken(
  action: string,
): Promise<string | null> {
  try {
    const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY as
      | string
      | undefined;
    const modeEnv = (process.env.RECAPTCHA_MODE || 'classic').toLowerCase();
    const mode: 'classic' | 'enterprise' =
      modeEnv === 'enterprise' ? 'enterprise' : 'classic';
    if (!siteKey) return null;
    await loadScript(siteKey, mode);
    // Wait for grecaptcha to be ready
    await new Promise<void>((resolve) => {
      const gr = window.grecaptcha as any;
      const ready = mode === 'enterprise' ? gr?.enterprise?.ready : gr?.ready;
      if (!ready) return resolve();
      try {
        ready(() => resolve());
      } catch {
        resolve();
      }
    });
    const gr = window.grecaptcha as any;
    const exec = mode === 'enterprise' ? gr?.enterprise?.execute : gr?.execute;
    const token: string = await exec(siteKey, { action });
    return token || null;
  } catch {
    return null;
  }
}
