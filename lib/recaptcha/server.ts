import type { NextRequest } from 'next/server';
import { RecaptchaEnterpriseServiceClient } from '@google-cloud/recaptcha-enterprise';
import { base64Decode } from '../string';
import { logger } from '@/lib/logger';

export type RecaptchaVerifyResult = {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  errorCodes?: string[];
  skipped?: boolean;
};

/**
 * Verify a reCAPTCHA v3 token on the server.
 * If RECAPTCHA_SECRET_KEY is not set, returns { success: true, skipped: true } to ease local dev.
 */
export async function verifyRecaptcha(
  token: string | null | undefined,
  expectedAction?: string,
  remoteIp?: string | null,
): Promise<RecaptchaVerifyResult> {
  logger.log('[recaptcha] Verifying token...');
  const mode = String(process.env.RECAPTCHA_MODE || 'classic').toLowerCase();
  const min = Number(process.env.RECAPTCHA_MIN_SCORE || '0.5');
  if (!token || typeof token !== 'string') {
    return { success: false, errorCodes: ['missing-input-response'] };
  }
  const secret = process.env.RECAPTCHA_SECRET_KEY;
  const canClassic = !!secret && (mode === 'classic' || mode === '');
  const projectId = process.env.RECAPTCHA_ENTERPRISE_PROJECT_ID;
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
  const clientEmail = process.env.RECAPTCHA_ENTERPRISE_CLIENT_EMAIL;
  const privateKey = process.env.RECAPTCHA_ENTERPRISE_PRIVATE_KEY;
  const canEnterprise =
    !!projectId &&
    !!siteKey &&
    !!clientEmail &&
    !!privateKey &&
    mode === 'enterprise';

  if (!canClassic && !canEnterprise) {
    return { success: true, skipped: true };
  }
  // Try enterprise first if configured
  if (canEnterprise) {
    logger.log('[recaptcha] Verifying enterprise token...');
    try {
      const client = getEnterpriseClient();
      const parent = client.projectPath(projectId!);
      const [response] = await client.createAssessment({
        parent,
        assessment: { event: { token, siteKey } },
      } as any);
      const valid = Boolean(response?.tokenProperties?.valid);
      const action = (response?.tokenProperties?.action as string) || undefined;
      const score = Number(response?.riskAnalysis?.score ?? 0);
      const passesScore = !Number.isFinite(min) || score >= min;
      const matchesAction = expectedAction ? action === expectedAction : true;
      const invalidReason = response?.tokenProperties?.invalidReason as
        | string
        | undefined;
      const reasons: string[] = Array.isArray(response?.riskAnalysis?.reasons)
        ? (response!.riskAnalysis!.reasons as any[]).map(String)
        : [];
      return {
        success: valid && passesScore && matchesAction,
        score,
        action,
        errorCodes: valid
          ? undefined
          : [invalidReason || 'invalid-token', ...reasons],
      };
    } catch {
      // Fall through to classic
    }
  }
  if (canClassic) {
    logger.log('[recaptcha] Verifying classic token...');
    try {
      const params = new URLSearchParams();
      params.set('secret', secret!);
      params.set('response', token);
      if (remoteIp) params.set('remoteip', remoteIp);
      const resp = await fetch(
        'https://www.google.com/recaptcha/api/siteverify',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: params.toString(),
          cache: 'no-store',
        },
      );
      const data = (await resp.json()) as any;
      const ok = Boolean(data?.success);
      const score = Number(data?.score ?? 0);
      const action = (data?.action as string) || undefined;
      const passesScore = !Number.isFinite(min) || score >= min;
      const matchesAction = expectedAction ? action === expectedAction : true;
      return {
        success: ok && passesScore && matchesAction,
        score,
        action,
        challenge_ts: data?.challenge_ts,
        hostname: data?.hostname,
        errorCodes: Array.isArray(data?.['error-codes'])
          ? (data?.['error-codes'] as string[])
          : undefined,
      };
    } catch {}
  }
  return { success: false, errorCodes: ['verification-failed'] };
}

let enterpriseClient: RecaptchaEnterpriseServiceClient | null = null;
function getEnterpriseClient(): RecaptchaEnterpriseServiceClient {
  if (!enterpriseClient) {
    const clientEmail = process.env.RECAPTCHA_ENTERPRISE_CLIENT_EMAIL;
    const privateKey = process.env.RECAPTCHA_ENTERPRISE_PRIVATE_KEY;
    if (!clientEmail || !privateKey) {
      throw new Error(
        'Missing GOOGLE_CLIENT_EMAIL or GOOGLE_PRIVATE_KEY environment variables',
      );
    }
    enterpriseClient = new RecaptchaEnterpriseServiceClient({
      credentials: {
        client_email: clientEmail,
        private_key: base64Decode(privateKey),
      },
    });
  }
  return enterpriseClient;
}

/** Extract client IP from a Next.js Request-like object. */
export function getClientIp(req: Request | NextRequest): string | null {
  try {
    // Standard headers first
    const h = (req as any).headers as Headers | undefined;
    const xff = h?.get('x-forwarded-for');
    if (xff) {
      const ip = xff.split(',')[0]?.trim();
      if (ip) return ip;
    }
    const xrip = h?.get('x-real-ip');
    if (xrip) return xrip;
  } catch {}
  return null;
}
