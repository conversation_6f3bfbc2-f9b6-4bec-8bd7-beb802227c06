import { prisma } from './prisma';
import { getUserCycleListeningSeconds, getUserPlanLimitSeconds } from './usage';

/**
 * Check if a user can upload a new document based on their plan limits
 */
export async function canUploadDocument(userId: string): Promise<{
  allowed: boolean;
  reason?: string;
  currentCount?: number;
  maxCount?: number;
}> {
  if (!userId) {
    return { allowed: false, reason: 'user_not_found' };
  }

  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { plan: true },
  });

  if (!user) {
    return { allowed: false, reason: 'user_not_found' };
  }

  // Check if plan has document limit per cycle
  const maxDocs = user.plan?.maxDocumentsPerCycle;

  // null or undefined means unlimited
  if (maxDocs == null) {
    return { allowed: true };
  }

  // Count documents in current billing cycle
  const cycleStart = user.billingPeriodStart || new Date();
  const currentCount = await prisma.document.count({
    where: {
      userId,
      createdAt: {
        gte: cycleStart,
      },
    },
  });

  if (currentCount >= maxDocs) {
    return {
      allowed: false,
      reason: 'document_limit_reached',
      currentCount,
      maxCount: maxDocs,
    };
  }

  return { allowed: true, currentCount, maxCount: maxDocs };
}

/**
 * Check if a user can generate new audio based on their plan listening time limits
 */
export async function canGenerateAudio(userId: string): Promise<{
  allowed: boolean;
  reason?: string;
  usedSeconds?: number;
  limitSeconds?: number;
  remainingSeconds?: number;
}> {
  if (!userId) {
    return { allowed: false, reason: 'user_not_found' };
  }

  const limitSeconds = await getUserPlanLimitSeconds(userId);

  // null means unlimited
  if (limitSeconds == null) {
    return { allowed: true };
  }

  const usedSeconds = await getUserCycleListeningSeconds(userId);
  const remainingSeconds = Math.max(0, limitSeconds - usedSeconds);

  if (remainingSeconds <= 0) {
    return {
      allowed: false,
      reason: 'plan_limit_reached',
      usedSeconds,
      limitSeconds,
      remainingSeconds: 0,
    };
  }

  return {
    allowed: true,
    usedSeconds,
    limitSeconds,
    remainingSeconds,
  };
}

/**
 * Check anonymous user's free trial limit for a specific document
 */
export async function checkAnonymousFreeTrialLimit(
  documentId: string,
  freeTrialSeconds: number,
): Promise<{
  allowed: boolean;
  reason?: string;
  usedSeconds?: number;
  limitSeconds?: number;
}> {
  if (!isFinite(freeTrialSeconds) || freeTrialSeconds <= 0) {
    return { allowed: true };
  }

  const produced = await prisma.pageAudio.findMany({
    where: {
      page: { documentId },
      s3KeyAudioMp3: { not: null },
    },
    select: { duration: true },
  });

  const totalSec = produced.reduce((sum, a) => sum + (a.duration || 0), 0);

  if (totalSec >= freeTrialSeconds) {
    return {
      allowed: false,
      reason: 'free_trial_reached',
      usedSeconds: totalSec,
      limitSeconds: freeTrialSeconds,
    };
  }

  return {
    allowed: true,
    usedSeconds: totalSec,
    limitSeconds: freeTrialSeconds,
  };
}
