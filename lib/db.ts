import { prisma } from './prisma';
import type { Prisma } from '@prisma/client';
import { readTextFlexible } from './file-service';

export async function createDocument(params: {
  userId: string | null;
  sessionId: string | null;
  originalFilename: string;
  contentType: string;
  sizeBytes: number;
  pageCount?: number;
  targetLanguage: string;
  voice: string;
  status: string;
  authors: string | null;
  hasFullAudio: boolean;
  tocSource?: string;
}) {
  return prisma.document.create({ data: params });
}

export async function getDocument(id: string) {
  return prisma.document.findUnique({ where: { id } });
}

export async function updateDocument(
  id: string,
  patch: Prisma.DocumentUpdateInput,
) {
  return prisma.document.update({ where: { id }, data: patch });
}

export async function seedPagesForDocument(documentId: string, count: number) {
  const data = Array.from({ length: count }).map((_, i) => ({
    documentId,
    pageNumber: i + 1,
    status: 'pending',
  }));
  await prisma.page.createMany({ data, skipDuplicates: true });
  return prisma.page.findMany({
    where: { documentId },
    orderBy: { pageNumber: 'asc' },
  });
}

export async function getPages(documentId: string) {
  return prisma.page.findMany({
    where: { documentId },
    orderBy: { pageNumber: 'asc' },
  });
}

export async function setChapters(
  documentId: string,
  items: { name: string; startPageNumber?: number; level?: number }[],
) {
  // map pageNumber -> pageId
  const pages = await prisma.page.findMany({ where: { documentId } });
  const byNum = new Map(pages.map((p) => [p.pageNumber, p] as const));
  await prisma.chapter.deleteMany({ where: { documentId } });
  const created = [] as any[];
  let order = 0;
  for (const it of items) {
    const start = it.startPageNumber
      ? byNum.get(it.startPageNumber)
      : undefined;
    const startPageId = start?.id ?? pages[0]?.id;
    if (!startPageId) continue;
    const ch = await prisma.chapter.create({
      data: {
        documentId,
        name: it.name,
        startPageId: startPageId,
        order: order++,
        level: it.level ?? 1,
        source: 'pdf_outline',
        confidence: 1.0,
      },
    });
    created.push(ch);
  }
  return created;
}

export async function getChapters(documentId: string) {
  return prisma.chapter.findMany({
    where: { documentId },
    orderBy: { order: 'asc' },
  });
}

export async function addPageAudio(params: {
  pageId: string;
  language: string;
  voice: string;
  ttsEngine: string;
  ttsParamsHash: string;
  modelVersion: string;
  s3KeyTranslatedText?: string | null;
  s3KeyAudioMp3?: string | null;
  duration?: number | null;
}) {
  return prisma.pageAudio.create({ data: params });
}

/**
 * @deprecated No longer used - translations are loaded on-demand during TTS generation only
 * This function was causing unnecessary S3 reads for all pages on every document load
 */
export async function getTranslations(documentId: string) {
  // Read PageAudio with translated text pointers and load their content for UI
  const audios = await prisma.pageAudio.findMany({
    where: { page: { documentId } },
    include: { page: true },
  });
  const results: { pageId: string; translatedSentences: string[] }[] = [];
  for (const a of audios) {
    if (a.s3KeyTranslatedText) {
      try {
        const raw = await readTextFlexible(a.s3KeyTranslatedText, 'utf-8');
        const arr = JSON.parse(raw);
        if (Array.isArray(arr)) {
          results.push({
            pageId: a.pageId,
            translatedSentences: arr.map((s: any) => String(s)),
          });
        }
      } catch {}
    }
  }
  return results;
}
