generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String    @id @default(cuid())
  clerkUserId          String    @unique
  email                String?
  name                 String?
  planKey              String    @default("free")
  plan                 Plan      @relation(fields: [planKey], references: [key])
  subscriptionStatus   String    @default("inactive") // active, past_due, canceled
  stripeCustomerId     String?
  paypalPayerId        String?
  creditBalance        Int       @default(0)
  billingPeriodStart   DateTime?
  billingPeriodEnd     DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  documents            Document[]
  usage                 Usage[]
  creditLedger          CreditLedger[]
  paymentEvents         PaymentEvent[]

  @@map("users")
}

model Plan {
  key                   String    @id
  name                  String
  priceMonthlyCents     Int
  maxDocumentsPerCycle  Int?
  includedPageCredits   Int?
  includedListeningTime Int?      // (seconds) for time-based model
  description           String
  isActive              Boolean   @default(true)
  users                 User[]

  @@map("plans")
}

model Usage {
  id                   String    @id @default(cuid())
  userId               String
  cycleStart           DateTime
  cycleEnd             DateTime
  documentsProcessed   Int       @default(0)
  pagesProcessed       Int       @default(0)
  listeningTime        Int       @default(0) // (seconds) time-based tracking
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, cycleStart])
  @@map("usage")
}

model CreditLedger {
  id                   String    @id @default(cuid())
  userId               String
  change               Int       // positive = credit, negative = debit
  reason               String    // "purchase", "usage", "refund", "bonus"
  description          String?
  meta                 Json?     // additional context
  createdAt            DateTime  @default(now())

  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, createdAt])
  @@map("credit_ledger")
}

model PaymentEvent {
  id                   String    @id @default(cuid())
  userId               String
  provider             String    // "stripe" | "paypal"
  type                 String    // "subscription_created", "payment_succeeded", etc.
  amount               Int?      // cents
  currency             String?
  status               String
  externalId           String    // Stripe/PayPal transaction ID
  raw                  Json      // full webhook payload
  createdAt            DateTime  @default(now())

  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([provider, externalId])
  @@map("payment_events")
}

model Document {
  id                    String     @id @default(cuid())
  userId                String?    // null for anonymous
  sessionId             String?    // for anonymous users
  originalFilename      String
  authors               String?    // comma-separated
  contentType           String
  sizeBytes             Int
  s3KeyOriginal         String
  pageCount             Int?
  hasFullAudio          Boolean    @default(false)
  targetLanguage        String?    // target language
  voice                 String?    // male/female
  status                String     @default("uploaded") // uploaded|splitting|processing|ready|failed
  suggestedStartPageId  String?
  tocSource             String?    // pdf_outline|ai_inferred|hybrid|none
  createdAt             DateTime   @default(now())
  updatedAt             DateTime   @updatedAt
  expiresAt             DateTime?  // for anonymous uploads

  user                  User?      @relation(fields: [userId], references: [id], onDelete: Cascade)
  suggestedStartPage    Page?      @relation("DocumentSuggestedStartPage", fields: [suggestedStartPageId], references: [id])
  pages                 Page[]     @relation("DocumentPages")
  chapters              Chapter[]
  headingDetections     PageHeadingDetection[]

  @@index([userId, createdAt])
  @@index([sessionId]) // for anonymous
  @@index([status])
  @@index([expiresAt]) // cleanup job
  @@index([suggestedStartPageId])
  @@map("documents")
}

model Page {
  id                       String     @id @default(cuid())
  documentId               String
  pageNumber               Int
  s3KeySinglePagePdf       String
  charCount                Int?
  charCountTranslated      Int?
  endsWithCompleteSentence Boolean    @default(true)
  isChapterStart           Boolean    @default(false)
  isSkippableByDefault     Boolean    @default(false)
  textSource               String?
  estimatedDuration        Int?
  status                   String     @default("pending") // pending|processing|ready|failed
  role                     String     @default("unknown") // front_matter|toc|content|appendix|index|unknown
  createdAt                DateTime   @default(now())
  updatedAt                DateTime   @updatedAt

  document                 Document   @relation("DocumentPages", fields: [documentId], references: [id], onDelete: Cascade)
  pageAudios               PageAudio[]
  headingDetections        PageHeadingDetection[]
  suggestedStartForDocuments Document[] @relation("DocumentSuggestedStartPage")
  chaptersStartingHere     Chapter[]  @relation("ChapterStartPage")
  chaptersEndingHere       Chapter[]  @relation("ChapterEndPage")

  @@unique([documentId, pageNumber])
  @@index([documentId, role])
  @@index([status])
  @@map("pages")
}

model Chapter {
  id                     String     @id @default(cuid())
  documentId             String
  name                   String
  startPageId            String
  endPageId              String?
  parentId               String?
  order                  Int
  level                  Int        @default(1)
  source                 String
  confidence             Float      @default(1.0)
  createdAt              DateTime   @default(now())

  document               Document   @relation(fields: [documentId], references: [id], onDelete: Cascade)
  parent                 Chapter?   @relation("ChapterHierarchy", fields: [parentId], references: [id])
  children               Chapter[]  @relation("ChapterHierarchy")
  startPage              Page       @relation("ChapterStartPage", fields: [startPageId], references: [id])
  endPage                Page?      @relation("ChapterEndPage", fields: [endPageId], references: [id])

  @@index([documentId, order])
  @@index([parentId])
  @@index([startPageId])
  @@index([endPageId])
  @@map("chapters")
}

model PageHeadingDetection {
  id                     String     @id @default(cuid())
  documentId             String
  pageId                 String
  language               String
  detectedHeading        String?
  headingConfidence      Float      @default(0.0)
  processingMode         String     // traditional|ai_first
  modelVersion           String?
  createdAt              DateTime   @default(now())

  document               Document   @relation(fields: [documentId], references: [id], onDelete: Cascade)
  page                   Page       @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@index([pageId, language])
  @@index([documentId])
  @@map("page_heading_detections")
}

model PageAudio {
  id                   String    @id @default(cuid())
  pageId               String
  language             String    // ISO 639-1 (en, vi, etc.)
  voice                String    // male|female
  ttsEngine            String    // gemini|openai|elevenlabs
  ttsParamsHash        String    // hash of TTS parameters
  translatedTextHash   String?
  modelVersion         String    // AI model version for caching
  s3KeyTranslatedText  String?
  s3KeyAudioMp3        String?
  duration             Int?      // seconds
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  page                 Page      @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@unique([pageId, language, voice, ttsEngine, ttsParamsHash, modelVersion])
  @@index([pageId, language, voice])
  @@map("page_audios")
}

