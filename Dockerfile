# Build stage - sử dụng để build app
FROM node:22-slim AS builder

# Thiết lập biến môi trường cho build
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV DOCKER_BUILD=true

# Cài đặt các thư viện cần thiết chỉ cho build
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

WORKDIR /app

# Copy package files trước để tận dụng Docker layer caching
COPY package.json package-lock.json* ./

# Cài đặt tất cả dependencies (dev + prod) cho build với retry
RUN npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    npm ci --no-audit --fetch-timeout=300000 || \
    npm ci --no-audit --fetch-timeout=300000

# Copy source code và file env test
COPY . .
COPY .env.production .env

# Tạo thư mục .husky trống nếu không tồn tại
RUN mkdir -p .husky

# Generate Prisma client và build app
RUN npx prisma generate && npx env-cmd -f .env next build

# Production stage - image cuối cùng sẽ nhẹ hơn nhiều
FROM node:22-slim AS runner

# Thiết lập biến môi trường
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV DOCKER_BUILD=true

# Cài đặt runtime dependencies: curl, ffmpeg
RUN apt-get update && apt-get install -y \
    curl \
    ffmpeg \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Tạo non-root user
RUN groupadd -g 1001 nodejs \
    && useradd -u 1001 -g nodejs -s /bin/bash -m nextjs

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Chỉ cài đặt production dependencies với retry
RUN npm config set fetch-retry-mintimeout 20000 && \
    npm config set fetch-retry-maxtimeout 120000 && \
    (npm ci --omit=dev --no-audit --fetch-timeout=300000 || \
     npm ci --omit=dev --no-audit --fetch-timeout=300000) && \
    npm cache clean --force && \
    rm -rf /tmp/* /var/tmp/* /root/.npm

# Copy built application từ builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Chuyển sang non-root user
USER nextjs

# Mở cổng
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Lệnh khởi động app - sử dụng standalone mode
CMD ["node", "server.js"]
