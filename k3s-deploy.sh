#!/bin/bash

# Deploy TransReed App to K3s Cluster
# Usage: ./k3s-deploy.sh [tag]

set -e

# Change to script directory
cd "$(dirname "$0")"

# Configuration
DOCKER_REGISTRY="docker.io"
DOCKER_USERNAME="coding229148"
IMAGE_NAME="transreed"
DOCKER_PASSWORD="************************************"

# VPS Configuration - Using same cluster as python-invoice
VPS_HOST="************"
VPS_USER="root"
VPS_PASSWORD="PasS@691761171552"
VPS_NAME="scraping-cluster-node5"

# K8s Configuration
NAMESPACE="default"
DEPLOYMENT_NAME="transread"
REPLICAS=2

# Database Configuration for Prisma Migration
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Generate CalVer tag if not provided
if [ -z "$1" ]; then
    TAG=$(date +"%Y.%m.%d.%H%M")
    echo -e "${YELLOW}Generated CalVer tag: ${TAG}${NC}"
else
    TAG=$1
fi

FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${TAG}"

echo -e "${BLUE}=== Building and Deploying TransReed App to K3s ===${NC}"
echo -e "${YELLOW}Image: ${FULL_IMAGE_NAME}${NC}"
echo -e "${YELLOW}Target Cluster: ${VPS_USER}@${VPS_HOST} (${VPS_NAME})${NC}"
echo -e "${YELLOW}Replicas: ${REPLICAS}${NC}"
echo ""

# Check Docker
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running${NC}"
    exit 1
fi

# Check sshpass
if ! command -v sshpass &> /dev/null; then
    echo -e "${YELLOW}Installing sshpass...${NC}"
    sudo apt-get update && sudo apt-get install -y sshpass
fi

# Check VPS connectivity
echo -e "${BLUE}Checking VPS connectivity...${NC}"
if ! sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${VPS_USER}@${VPS_HOST} "echo 'Connected'" > /dev/null 2>&1; then
    echo -e "${RED}Error: Cannot connect to VPS${NC}"
    exit 1
fi
echo -e "${GREEN}✓ VPS accessible${NC}"

# Install K3s if not already installed
if ! sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} "command -v k3s" &> /dev/null; then
    echo -e "${BLUE}K3s not found, installing...${NC}"
    ./install-k3s.sh "${VPS_HOST}" "${VPS_USER}" "${VPS_PASSWORD}"
    echo ""
else
    echo -e "${GREEN}✓ K3s already installed${NC}"
fi

# Install cert-manager if not already installed
if ! sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} "k3s kubectl get namespace cert-manager" &> /dev/null; then
    echo -e "${BLUE}cert-manager not found, installing...${NC}"
    ./install-cert-manager.sh "${VPS_HOST}" "${VPS_USER}" "${VPS_PASSWORD}" 22 "<EMAIL>"
    echo ""
else
    echo -e "${GREEN}✓ cert-manager already installed${NC}"
fi

# Login to Docker Hub
echo -e "${BLUE}Logging into Docker Hub...${NC}"
if ! echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin 2>/dev/null; then
    echo -e "${RED}Error: Docker Hub login failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Docker Hub login successful${NC}"

# Run Prisma migrations before building
echo -e "${BLUE}Running Prisma migrations...${NC}"
if ! DATABASE_URL="${DATABASE_URL}" NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ npm run prisma:prod; then
    echo -e "${RED}Error: Prisma migration failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Prisma migrations completed successfully${NC}"

# Build image
echo -e "${BLUE}Building Docker image...${NC}"
if ! docker build -t "${FULL_IMAGE_NAME}" .; then
    echo -e "${RED}Error: Build failed${NC}"
    exit 1
fi

# Get image size
IMAGE_SIZE=$(docker images "${FULL_IMAGE_NAME}" --format "{{.Size}}" | head -1)
echo -e "${GREEN}✓ Build successful (${IMAGE_SIZE})${NC}"

# Push image
echo -e "${BLUE}Pushing to Docker Hub...${NC}"
if ! docker push "${FULL_IMAGE_NAME}"; then
    echo -e "${RED}Error: Push failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Push successful${NC}"

# Deploy to K3s
echo -e "${BLUE}Deploying to K3s...${NC}"

sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} << EOF
set -e

# Setup kubectl alias
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
alias kubectl='k3s kubectl'

# Check cluster status
echo "Checking cluster status..."
k3s kubectl get nodes
echo ""

# Create Docker Hub secret if not exists
echo "Creating Docker Hub secret..."
k3s kubectl create secret docker-registry dockerhub-secret \
    --docker-server=docker.io \
    --docker-username=${DOCKER_USERNAME} \
    --docker-password=${DOCKER_PASSWORD} \
    --namespace=${NAMESPACE} \
    --dry-run=client -o yaml | k3s kubectl apply -f -
echo "✓ Docker Hub secret created/updated"
echo ""

# Create Redis deployment
echo "Creating Redis deployment..."
cat <<REDIS_MANIFEST | k3s kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: ${NAMESPACE}
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        volumeMounts:
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: ${NAMESPACE}
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
REDIS_MANIFEST

echo "✓ Redis deployment created"
echo ""

# Wait for Redis to be ready
echo "Waiting for Redis to be ready..."
k3s kubectl wait --for=condition=available --timeout=60s deployment/redis -n ${NAMESPACE} || true
echo "✓ Redis is ready"
echo ""

# Create/Update deployment
echo "Creating/Updating deployment..."
cat <<MANIFEST | k3s kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${DEPLOYMENT_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: transread
spec:
  replicas: ${REPLICAS}
  selector:
    matchLabels:
      app: transread
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: transread
    spec:
      imagePullSecrets:
        - name: dockerhub-secret
      initContainers:
      - name: wait-for-redis
        image: redis:7-alpine
        command:
        - sh
        - -c
        - |
          echo "Waiting for Redis to be ready..."
          until redis-cli -h redis -p 6379 ping | grep -q PONG; do
            echo "Redis not ready yet, waiting..."
            sleep 2
          done
          echo "Redis is ready!"
      containers:
      - name: transread
        image: ${FULL_IMAGE_NAME}
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        env:
        # App configuration
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_BASE_URL
          value: "https://transreed.com"
        - name: NEXT_PUBLIC_ALLOW_EMAIL_ALIAS
          value: "false"
        - name: NEXT_PUBLIC_FREE_TRIAL_SECONDS
          value: "60"
        - name: NEXT_PUBLIC_SIGNED_IN_FREE_SECONDS
          value: "120"
        - name: NEXT_PUBLIC_ENABLE_SIGNED_IN_GATE
          value: "true"
        - name: STORAGE_MODE
          value: "s3"
        # Redis configuration
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Clerk authentication
        - name: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
          value: "pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ"
        - name: CLERK_PUBLISHABLE_KEY
          value: "pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ"
        - name: CLERK_SECRET_KEY
          value: "**************************************************"
        # Database
        - name: DATABASE_URL
          value: "${DATABASE_URL}"
        # AWS S3
        - name: AWS_REGION
          value: "ap-southeast-1"
        - name: AWS_ACCESS_KEY_ID
          value: "********************"
        - name: AWS_SECRET_ACCESS_KEY
          value: "eyzO00KGR7XpPcDnxsAeGZaQiFzxZ9sbsPpH85JI"
        - name: S3_BUCKET_NAME
          value: "transreed-prod"
        # TTS Configuration
        - name: VBEE_APP_ID
          value: "8e01bd4e-6353-483d-b02e-91f1b907661b"
        - name: VBEE_TOKEN
          value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NjMxMDc2NDN9.AcrdZef73Z_hINR8M9D-wTmIjQoe7IGcJHNvQ-bh8x8"
        - name: TTS_CHUNK_WORDS
          value: "260"
        - name: GEMINI_API_KEY
          value: "AIzaSyA-kXPgR57irUBhsJy6wIkiafWLoTv5qg8"
        - name: GEMINI_API_KEY2
          value: "AIzaSyBS_x8HuDUq9WPYAzdaHzwfyTSXx9Kh5gw"
        - name: GEMINI_TTS_VOICE_NAME
          value: "Zephyr"
        # Google Cloud TTS
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "./.secret/transread-470011-3265925815ba.json"
        - name: GCP_TTS_API_KEY
          value: "AIzaSyCNJe67aVkiwKXNJo1L8J0b0CchvMtRabs"
        - name: GCP_TTS_PROJECT_ID
          value: "transread-470011"
        - name: GCP_TTS_MODEL
          value: "gemini-2.5-pro-preview-tts"
        - name: GCP_TTS_VOICE_NAME
          value: "vi-VN-Standard-A"
        - name: GCP_TTS_AUDIO_ENCODING
          value: "MP3"
        # OpenAI
        - name: OPENAI_API_KEY
          value: "********************************************************************************************************************************************************************"
        # Polar
        - name: POLAR_ACCESS_TOKEN
          value: "polar_oat_6Lwpm1ZZfPkseruEmStgrqxgJ0QO9vSd72IXf3gA6Wd"
        # reCAPTCHA
        - name: RECAPTCHA_MODE
          value: "enterprise"
        - name: NEXT_PUBLIC_RECAPTCHA_SITE_KEY
          value: "6Lfrmx0qAAAAABxoovTtDcXPtGUf0_QRDrvod_Hg"
        - name: RECAPTCHA_ENTERPRISE_PROJECT_ID
          value: "shaped-shuttle-425110-i4"
        - name: RECAPTCHA_MIN_SCORE
          value: "0.3"
        - name: RECAPTCHA_ENTERPRISE_CLIENT_EMAIL
          value: "<EMAIL>"
        - name: RECAPTCHA_ENTERPRISE_PRIVATE_KEY
          value: "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"
        - name: GOOGLE_CLIENT_EMAIL
          value: "<EMAIL>"
        - name: GOOGLE_PRIVATE_KEY
          value: "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"
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            cpu: 2000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: ${DEPLOYMENT_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: transread
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: transread
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${DEPLOYMENT_NAME}
  namespace: ${NAMESPACE}
  labels:
    app: transread
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: traefik
  tls:
  - hosts:
    - transreed.com
    - www.transreed.com
    secretName: transread-tls
  rules:
  - host: transreed.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ${DEPLOYMENT_NAME}
            port:
              number: 3000
  - host: www.transreed.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ${DEPLOYMENT_NAME}
            port:
              number: 3000
MANIFEST

# Create/Update worker deployment
echo "Creating/Updating worker deployment..."
cat <<WORKER_MANIFEST | k3s kubectl apply -f -
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: transread-worker
  namespace: ${NAMESPACE}
  labels:
    app: transread-worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: transread-worker
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: transread-worker
    spec:
      imagePullSecrets:
        - name: dockerhub-secret
      initContainers:
      - name: wait-for-redis
        image: redis:7-alpine
        command:
        - sh
        - -c
        - |
          echo "Waiting for Redis to be ready..."
          until redis-cli -h redis -p 6379 ping | grep -q PONG; do
            echo "Redis not ready yet, waiting..."
            sleep 2
          done
          echo "Redis is ready!"
      containers:
      - name: worker
        image: ${FULL_IMAGE_NAME}
        imagePullPolicy: Always
        command: ["npm", "run", "worker"]
        env:
        # App configuration
        - name: NODE_ENV
          value: "production"
        - name: NEXT_PUBLIC_BASE_URL
          value: "https://transreed.com"
        - name: NEXT_PUBLIC_ALLOW_EMAIL_ALIAS
          value: "false"
        - name: NEXT_PUBLIC_FREE_TRIAL_SECONDS
          value: "60"
        - name: NEXT_PUBLIC_SIGNED_IN_FREE_SECONDS
          value: "120"
        - name: NEXT_PUBLIC_ENABLE_SIGNED_IN_GATE
          value: "true"
        - name: STORAGE_MODE
          value: "s3"
        # Redis configuration
        - name: REDIS_HOST
          value: "redis"
        - name: REDIS_PORT
          value: "6379"
        # Clerk authentication
        - name: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
          value: "pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ"
        - name: CLERK_PUBLISHABLE_KEY
          value: "pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ"
        - name: CLERK_SECRET_KEY
          value: "**************************************************"
        # Database
        - name: DATABASE_URL
          value: "${DATABASE_URL}"
        # AWS S3
        - name: AWS_REGION
          value: "ap-southeast-1"
        - name: AWS_ACCESS_KEY_ID
          value: "********************"
        - name: AWS_SECRET_ACCESS_KEY
          value: "eyzO00KGR7XpPcDnxsAeGZaQiFzxZ9sbsPpH85JI"
        - name: S3_BUCKET_NAME
          value: "transreed-prod"
        # TTS Configuration
        - name: VBEE_APP_ID
          value: "8e01bd4e-6353-483d-b02e-91f1b907661b"
        - name: VBEE_TOKEN
          value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NjMxMDc2NDN9.AcrdZef73Z_hINR8M9D-wTmIjQoe7IGcJHNvQ-bh8x8"
        - name: TTS_CHUNK_WORDS
          value: "260"
        - name: GEMINI_API_KEY
          value: "AIzaSyA-kXPgR57irUBhsJy6wIkiafWLoTv5qg8"
        - name: GEMINI_API_KEY2
          value: "AIzaSyBS_x8HuDUq9WPYAzdaHzwfyTSXx9Kh5gw"
        - name: GEMINI_TTS_VOICE_NAME
          value: "Zephyr"
        # Google Cloud TTS
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "./.secret/transread-470011-3265925815ba.json"
        - name: GCP_TTS_API_KEY
          value: "AIzaSyCNJe67aVkiwKXNJo1L8J0b0CchvMtRabs"
        - name: GCP_TTS_PROJECT_ID
          value: "transread-470011"
        - name: GCP_TTS_MODEL
          value: "gemini-2.5-pro-preview-tts"
        - name: GCP_TTS_VOICE_NAME
          value: "vi-VN-Standard-A"
        - name: GCP_TTS_AUDIO_ENCODING
          value: "MP3"
        # OpenAI
        - name: OPENAI_API_KEY
          value: "********************************************************************************************************************************************************************"
        # Polar
        - name: POLAR_ACCESS_TOKEN
          value: "polar_oat_6Lwpm1ZZfPkseruEmStgrqxgJ0QO9vSd72IXf3gA6Wd"
        # reCAPTCHA
        - name: RECAPTCHA_MODE
          value: "enterprise"
        - name: NEXT_PUBLIC_RECAPTCHA_SITE_KEY
          value: "6Lfrmx0qAAAAABxoovTtDcXPtGUf0_QRDrvod_Hg"
        - name: RECAPTCHA_ENTERPRISE_PROJECT_ID
          value: "shaped-shuttle-425110-i4"
        - name: RECAPTCHA_MIN_SCORE
          value: "0.3"
        - name: RECAPTCHA_ENTERPRISE_CLIENT_EMAIL
          value: "<EMAIL>"
        - name: RECAPTCHA_ENTERPRISE_PRIVATE_KEY
          value: "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"
        - name: GOOGLE_CLIENT_EMAIL
          value: "<EMAIL>"
        - name: GOOGLE_PRIVATE_KEY
          value: "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"
        resources:
          requests:
            cpu: 500m
            memory: 512Mi
          limits:
            cpu: 2000m
            memory: 2Gi
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "ps aux | grep -v grep | grep 'workers/document-processor' || exit 1"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
WORKER_MANIFEST

echo "✓ Worker deployment created/updated"
echo ""

# Wait for worker rollout
echo "Waiting for worker rollout to complete..."
k3s kubectl rollout status deployment/transread-worker -n ${NAMESPACE} --timeout=300s

echo ""
echo "=== Worker Deployment Status ==="
k3s kubectl get deployment transread-worker -n ${NAMESPACE}
echo ""

echo "=== Worker Pods Status ==="
k3s kubectl get pods -n ${NAMESPACE} -l app=transread-worker
echo ""

echo "✓ Deployment created/updated"
echo ""

# Wait for rollout
echo "Waiting for rollout to complete..."
k3s kubectl rollout status deployment/${DEPLOYMENT_NAME} -n ${NAMESPACE} --timeout=300s

echo ""
echo "=== Deployment Status ==="
k3s kubectl get deployment ${DEPLOYMENT_NAME} -n ${NAMESPACE}
echo ""

echo "=== Pods Status ==="
k3s kubectl get pods -n ${NAMESPACE} -l app=transread
echo ""

echo "=== Service Status ==="
k3s kubectl get service ${DEPLOYMENT_NAME} -n ${NAMESPACE}
echo ""

echo "=== Ingress Status ==="
k3s kubectl get ingress ${DEPLOYMENT_NAME} -n ${NAMESPACE}
echo ""

echo "=== Resource Usage ==="
k3s kubectl top pods -n ${NAMESPACE} -l app=transread 2>/dev/null || echo "Metrics not available yet"
echo ""

# Test health endpoint
echo "=== Testing Health Endpoint ==="
sleep 5
POD_NAME=\$(k3s kubectl get pods -n ${NAMESPACE} -l app=transread -o jsonpath='{.items[0].metadata.name}')
if [ -n "\$POD_NAME" ]; then
    echo "Testing health endpoint on pod \$POD_NAME..."
    k3s kubectl exec -n ${NAMESPACE} \$POD_NAME -- curl -s http://localhost:3000/api/health || echo "Health check pending..."
fi
echo ""

EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Deployment successful${NC}"
else
    echo -e "${RED}Error: Deployment failed${NC}"
    exit 1
fi

# Cleanup local image
docker rmi "${FULL_IMAGE_NAME}" 2>/dev/null || true

echo ""
echo -e "${GREEN}=== K3s Deployment Complete! ===${NC}"
echo -e "${YELLOW}Image: ${FULL_IMAGE_NAME}${NC}"
echo -e "${YELLOW}Size: ${IMAGE_SIZE}${NC}"
echo -e "${YELLOW}Cluster: ${VPS_USER}@${VPS_HOST} (${VPS_NAME})${NC}"
echo -e "${YELLOW}Namespace: ${NAMESPACE}${NC}"
echo -e "${YELLOW}Replicas: ${REPLICAS}${NC}"
echo ""
echo -e "${BLUE}🌐 Production Domain (HTTPS):${NC}"
echo -e "${YELLOW}  Domain:       https://transreed.com${NC}"
echo -e "${YELLOW}  Alt Domain:   https://www.transreed.com${NC}"
echo -e "${YELLOW}  Health:       https://transreed.com/api/health${NC}"
echo ""
echo -e "${BLUE}⚠️  DNS Configuration Required:${NC}"
echo -e "${YELLOW}  Add A records:${NC}"
echo -e "${YELLOW}    transreed.com → ${VPS_HOST}${NC}"
echo -e "${YELLOW}    www.transreed.com → ${VPS_HOST}${NC}"
echo -e "${YELLOW}  SSL certificate will be automatically provisioned by cert-manager${NC}"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo ""
echo -e "${YELLOW}# View pods${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl get pods -l app=transread'"
echo ""
echo -e "${YELLOW}# View logs${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl logs -l app=transread -f'"
echo ""
echo -e "${YELLOW}# View deployment status${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl get deployment transread'"
echo ""
echo -e "${YELLOW}# View ingress and certificate${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl get ingress transread'"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl get certificate transread-tls'"
echo ""
echo -e "${YELLOW}# Restart deployment${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl rollout restart deployment/transread'"
echo ""
echo -e "${YELLOW}# Scale replicas${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl scale deployment transread --replicas=3'"
echo ""
echo -e "${YELLOW}# Check resource usage${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'k3s kubectl top pods -l app=transread'"
echo ""
echo -e "${YELLOW}# SSH to VPS${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST}"
echo ""
