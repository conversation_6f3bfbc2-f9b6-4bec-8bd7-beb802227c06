#!/bin/bash

# Install K3s on VPS
# Usage: ./install-k3s.sh [vps_host] [vps_user] [vps_password] [vps_port]
# Example: ./install-k3s.sh ************ root mypassword 22

set -e

# Default values
VPS_HOST="${1:-************}"
VPS_USER="${2:-root}"
VPS_PASSWORD="${3}"
VPS_PORT="${4:-22}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

if [ -z "$VPS_PASSWORD" ]; then
    echo -e "${RED}Error: VPS password is required${NC}"
    echo "Usage: $0 <vps_host> <vps_user> <vps_password> [vps_port]"
    exit 1
fi

echo -e "${BLUE}=== Installing K3s on VPS ===${NC}"
echo -e "${YELLOW}Host: ${VPS_USER}@${VPS_HOST}:${VPS_PORT}${NC}"
echo ""

# Check sshpass
if ! command -v sshpass &> /dev/null; then
    echo -e "${YELLOW}Installing sshpass...${NC}"
    sudo apt-get update && sudo apt-get install -y sshpass
fi

# Check VPS connectivity
echo -e "${BLUE}Checking VPS connectivity...${NC}"
if ! sshpass -p "${VPS_PASSWORD}" ssh -p ${VPS_PORT} -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${VPS_USER}@${VPS_HOST} "echo 'Connected'" > /dev/null 2>&1; then
    echo -e "${RED}Error: Cannot connect to ${VPS_HOST}:${VPS_PORT}${NC}"
    exit 1
fi
echo -e "${GREEN}✓ VPS accessible${NC}"

# Install K3s on VPS
echo -e "${BLUE}Installing K3s...${NC}"

sshpass -p "${VPS_PASSWORD}" ssh -p ${VPS_PORT} -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} << 'EOF'
set -e

# Check if K3s is already installed
if command -v k3s &> /dev/null; then
    echo ""
    echo "✓ K3s already installed"
    k3s --version
    echo ""

    # Check K3s status
    echo "Checking K3s status..."
    systemctl status k3s --no-pager || true
    echo ""

    # Check cluster nodes
    echo "Cluster nodes:"
    k3s kubectl get nodes
    echo ""

    exit 0
fi

echo ""
echo "=== K3s not found, installing K3s single-node cluster ==="
echo ""

# Install K3s
curl -sfL https://get.k3s.io | sh -s - --write-kubeconfig-mode 644

# Wait for K3s to be ready
echo "Waiting for K3s to be ready..."
sleep 15

# Check K3s status
echo ""
echo "K3s service status:"
systemctl status k3s --no-pager || true
echo ""

# Wait for node to be ready
echo "Waiting for node to be ready..."
TIMEOUT=60
ELAPSED=0
while [ $ELAPSED -lt $TIMEOUT ]; do
    STATUS=$(k3s kubectl get nodes --no-headers 2>/dev/null | awk '{print $2}' | head -1)
    echo "  - Node status: $STATUS (elapsed: ${ELAPSED}s)"

    if [ "$STATUS" = "Ready" ]; then
        echo "✓ Node is ready!"
        break
    fi

    sleep 5
    ELAPSED=$((ELAPSED + 5))
done

if [ $ELAPSED -ge $TIMEOUT ]; then
    echo "⚠ Warning: Timeout waiting for node to be ready"
    exit 1
fi

echo ""
echo "✓ K3s installed successfully"
echo ""

# Show K3s version
k3s --version
echo ""

# Show cluster nodes
echo "Cluster nodes:"
k3s kubectl get nodes
echo ""

# Show system pods
echo "System pods:"
k3s kubectl get pods -A
echo ""

EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ K3s installation completed${NC}"
else
    echo -e "${RED}Error: K3s installation failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== K3s Installation Complete! ===${NC}"
echo -e "${YELLOW}Host: ${VPS_USER}@${VPS_HOST}:${VPS_PORT}${NC}"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo ""
echo -e "${YELLOW}# SSH to VPS${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST}"
echo ""
echo -e "${YELLOW}# Check K3s status${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'systemctl status k3s'"
echo ""
echo -e "${YELLOW}# View cluster nodes${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl get nodes'"
echo ""
echo -e "${YELLOW}# View all pods${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl get pods -A'"
echo ""
