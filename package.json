{"name": "transread", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@heroui/react": "^2.8.2", "@heroui/theme": "^2.4.20", "@prisma/client": "^6.14.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@types/pg": "^8.15.5", "next": "15.5.0", "pg": "^8.16.3", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "tsx": "^4.20.4", "typescript": "^5"}}