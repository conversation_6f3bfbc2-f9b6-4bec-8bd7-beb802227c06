{"name": "transreed", "version": "0.1.0", "private": true, "scripts": {"predev": "node scripts/ensure-redis.js && node scripts/check-next-perms.js", "dev": "concurrently --success first \"npm run dev:server\" \"npm run dev:worker\" --names \"server,worker\" --prefix-colors \"cyan,magenta\"", "dev:server": "next dev", "dev:worker": "npx tsx watch workers/document-processor.ts", "build": "prisma generate && next build", "build:prod": "env-cmd -f .env.production next build", "start": "concurrently --success first \"npm run start:server\" \"npm run start:worker\" --names \"server,worker\" --prefix-colors \"cyan,magenta\"", "start:server": "next start", "start:worker": "npx tsx workers/document-processor.ts", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "setup:db": "node scripts/setup-db.js", "prisma": "prisma", "prisma:prod": "env-cmd -f .env.production prisma generate && prisma migrate deploy", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy", "db:migrate:dev": "prisma migrate dev", "db:reset": "prisma migrate reset --force", "db:push": "prisma db push", "data:import-docs": "node scripts/import-docs.js", "data:index-artifacts": "node scripts/index-local-artifacts.js", "data:free-anon": "node scripts/free-anon-session.js", "worker": "npx tsx workers/document-processor.ts", "worker:dev": "npx tsx watch workers/document-processor.ts", "dev:cleanup": "node scripts/cleanup-dev.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.893.0", "@clerk/nextjs": "^6.33.3", "@fingerprintjs/fingerprintjs": "^4.6.2", "@google-cloud/recaptcha-enterprise": "^6.3.0", "@google/genai": "^0.3.0", "@google/generative-ai": "^0.19.0", "@heroicons/react": "^2.2.0", "@heroui/react": "2.7.6", "@heroui/theme": "2.4.13", "@heroui/use-infinite-scroll": "2.2.7", "@polar-sh/nextjs": "^0.4.9", "@prisma/client": "^6.15.0", "@tabler/icons-react": "^3.19.0", "@tanstack/react-query": "5.68.0", "autoprefixer": "10.4.19", "bufferutil": "^4.0.9", "bullmq": "^5.64.1", "env-cmd": "^10.1.0", "ioredis": "^5.8.2", "mime": "^4.0.4", "music-metadata": "^11.9.0", "next": "15.5.4", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^4.7.76", "prisma": "^6.15.0", "react": "19.2.0", "react-dom": "19.2.0", "react-dropzone": "^14.3.6", "react-toastify": "^10.0.5", "utf-8-validate": "^6.0.5"}, "devDependencies": {"@types/node": "24.3.0", "@types/react": "19.1.11", "@types/react-dom": "19.0.4", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "concurrently": "^9.2.1", "eslint": "8.57.0", "eslint-config-next": "15.5.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "postcss": "8.4.39", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "tailwindcss": "3.4.10", "typescript": "5.7.3"}}