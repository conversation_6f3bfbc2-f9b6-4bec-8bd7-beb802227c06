{"permissions": {"allow": ["Bash(npm run lint:*)", "Bash(npm run lint:fix:*)", "Bash(find:*)", "<PERSON><PERSON>(pkill:*)", "WebSearch", "WebFetch(domain:docs.polar.sh)", "WebFetch(domain:polar.sh)", "<PERSON><PERSON>(cat:*)", "Bash(npm run build:*)", "Read(///**)", "Bash(psql:*)", "Bash(npx tsx:*)", "Bash(sudo rm:*)", "<PERSON><PERSON>(chmod:*)", "WebFetch(domain:ai.google.dev)", "Bash(tree:*)", "Bash(npx tsc:*)", "Bash(grep:*)", "Bash(npm run dev:*)", "Bash(npm install:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(npx jest:*)", "<PERSON><PERSON>(npx prettier:*)", "Bash(aws s3 ls:*)", "<PERSON><PERSON>(timeout 5 npm run worker:*)", "Bash(redis-cli:*)", "Bash(git log:*)"], "deny": [], "ask": []}}