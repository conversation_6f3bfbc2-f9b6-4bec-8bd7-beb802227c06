version: '3.9'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: transreed
      POSTGRES_USER: transreed
      POSTGRES_PASSWORD: transreed
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      # Expose for local access (optional)
      - '5432:5432'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB']
      interval: 5s
      timeout: 5s
      retries: 10

  redis:
    image: redis:7-alpine
    container_name: transread-redis
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 5s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: transreed-app
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    image: transreed:${TAG}
    ports:
      - '3001:3000'
    restart: unless-stopped

volumes:
  pgdata:
  redis-data:
