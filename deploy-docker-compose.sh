#!/bin/bash

# Deploy TransReed App to VPS using Docker Compose
# Usage: ./deploy-docker-compose.sh [tag]

set -e

# Change to script directory
cd "$(dirname "$0")"

# Configuration
DOCKER_REGISTRY="docker.io"
DOCKER_USERNAME="coding229148"
IMAGE_NAME="transreed"
DOCKER_PASSWORD="************************************"

# VPS Configuration
VPS_HOST="***************"
VPS_USER="root"
VPS_PASSWORD="TDCloud@1720"
VPS_DEPLOY_DIR="/root/transread"

# Database Configuration for Prisma Migration
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Generate CalVer tag if not provided
if [ -z "$1" ]; then
    TAG=$(date +"%Y.%m.%d.%H%M")
    echo -e "${YELLOW}Generated CalVer tag: ${TAG}${NC}"
else
    TAG=$1
fi

FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${TAG}"

echo -e "${BLUE}=== Building and Deploying TransReed App ===${NC}"
echo -e "${YELLOW}Image: ${FULL_IMAGE_NAME}${NC}"
echo -e "${YELLOW}VPS: ${VPS_USER}@${VPS_HOST}${NC}"
echo ""

# Check Docker
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running${NC}"
    exit 1
fi

# Check VPS connectivity
echo -e "${BLUE}Checking VPS connectivity...${NC}"
if ! sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${VPS_USER}@${VPS_HOST} "echo 'Connected'" > /dev/null 2>&1; then
    echo -e "${RED}Error: Cannot connect to VPS${NC}"
    exit 1
fi
echo -e "${GREEN}✓ VPS accessible${NC}"

# Check sshpass
if ! command -v sshpass &> /dev/null; then
    echo -e "${YELLOW}Installing sshpass...${NC}"
    sudo apt-get update && sudo apt-get install -y sshpass
fi

# Login to Docker Hub
echo -e "${BLUE}Logging into Docker Hub...${NC}"
if ! echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin 2>/dev/null; then
    echo -e "${RED}Error: Docker Hub login failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Docker Hub login successful${NC}"

# Run Prisma migrations before building
echo -e "${BLUE}Running Prisma migrations...${NC}"
if ! DATABASE_URL="${DATABASE_URL}" NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ npm run prisma:prod; then
    echo -e "${RED}Error: Prisma migration failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Prisma migrations completed successfully${NC}"

# Build image
echo -e "${BLUE}Building Docker image...${NC}"
if ! docker build -t "${FULL_IMAGE_NAME}" .; then
    echo -e "${RED}Error: Build failed${NC}"
    exit 1
fi

# Get image size
IMAGE_SIZE=$(docker images "${FULL_IMAGE_NAME}" --format "{{.Size}}" | head -1)
echo -e "${GREEN}✓ Build successful (${IMAGE_SIZE})${NC}"

# Push image
echo -e "${BLUE}Pushing to Docker Hub...${NC}"
if ! docker push "${FULL_IMAGE_NAME}"; then
    echo -e "${RED}Error: Push failed${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Push successful${NC}"

# Update docker-compose.vps.yml with new tag
echo -e "${BLUE}Updating docker-compose.vps.yml with new tag...${NC}"
sed -i "s|image: docker.io/coding229148/transreed:.*|image: docker.io/coding229148/transreed:${TAG}|g" docker-compose.vps.yml
echo -e "${GREEN}✓ docker-compose.vps.yml updated${NC}"

# Deploy to VPS
echo -e "${BLUE}Deploying to VPS...${NC}"

# Create deployment directory on VPS
sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} "mkdir -p ${VPS_DEPLOY_DIR}"

# Copy docker-compose.vps.yml and Caddyfile to VPS
echo -e "${BLUE}Copying files to VPS...${NC}"
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no docker-compose.vps.yml ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/docker-compose.yml
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no Caddyfile ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/
echo -e "${GREEN}✓ Files copied to VPS${NC}"

# Deploy on VPS
echo -e "${BLUE}Running docker-compose on VPS...${NC}"
sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} << EOF
cd ${VPS_DEPLOY_DIR}

# Install Docker if not exists
if ! command -v docker &> /dev/null; then
    echo "Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl enable docker
    systemctl start docker
    rm get-docker.sh
    echo "✓ Docker installed successfully"
fi

# Install docker-compose if not exists
if ! command -v docker-compose &> /dev/null; then
    echo "Installing docker-compose..."
    curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-\$(uname -s)-\$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    echo "✓ docker-compose installed successfully"
fi

# Login to Docker Hub on VPS
echo "Logging into Docker Hub on VPS..."
echo "${DOCKER_PASSWORD}" | docker login -u "${DOCKER_USERNAME}" --password-stdin > /dev/null 2>&1
if [ \$? -eq 0 ]; then
    echo "✓ Docker Hub login successful on VPS"
else
    echo "⚠ Docker Hub login failed, but continuing..."
fi

# Pull latest image for transread-app only
echo "Pulling latest transread-app image..."
docker-compose pull transread-app

# Remove old images to free up space
echo "Cleaning up old images..."
docker image prune -f

# Update only transread-app service without affecting caddy and portainer
echo "Updating transread-app service (2 replicas)..."
docker-compose up -d --no-deps --scale transread-app=2 transread-app

# Wait for containers to be healthy
echo ""
echo "Waiting for containers to be healthy..."
sleep 10

# Show status
echo ""
echo "Container status:"
docker-compose ps

# Check health status
echo ""
echo "Health check:"
docker-compose ps | grep -q "healthy" && echo "✓ Containers are healthy" || echo "⚠ Some containers are not healthy yet"

# Show logs
echo ""
echo "Recent logs:"
docker-compose logs --tail=30

# Show resource usage
echo ""
echo "Resource usage:"
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}"
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Deployment successful${NC}"
else
    echo -e "${RED}Error: Deployment failed${NC}"
    exit 1
fi

# Cleanup local image
docker rmi "${FULL_IMAGE_NAME}" 2>/dev/null || true

echo ""
echo -e "${GREEN}=== Deployment Complete! ===${NC}"
echo -e "${YELLOW}Image: ${FULL_IMAGE_NAME}${NC}"
echo -e "${YELLOW}Size: ${IMAGE_SIZE}${NC}"
echo -e "${YELLOW}VPS: https://transreed.com${NC}"
echo -e "${YELLOW}Instances: 2 (Load Balanced)${NC}"
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo -e "${YELLOW}# Check container status${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'cd ${VPS_DEPLOY_DIR} && docker-compose ps'"
echo ""
echo -e "${YELLOW}# View logs${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'cd ${VPS_DEPLOY_DIR} && docker-compose logs -f'"
echo ""
echo -e "${YELLOW}# Scale to different number of instances${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'cd ${VPS_DEPLOY_DIR} && docker-compose up -d --scale transread-app=3'"
echo ""
echo -e "${YELLOW}# Restart containers${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'cd ${VPS_DEPLOY_DIR} && docker-compose restart'"
echo ""
echo -e "${YELLOW}# Check resource usage${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh ${VPS_USER}@${VPS_HOST} 'docker stats --no-stream'"
