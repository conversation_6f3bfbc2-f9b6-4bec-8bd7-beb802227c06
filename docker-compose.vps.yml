version: '3.8'

services:
  transread-app:
    image: docker.io/coding229148/transreed:2025.10.24.1746
    restart: unless-stopped
    deploy:
      replicas: 2
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_BASE_URL: https://transreed.com
      NEXT_PUBLIC_ALLOW_EMAIL_ALIAS: "false"
      NEXT_PUBLIC_FREE_TRIAL_SECONDS: "60"
      NEXT_PUBLIC_SIGNED_IN_FREE_SECONDS: "120"
      NEXT_PUBLIC_ENABLE_SIGNED_IN_GATE: "true"

      STORAGE_MODE: s3

      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ
      CLERK_PUBLISHABLE_KEY: pk_live_Y2xlcmsudHJhbnNyZWVkLmNvbSQ
      CLERK_SECRET_KEY: **************************************************

      DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

      AWS_REGION: ap-southeast-1
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: eyzO00KGR7XpPcDnxsAeGZaQiFzxZ9sbsPpH85JI
      S3_BUCKET_NAME: transreed-prod

      TTS_CHUNK_WORDS: "260"

      GEMINI_API_KEY: AIzaSyA-kXPgR57irUBhsJy6wIkiafWLoTv5qg8
      GEMINI_API_KEY2: AIzaSyBS_x8HuDUq9WPYAzdaHzwfyTSXx9Kh5gw
      GEMINI_TTS_VOICE_NAME: Zephyr

      GOOGLE_APPLICATION_CREDENTIALS: ./.secret/transread-470011-3265925815ba.json
      GCP_TTS_API_KEY: AIzaSyCNJe67aVkiwKXNJo1L8J0b0CchvMtRabs
      GCP_TTS_PROJECT_ID: transread-470011
      GCP_TTS_MODEL: gemini-2.5-pro-preview-tts
      GCP_TTS_VOICE_NAME: vi-VN-Standard-A
      GCP_TTS_AUDIO_ENCODING: MP3

      OPENAI_API_KEY: ********************************************************************************************************************************************************************
      POLAR_ACCESS_TOKEN: polar_oat_6Lwpm1ZZfPkseruEmStgrqxgJ0QO9vSd72IXf3gA6Wd

      RECAPTCHA_MODE: enterprise
      NEXT_PUBLIC_RECAPTCHA_SITE_KEY: 6Lfrmx0qAAAAABxoovTtDcXPtGUf0_QRDrvod_Hg
      RECAPTCHA_ENTERPRISE_PROJECT_ID: shaped-shuttle-425110-i4
      RECAPTCHA_MIN_SCORE: "0.3"

      RECAPTCHA_ENTERPRISE_CLIENT_EMAIL: "<EMAIL>"
      RECAPTCHA_ENTERPRISE_PRIVATE_KEY: "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"

      GOOGLE_CLIENT_EMAIL: "<EMAIL>"
      GOOGLE_PRIVATE_KEY: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2UUlCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktjd2dnU2pBZ0VBQW9JQkFRQzdGWHFLWWlNUTRlMDkKTkRKZHc1WDlsd3hMa1dHZXFtamN5UEtsdUNWMSt6ZkRWcmpIUTRRaGhSWDNacmEvWTR3UmhvWHJsNWZobnVpSQpkcWU1cEpuZDhpYitUZm8rZGorUkRDM0R1TlNubnBTV3VpdlBkaDJiL05DM0NrVjBlZWk0S3Z3eFBBTkx5SXNaCkFZaHV6YkVjYUcxYXhJbitjVjRhNzJxTW1nWS9hLzRsSFdhOXMzdzhWM1JGaVdHVkZCeWQ1SGtrTXptbW9MM2kKY3ByWHB5RDZkbjlNZGpnUFZxWGlFajZYdE1wS2JsNUFGL2VwTWxDbk5lWDdQL1BZMnhQTXZmVkkzT1hEbWpFWApBUmM0elRKbG9hVHYrZlF2NjBjdHBuOHptczVmMUJQT0JJZEtZc3RpN25oWWY4c1I2d2RoSW9KQXRUSCt1K0F6Ck1hcm9mK3hyQWdNQkFBRUNnZ0VBRURWSEFHTTFNakFUVU5OTFdSMC9TdVpCczBXbitwK1NrSUk3cDNPQjUxYWsKUTZEK25EWXpaZFlLWVB5VFJrSGhHalJET1BYUlprTjhBTi8xY3dZWmdJV0xXWi9oZ2grQTJBSldtcXpTTVAxeQpWbkZRVWdaNUtwRDh6TWJhaCt5aDVyRnVyUE5MNlNPNWpWUGoxbzhDZlBsbnlVNkZhQTBPcXBzUWFrTCtoSkw0CkttVmgvYnM0OWMvRDhLMWtOMXhraEUyZHVBamNxK2laUElIY0loQnpLTUNCaC9iYVBCNlNoNHd3MHpRb0ZkaW0KM2doTGJobmNkSWtCZkd3Y3cwUitOc2pHTG5CQXhTbDB0TUNsRkVNOXJPS0JvdC92clpvYTh6SXYvTXZuSFlaTApyRldlUlVMbkFIMmhodXJrTjVlZmgvSHhhMjk2dkhYbkFwMlN4T0Q4QVFLQmdRRGhKbEhVL1BCVkNubTI0R3VnCm5yaXVhSUptbkh1OXFpSk8ycS9TeUFSOEVVMGFIdkVPejkvZHgwNkFSZGxUTCtzOXFndVJNN20wUTFLbGdNSS8KSWVRY3hZZGpMUjJJUHgyQ01BQnR6TFptbXpzaCtWeUN2NWRzOGNnSnd6UkR4VXVuanQzVnV0cXlENGdXbXg0WQpheVJRaXVXUHlackJEaXEralNzK24vRk1BUUtCZ1FEVXQraGlxemt0SlJMYlVocTNFNmJVZlEwb0I3ZURPY2RBClRxZ2t0a2h2c3NONEt5YWFlTUdxWFNWdWQ0R2JqSnhiOVUzRGtjdXEwcW1ILzRwSTF5a241QUgrR3kvWGpRNzcKeEhFdmdIL1NyUVNGSzZSdDU3cTF4RlhqT0h0WWg1UGIwVkhRcng5c2RRZUFhWVczZjhadVBEWmd3VXJBbU9ORwpYeURvM3NVb2F3S0JnQ1VGUC85ZGQ4Y0Q2ZStBQnpKcDVzUUd1eXBVSUtTWERQMGFTRDdJVkxSeHc5Ty9zck5VCnl1S1VtdnZNZ0dlRlFaRHFIT3pMTElYUGJBOTZsSFg5QkowV2ZhUjVEcG9RaHJHWll2aXE2SE1Hb3pwNjNoTkIKbll4MFZRZ0Y2Tm9iM3N5MldST0RuZUlaVzY4YjRsN3hmdzhmUjQvVS9uVXY5eWROM3daMUZpQUJBb0dCQUpjSQpUUk5oaHlNcW80emRhdUo2TFlYSmxFSEwyTnh2ZGEyUEs0dUZjVUQ5cUJQMmIxNVdSd3R6UDFTRHZaNTM0N3pYCkd2TzAzT3JZMEU0amZrZmhuUE1aek9CaTVVRWlnblQ0TVhVTXc3VU42NXV5TlBsa0F3bHo4WmQzVHZ5NHBwNzQKckNLRGRNaEkvNTZKVGN2UUNVL1RxTGhpQlhWaUdGeXF4bThSWE9NcEFvR0FOQTF0b0FLL2E0L1RpN2xVcUlwawpxaEpOb3BhbGliemhPVlh1aURQd29TNi9Eam1BdmduY05LYWJhZjJyYUxKYmM1UEJINEZieWlsRUVDRUpvWkp4CnNTaUcyMWpEK1dQdW96U3o5SGR4L01sSDJOSjgzaCtlZVFydkloZ2VZMkdrVEx2TFM5enhMdDQ4WGwyemxxTWIKd200c0tJSCtFd2xxdmxQUGtBOXpLb1U9Ci0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0

    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  caddy:
    image: caddy:2-alpine
    container_name: caddy-transread
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "443:443/udp"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
    networks:
      - app-network
    depends_on:
      - transread-app
      - grafana
    environment:
      - ACME_AGREE=true

  loki:
    image: grafana/loki:latest
    container_name: loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - loki_data:/loki
      - ./loki-config.yml:/etc/loki/local-config.yaml
    networks:
      - app-network
    command: -config.file=/etc/loki/local-config.yaml

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    restart: unless-stopped
    volumes:
      - /var/log:/var/log
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock
      - ./promtail-config.yml:/etc/promtail/config.yml
    networks:
      - app-network
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - /var/run/docker.sock:/var/run/docker.sock
      - ./grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
    networks:
      - app-network
    environment:
      - GF_SERVER_ROOT_URL=https://grafana.transreed.com
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=@Nautilus1995
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    depends_on:
      - loki

networks:
  app-network:
    driver: bridge

volumes:
  caddy_data:
  caddy_config:
  grafana_data:
  loki_data:
