# TransRead - Next.js Full-Stack Application

A modern Next.js application with a complete backend API setup, built with TypeScript, Tailwind CSS, and comprehensive API routes.

## 🚀 Features

### Frontend
- ⚛️ **Next.js 15.5.0** with App Router
- 📘 **TypeScript** for type safety
- 🎨 **Tailwind CSS v4** for styling
- 🎯 **HeroUI** for beautiful components
- � **TanStack Query** for data fetching
- �🔍 **ESLint** for code quality
- 🏗️ **Turbopack** for fast development

### Backend API
- 🛠️ **RESTful API** with Next.js Route Handlers
- ✅ **Request Validation** and error handling
- 🔄 **CRUD Operations** for Users, Posts, Comments, and Tags
- 🌐 **CORS Middleware** configured
- 📊 **Structured Response Format**
- 🗄️ **Prisma ORM** with PostgreSQL
- 🌱 **Database Seeding** with sample data

## 📁 Project Structure

```
transread/
├── src/
│   ├── app/
│   │   ├── api/                 # Backend API routes
│   │   │   ├── hello/           # Simple test endpoint
│   │   │   ├── users/           # User management with Prisma
│   │   │   │   └── [id]/        # Individual user operations
│   │   │   └── posts/           # Post management with Prisma
│   │   ├── layout.tsx           # Root layout with providers
│   │   └── page.tsx             # Home page with modern UI
│   ├── components/
│   │   └── api-test.tsx         # HeroUI dashboard component
│   ├── lib/
│   │   ├── api-utils.ts         # API utilities and helpers
│   │   ├── database.ts          # Database configuration
│   │   └── prisma.ts            # Prisma client instance
│   ├── providers/
│   │   ├── query-provider.tsx   # TanStack Query provider
│   │   └── heroui-provider.tsx  # HeroUI provider
│   └── middleware.ts            # Request middleware (CORS, logging)
├── prisma/
│   ├── schema.prisma            # Database schema
│   ├── seed.ts                  # Database seeding script
│   └── migrations/              # Database migrations
├── .env.example                 # Environment variables template
└── README.md
```

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database configuration if needed
   ```

3. **Set up the database:**
   ```bash
   # Start Prisma's local PostgreSQL server
   npx prisma dev

   # In another terminal, run migrations and seed data
   npm run db:migrate
   npm run db:seed
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to [http://localhost:3001](http://localhost:3001)

## 📡 API Endpoints

### Base URL: `/api`

#### Hello Endpoint
- **GET** `/api/hello` - Test endpoint
- **POST** `/api/hello` - Echo endpoint

#### Users
- **GET** `/api/users` - List all users (with pagination)
- **POST** `/api/users` - Create a new user
- **GET** `/api/users/[id]` - Get user by ID
- **PUT** `/api/users/[id]` - Update user
- **DELETE** `/api/users/[id]` - Delete user

#### Posts
- **GET** `/api/posts` - List all posts (with filtering and pagination)
- **POST** `/api/posts` - Create a new post

### Example API Usage

```javascript
// Create a user
const response = await fetch('/api/users', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg'
  })
});

// Create a post
const postResponse = await fetch('/api/posts', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: 'My First Post',
    content: 'This is the content of my first post.',
    authorId: 'user-id-here',
    published: true
  })
});

// Get all users with pagination
const users = await fetch('/api/users?limit=10&offset=0');
```

## 🧪 Testing the API

The application includes a beautiful API dashboard built with HeroUI accessible from the home page. You can:

- View all users and posts with real-time data
- Create new users with the modal interface
- See user statistics (posts, comments count)
- View post details with author information
- Refresh data with TanStack Query integration
- Experience modern UI components and interactions

## 🗄️ Database Integration

The project comes with **Prisma ORM** and **PostgreSQL** fully configured:

### Database Features
- ✅ **Prisma ORM** with type-safe database access
- ✅ **PostgreSQL** database (local Prisma server)
- ✅ **Database migrations** for schema changes
- ✅ **Seeding script** with sample data
- ✅ **Relational data** (Users, Posts, Comments, Tags)

### Database Commands

```bash
# Start local PostgreSQL server
npx prisma dev

# Run database migrations
npm run db:migrate

# Generate Prisma client
npm run db:generate

# Seed database with sample data
npm run db:seed

# Open Prisma Studio (database GUI)
npm run db:studio
```

### Database Schema

The database includes these models:
- **Users** - User accounts with avatars
- **Posts** - Blog posts with slugs and publish status
- **Comments** - User comments on posts
- **Tags** - Post categorization
- **PostTags** - Many-to-many relationship between posts and tags

## 🔧 Available Scripts

### Development
- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Database
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:seed` - Seed database with sample data
- `npm run db:studio` - Open Prisma Studio

## 🌐 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Configure environment variables
4. Deploy automatically

### Other Platforms
The application can be deployed to any platform that supports Node.js:
- Railway
- Render
- DigitalOcean App Platform
- AWS Amplify
- Netlify

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Next.js API Routes](https://nextjs.org/docs/app/building-your-application/routing/route-handlers)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
