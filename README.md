# TransReed (skeleton)

This is a starter scaffold for the TransReed app described in `plan.md`.

What’s included:

- Next.js 14 App Router app with Tailwind CSS
- Minimal upload flow (`/` → `/api/upload` → `/doc/[id]`)
- Simple in-repo JSON store in `data/db.json` to simulate a database

Not yet wired (placeholders only):

- <PERSON><PERSON>/BullMQ worker, TTS, Stripe/PayPal

Getting started:

1. Install deps: `npm install` (network access required).
2. One‑shot DB setup: `npm run setup:db`
   - Ensures `DATABASE_URL` in `.env` (defaults to `postgresql://transreed:transreed@localhost:5432/transreed`).
   - If Dock<PERSON> is available, starts a local Postgres (`postgres:15`) container named `transreed-pg`.
   - Waits for <PERSON>, then runs Prisma generate + migrations (falls back to `db push`).
3. Run dev server: `npm run dev`.

Clerk authentication:

- Install dependency: `npm install @clerk/nextjs`
- Required env vars (already supported):
  - `CLERK_PUBLISHABLE_KEY=pk_test_...`
  - `CLERK_SECRET_KEY=sk_test_...`
- The app is wrapped with `<ClerkProvider>` in `app/layout.tsx` and reads `CLERK_PUBLISHABLE_KEY` automatically.
- Navbar shows Log in / Sign up when signed out, and a user menu when logged in.
- Log in and sign up routes are available at:
  - `app/log-in/[[...log-in]]/page.tsx`
  - `app/sign-up/[[...sign-up]]/page.tsx`

Import existing local documents (optional):

- Place PDF folders under `data/documents/<docId>/original.pdf` (already present in your case).
- Run `npm run data:import-docs` to insert them into Postgres.
  - Use `npm run data:import-docs -- --force` to re-import and overwrite existing rows.
  - After import, open `/doc/<docId>` in the browser.
  - If you already have page PDFs, translations, or audio under `data/documents/<docId>/pages`, run `npm run data:index-artifacts` to link them in the DB and update statuses.

Free an anonymous session (remove its doc):

- Anonymous users are limited to a single document per session. If you need to clear the attached document and start a fresh upload while signed out, run:

```
npm run data:free-anon -- --sid <YOUR_ANON_SESSION_ID>
```

- You can pass multiple IDs: `--sid id1 --sid id2` or `--sid id1,id2`.
- Where to find the session ID: check the browser’s localStorage key `transread_anon_id` or cookie `tr_anon_sid`.

Manual setup (alternative):

- Copy `.env.example` to `.env.local` and set:
  - `NEXT_PUBLIC_BASE_URL=http://localhost:3000`
  - `DATABASE_URL=postgresql://USER:PASSWORD@HOST:PORT/DBNAME`
- Generate client: `npm run db:generate`
- Apply schema: `npm run db:migrate` (or `npm run db:push`)

Project structure:

- `app/` — Next.js routes and pages
- `app/api/` — API routes
- `lib/` — simple JSON store and shared types
- `data/db.json` — created automatically on first write

Next steps (per plan.md):

- Add Clerk auth and anonymous sessions
- S3 upload for original and per-page artifacts
- Background worker (BullMQ + Redis) for splitting, translate, TTS
- Page-by-page audio playback UI with prefetch policy
- Usage tracking and billing integration
  Runtime prefetch controls:
- `NEXT_PUBLIC_PREFETCH_PERCENT` (default 0.55): when current page progress passes this fraction, prefetch next page.
- `NEXT_PUBLIC_MIN_REMAIN_SEC` (default 10): or when remaining seconds <= this, prefetch next page.
- `NEXT_PUBLIC_ELAPSED_FALLBACK_SEC` (default 5): if audio duration is unknown (e.g., some MP3s), prefetch next page after this many seconds of playback.

## S3 Uploads (per plan.md)

- The storage layer now mirrors saved artifacts to AWS S3 when configured, while keeping local copies for compatibility.
- Required env vars in `.env` or `.env.production`:
  - `AWS_REGION`
  - `S3_BUCKET_NAME`
  - `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` (or use an IAM role on your runtime)
- What gets uploaded (keys under `documents/{docId}/...`):
  - `original.pdf`
  - `pages/page-{n}/raw.pdf`
  - `pages/page-{n}/translations/{language}/sentences.json` and `meta.json` (and `text.txt` if used)
  - `pages/page-{n}/audio/{language}/{voice}/audio.(mp3|wav)`
- Note: API routes still read from local paths stored in the DB. S3 is a mirrored backup right now. If you want the app to stream directly from S3 (signed URLs/CloudFront), we can wire that next.

## Gemini TTS Method Selection

- Configure which Gemini integration is used for TTS in `lib/config.ts` via `appConfig.geminiTtsMethod`.
- Supported values: `auto`, `genai_live`, `genai_sdk`, `rest`, `gcp_oauth`, `gcp_classic`.

## Data Migration: transread → transreed

If you previously ran the app with the old database/user name `transread` and want to keep that data under the new naming `transreed`, run the SQL migration:

- Ensure you can connect as a superuser or a role with sufficient privileges to rename DBs and manage roles.
- Run the SQL against a maintenance DB (e.g., `postgres`), not `transread` itself:

```
psql "postgresql://USER:PASSWORD@HOST:PORT/postgres" -f scripts/sql/migrate_transread_to_transreed.sql
```

What it does:

- Terminates active connections to `transread`.
- Renames database `transread` → `transreed` (if present and target not already existing).
- Renames role `transread` → `transreed` if possible; otherwise (when connected as `transread`) creates `transreed`, reassigns object ownership to it, and skips dropping/renaming the current role with a notice.
- Ensures the `transreed` role owns the `transreed` DB.
- Switches into the `transreed` DB and attempts `REASSIGN OWNED BY transread TO transreed`; if not possible, falls back to granting full privileges to `transreed`.

Docker note:

- If you used the old Docker container `transread-pg`, run the command with the container’s exposed host/port. Alternatively, exec into the container and run psql from there.

After migration:

- Update `.env` to point `DATABASE_URL` at `.../transreed` with user `transreed`.
- Re-run: `npm run setup:db` (this will generate Prisma client and apply any migrations).

If you see “session user cannot be renamed”

- That means you ran the script while logged in as `transread`. The script now handles this by creating `transreed`, reassigning ownership, and skipping rename/drop of the current role. To remove the old role later, reconnect as a different superuser and run:
  - DROP OWNED BY transread;
  - DROP ROLE transread;

If you see “cannot reassign ownership … required by the database system”

- The script now catches that and continues. It also applies broad GRANTs so `transreed` can read/write existing app tables. If you still want to transfer ownerships, run (connected to `transreed` DB as superuser):
  - REASSIGN OWNED BY transread TO transreed;
  - ALTER SCHEMA public OWNER TO transreed;
