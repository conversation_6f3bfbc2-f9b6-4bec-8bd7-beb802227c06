# Queue-Based Architecture - Complete Refactoring

## Overview

The application has been refactored to use a **pure queue-based architecture** with BullMQ and Redis. All document processing now happens asynchronously in background worker processes, with the frontend polling for status and retrieving audio when ready.

## Key Principles

1. **Separation of Concerns**:
   - Upload API: Accepts files and enqueues jobs
   - Worker: Processes jobs in background
   - Status API: Returns current processing status
   - Audio API: Returns audio URLs (read-only, no processing)

2. **Read-Only Frontend**:
   - Frontend NEVER triggers processing
   - Frontend ONLY polls status and retrieves audio
   - All processing happens in worker queue

3. **Progressive Processing**:
   - First page processed first (fast feedback to user)
   - Remaining pages queued after first page completes
   - All pages processed in background

## Architecture Diagram

```
┌─────────────┐
│  Frontend   │
│   Upload    │
└──────┬──────┘
       │ POST /api/upload (file + params)
       ▼
┌──────────────────────────┐
│  Upload API              │
│  1. Save file            │
│  2. Create document      │
│  3. Enqueue job          │◄─── Returns {docId} IMMEDIATELY
│  4. Return docId         │
└──────┬───────────────────┘
       │
       ▼
┌──────────────────────────┐         ┌─────────────────────┐
│  Redis Queue (BullMQ)    │◄────────│  Worker Process     │
│                          │         │  (Background)       │
└──────┬───────────────────┘         └─────────────────────┘
       │
       │ Job Chain:
       │
       ├─► 1. process-document
       │      - Load original PDF
       │      - Split into single-page PDFs
       │      - Save each page
       │      - Enqueue translate-first-page
       │
       ├─► 2. translate-first-page
       │      - Find first non-skippable page
       │      - Translate with Gemini
       │      - Save translation
       │      - Enqueue generate-audio-first-page
       │
       ├─► 3. generate-audio-first-page
       │      - Generate audio chunks for first page
       │      - Save first chunk
       │      - Update status to 'generated_audio_first_page'
       │      - Enqueue remaining pages
       │
       └─► 4. process-page (x N pages)
              - Translate page
              - Generate audio chunks
              - Save to storage

┌─────────────┐
│  Frontend   │
│  Polling    │◄── GET /api/doc/[id] every 1.5s
└──────┬──────┘    (returns {status, firstPageAudioReady})
       │
       │ When status = 'generated_audio_first_page'
       │ AND firstPageAudioReady = true
       │
       ▼
┌─────────────┐
│  Frontend   │
│  Retrieve   │◄── GET /api/page/[id]/audio
│  Audio      │    (returns {ok, chunked, urls})
└──────┬──────┘
       │
       ▼
┌─────────────┐
│  Play       │
│  Audio      │
└─────────────┘
```

## Document Status Flow

```
uploaded
  ↓
  ↓ Worker: process-document
  ↓ (Split PDF into single-page PDFs)
  ↓
translating_first_page
  ↓
  ↓ Worker: translate-first-page
  ↓ (Find and translate first non-skippable page)
  ↓
translated_first_page
  ↓
  ↓ Worker: generate-audio-first-page
  ↓ (Generate audio chunks for first page)
  ↓
generating_audio_first_page
  ↓
  ↓ (First chunk saved)
  ↓
generated_audio_first_page ✓ READY!
  ↓
  ↓ Worker: process-page (for each remaining page)
  ↓ (Translate + generate audio in background)
  ↓
[All pages processed]
```

## API Endpoints

### 1. POST /api/upload

**Purpose**: Upload document and enqueue processing

**Request**:
```typescript
FormData {
  file: File,
  targetLanguage: string,
  voice: 'male' | 'female'
}
```

**Response**:
```json
{
  "docId": "doc-123"
}
```

**Behavior**:
- Saves original PDF to storage
- Creates Document record with `status='uploaded'`
- Enqueues `process-document` job
- Returns immediately (does NOT wait for processing)

### 2. GET /api/doc/[id]

**Purpose**: Get document status for polling

**Response**:
```json
{
  "document": {
    "id": "doc-123",
    "status": "generated_audio_first_page",
    "pageCount": 100,
    "suggestedStartPageId": "page-5",
    ...
  },
  "pages": [...],
  "chapters": [...],
  "firstPageAudioReady": true
}
```

**Status Values**:
- `uploaded` - PDF uploaded, queued for processing
- `translating_first_page` - Finding and translating first page
- `translated_first_page` - Translation complete
- `generating_audio_first_page` - Generating audio for first page
- `generated_audio_first_page` - First page ready! (check `firstPageAudioReady`)

**Frontend Polling**:
- Poll every 1.5 seconds
- Show processing UI while `status !== 'generated_audio_first_page' || !firstPageAudioReady`
- Start playing when `status === 'generated_audio_first_page' && firstPageAudioReady === true`

### 3. GET /api/page/[id]/audio

**Purpose**: Retrieve audio chunks (read-only, no processing)

**Response (Ready)**:
```json
{
  "ok": true,
  "chunked": true,
  "total": 5,
  "available": [1, 2, 3, 4, 5],
  "urls": [
    "/api/page/page-1/chunk/1",
    "/api/page/page-1/chunk/2",
    ...
  ],
  "status": "ready",
  "processingStage": "complete"
}
```

**Response (Processing)**:
```json
{
  "ok": false,
  "status": "processing",
  "processingStage": "translating"
}
```

**Response (Not Started)**:
```json
{
  "ok": false,
  "status": "not_started",
  "processingStage": "not_started"
}
```

**Response (Skippable)**:
```json
{
  "ok": true,
  "skip": true,
  "skippable": true
}
```

**Behavior**:
- Read-only endpoint (GET, no processing)
- Returns audio if ready
- Returns status if still processing
- Frontend polls this endpoint if page not ready

## Job Types

### 1. process-document

**Trigger**: After upload

**Responsibilities**:
1. Load original PDF from storage
2. Split into single-page PDFs using pdf-lib
3. Save each page PDF to storage
4. Update Page records with `s3KeySinglePagePdf`
5. Enqueue `translate-first-page` job

**File**: `lib/jobs/process-document.ts`

### 2. translate-first-page

**Trigger**: After process-document completes

**Responsibilities**:
1. Update document status to `translating_first_page`
2. Find first non-skippable page (process in batches of 5)
3. Translate that page using Gemini AI
4. Save translation to storage
5. Create/update PageAudio record
6. Update document status to `translated_first_page`
7. Set `suggestedStartPageId`
8. Enqueue `generate-audio-first-page` job

**File**: `lib/jobs/translate-first-page.ts`

### 3. generate-audio-first-page

**Trigger**: After translate-first-page completes

**Responsibilities**:
1. Update document status to `generating_audio_first_page`
2. Prepare TTS text from translation
3. Apply chunking strategy (Vbee or Gemini)
4. Synthesize first chunk
5. Save first chunk to storage
6. Update document status to `generated_audio_first_page`
7. Continue remaining chunks in background
8. **Enqueue all remaining pages** for background processing

**File**: `lib/jobs/generate-audio-first-page.ts`

### 4. process-page

**Trigger**: After first page audio is generated (for all remaining pages)

**Responsibilities**:
1. Load page PDF
2. Translate with Gemini (with carryover handling)
3. Save translation
4. Generate audio chunks
5. Save chunks to storage

**File**: `lib/jobs/process-page.ts`

**Note**: Runs for EVERY page except the first page, all in background

## Worker Process

**File**: `workers/document-processor.ts`

**Configuration**:
- Concurrency: 5 jobs in parallel
- Rate limit: 10 jobs per second
- Retry: 3 attempts with exponential backoff
- Completed jobs: Removed after 24 hours
- Failed jobs: Kept for 7 days

**Commands**:
```bash
# Development (with auto-reload)
npm run worker:dev

# Production
npm run worker
```

**Deployment**:
- Must run as separate service from Next.js app
- Cannot run on Vercel (long-running processes not supported)
- Recommended: Render (Background Worker), Railway, AWS EC2/ECS

## Storage Structure

All files stored in S3 (or local in dev):

```
documents/{docId}/
├── original.pdf
├── pages/
│   └── page-{n}/
│       ├── raw.pdf                           # Single-page PDF for AI
│       ├── translations/{language}/
│       │   ├── sentences.json                # Translation
│       │   └── meta.json                     # Metadata
│       └── audio/{language}/{voice}/
│           └── chunks/
│               ├── meta.json                 # {total: N}
│               ├── chunk-1.mp3
│               ├── chunk-2.mp3
│               └── chunk-N.mp3
```

## Frontend Flow

### Upload Flow

```typescript
// 1. Upload file
const formData = new FormData();
formData.append('file', file);
formData.append('targetLanguage', 'vi');
formData.append('voice', 'female');

const response = await fetch('/api/upload', {
  method: 'POST',
  body: formData,
});

const { docId } = await response.json();

// 2. Redirect to document page
router.push(`/doc/${docId}`);
```

### Status Polling

```typescript
// Poll every 1.5 seconds
useEffect(() => {
  const interval = setInterval(async () => {
    const response = await fetch(`/api/doc/${docId}`);
    const data = await response.json();

    const isProcessing =
      data.document.status === 'uploaded' ||
      data.document.status === 'translating_first_page' ||
      data.document.status === 'translated_first_page' ||
      data.document.status === 'generating_audio_first_page' ||
      (data.document.status === 'generated_audio_first_page' &&
       !data.firstPageAudioReady);

    setProcessing(isProcessing);

    // Update UI based on status
    if (data.document.status === 'translating_first_page') {
      setProcessingStage('translating');
    } else if (data.document.status === 'generating_audio_first_page') {
      setProcessingStage('generating_audio');
    }
  }, 1500);

  return () => clearInterval(interval);
}, [docId]);
```

### Audio Retrieval

```typescript
// When user navigates to a page
const response = await fetch(`/api/page/${pageId}/audio`);
const data = await response.json();

if (data.ok && data.urls) {
  // Audio ready - start playing
  audioRef.current.src = data.urls[0];
  audioRef.current.play();
} else if (data.status === 'processing') {
  // Still processing - show loading and poll
  setProcessingStage(data.processingStage);
  setTimeout(() => fetchAudio(), 2000);
} else if (data.status === 'not_started') {
  // Page not processed yet (shouldn't happen after first page)
  setProcessingStage('not_started');
  setTimeout(() => fetchAudio(), 2000);
}
```

## Setup Instructions

### 1. Install Dependencies

Already installed:
```bash
npm install bullmq ioredis
```

### 2. Configure Environment

Add to `.env`:
```bash
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

### 3. Start Redis

Using Docker:
```bash
docker run -d -p 6379:6379 redis:7-alpine
```

Or install locally:
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu
sudo apt-get install redis-server
sudo systemctl start redis
```

### 4. Start Services

Terminal 1 - Worker:
```bash
npm run worker:dev
```

Terminal 2 - Next.js:
```bash
npm run dev
```

## Monitoring

### Check Redis Connection

```bash
redis-cli ping  # Should return PONG
```

### Check Queue Status

```bash
redis-cli
> KEYS "bull:document-processing:*"
> LLEN bull:document-processing:wait
> LLEN bull:document-processing:active
> LLEN bull:document-processing:completed
> LLEN bull:document-processing:failed
```

### Worker Logs

The worker outputs detailed logs for each job:

```
2025-11-23T00:10:27.667Z [worker] Worker ready and listening for jobs
2025-11-23T00:15:32.123Z [worker] Processing job: process-document (job-123)
2025-11-23T00:15:32.456Z [process-document] Starting job { docId: 'doc-123' }
2025-11-23T00:15:33.789Z [process-document] PDF loaded { docId: 'doc-123', pageCount: 250 }
```

## Migration from Old System

### What Changed

**Old System**:
- Upload API did synchronous "priming" (fire-and-forget)
- Frontend called `POST /api/page/[id]/ensure-tts` to trigger processing
- Processing happened on-demand when user navigated

**New System**:
- Upload API enqueues job and returns immediately
- Worker processes everything in background
- Frontend calls `GET /api/page/[id]/audio` (read-only, no processing)
- All pages processed automatically after first page

### Benefits

1. **Better UX**: Fast upload response, clear progress indicators
2. **Reliability**: Failed jobs retry automatically, errors logged
3. **Scalability**: Multiple workers can process in parallel
4. **Monitoring**: Full visibility into queue status and failures
5. **Resource Efficiency**: Background processing doesn't block API routes

## Troubleshooting

### Worker Not Processing

1. Check Redis:
   ```bash
   redis-cli ping
   ```

2. Check worker logs for errors

3. Verify jobs are in queue:
   ```bash
   redis-cli LLEN bull:document-processing:wait
   ```

### Frontend Stuck on "Processing..."

1. Check document status in database:
   ```sql
   SELECT id, status FROM documents WHERE id = 'doc-id';
   ```

2. Check worker logs for errors

3. Manually retry job if needed:
   ```bash
   # Clear failed job and re-enqueue
   ```

### Pages Not Being Processed

1. Check if `generate-audio-first-page` job completed successfully

2. Check worker logs for `process-page` jobs

3. Verify jobs were enqueued:
   ```bash
   redis-cli KEYS "bull:document-processing:*process-page*"
   ```

## Production Deployment

### Environment Variables

Required:
```bash
DATABASE_URL=postgresql://...
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
GEMINI_API_KEY=your-api-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket
AWS_ACCESS_KEY_ID=...
AWS_SECRET_ACCESS_KEY=...
```

### Worker Deployment

Deploy worker as separate service:

**Render** (Recommended):
1. Create "Background Worker" service
2. Build command: `npm install`
3. Start command: `npm run worker`

**Railway**:
1. Create new service
2. Set start command: `npm run worker`

**AWS EC2/ECS**:
1. Create EC2 instance or ECS task
2. Run: `npm run worker`
3. Use PM2 for process management

### Scaling

**Horizontal Scaling**:
- Run multiple worker instances
- BullMQ automatically distributes jobs

**Vertical Scaling**:
- Increase worker concurrency (5-20 recommended)
- Adjust rate limits based on API quotas

**Redis**:
- Use Redis Cluster for high availability
- Consider managed Redis (AWS ElastiCache, Redis Cloud)

## Files Reference

### Created Files

- `lib/queue/config.ts` - Queue configuration
- `lib/queue/jobs.ts` - Job type definitions
- `lib/jobs/process-document.ts` - PDF splitting job
- `lib/jobs/translate-first-page.ts` - Translation job
- `lib/jobs/generate-audio-first-page.ts` - TTS job (first page)
- `lib/jobs/process-page.ts` - Process individual page (remaining pages)
- `workers/document-processor.ts` - Worker process
- `app/api/page/[id]/audio/route.ts` - Audio retrieval API (NEW)

### Modified Files

- `app/api/upload/route.ts` - Enqueues job instead of sync processing
- `app/api/doc/[id]/route.ts` - Added `firstPageAudioReady` flag
- `app/doc/[id]/hooks/useInteractiveCore.tsx` - Uses GET /api/page/[id]/audio
- `app/doc/[id]/hooks/utils/ensureAudio.ts` - Uses GET /api/page/[id]/audio
- `.env` - Added Redis configuration
- `package.json` - Added worker scripts

### Old Files (Can be deprecated)

- `app/api/page/[id]/ensure-tts/route.ts` - Now replaced by audio endpoint
