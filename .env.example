# Web app
NEXT_PUBLIC_BASE_URL=http://localhost:3000
# Allow email aliases (+tag) on signup in dev
NEXT_PUBLIC_ALLOW_EMAIL_ALIAS=true

# Clerk (auth)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=

# Database (Prisma) — to be wired later
DATABASE_URL=

# Redis (BullMQ job queue)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AWS S3 — to be wired later
AWS_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_BUCKET_NAME=

PRIME_ON_UPLOAD=false
# Optional: tune priming load if enabled
PRIME_ROUNDS=2
PRIME_BATCH_SIZE=5

# Vbee TTS (Primary TTS Provider)
# Get API credentials from https://vbee.vn
VBEE_APP_ID=
VBEE_TOKEN=
# Timeout for waiting for Vbee webhook/first chunk (milliseconds, default: 120000 = 120 seconds)
VBEE_FIRST_CHUNK_TIMEOUT_MS=120000

# AI — Gemini + optional Google Cloud TTS fallback
GEMINI_API_KEY=
# Optional: Additional Gemini API keys for load balancing and parallel processing
# Add GEMINI_API_KEY2, GEMINI_API_KEY3, etc. to enable API key rotation
GEMINI_API_KEY2=
# Optional: Delay in milliseconds between starting each TTS chunk request (default: 1000)
# Increase this if Gemini returns all chunks simultaneously, decrease for faster processing
# Recommended range: 500-3000ms
TTS_CHUNK_STAGGER_DELAY_MS=1000
# Optional fallback; if unset we skip Google Cloud TTS
GCP_TTS_API_KEY=

# Google Cloud Gemini TTS via OAuth or Service Account (preferred)
# Either provide a short-lived OAuth token and project id...
# GCP_TTS_OAUTH_TOKEN=
# ...or provide Service Account credentials JSON (inline or path) and project id
# GCP_TTS_SERVICE_ACCOUNT_JSON=
# GOOGLE_APPLICATION_CREDENTIALS=
# Project to bill requests to (x-goog-user-project)
GCP_TTS_PROJECT_ID=

# Gemini GenAI SDK TTS
# GEMINI_TTS_MODEL=gemini-2.5-pro-preview-tts

# Optional: path to ffmpeg binary for WAV->MP3 transcode fallback
# FFMPEG_PATH=ffmpeg
# FingerprintJS (Open Source) uses CDN by default via lib/anon.ts

# Stripe/PayPal — to be wired later
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=

# Polar.sh Payment Integration
# Get your access token from https://polar.sh/settings
POLAR_ACCESS_TOKEN=polar_at_xxx
# Webhook secret for verifying webhook signatures
# Get this from Polar dashboard → Webhooks → Add Endpoint
# Required for production to verify webhook authenticity
POLAR_WEBHOOK_SECRET=
# Success URL will be used after successful payment
# {CHECKOUT_ID} will be replaced with actual checkout ID
# POLAR_SUCCESS_URL is now auto-generated from NEXT_PUBLIC_BASE_URL

# Polar server mode: 'sandbox' for testing, 'production' for live (or leave empty for default)
POLAR_SERVER=sandbox

# Polar Product IDs (or Price IDs) for each subscription tier
# Get these from your Polar dashboard after creating products
# Can use either Product ID (prod_xxx) or Price ID (priceId_xxx)
NEXT_PUBLIC_POLAR_PRODUCT_ID_STARTER=
NEXT_PUBLIC_POLAR_PRODUCT_ID_PRO=
NEXT_PUBLIC_POLAR_PRODUCT_ID_PREMIUM=
NEXT_PUBLIC_POLAR_PRODUCT_ID_ENTERPRISE=

# Google reCAPTCHA v3
# Mode: classic | enterprise (choose one)
RECAPTCHA_MODE=classic
# Public site key used by the browser (classic or enterprise site key)
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=
# Classic v3 secret key (only for classic mode)
RECAPTCHA_SECRET_KEY=
# Enterprise project id (only for enterprise mode)
RECAPTCHA_ENTERPRISE_PROJECT_ID=
# Optional: minimum score threshold (0.0 - 1.0). Default 0.5
RECAPTCHA_MIN_SCORE=0.5

# Access tokens (JWT HS256)
ACCESS_TOKEN_SECRET=dev_secret_change_me
ACCESS_TOKEN_TTL_SECONDS=1800
ACCESS_TOKEN_ANON_MAX_SECONDS=60
ACCESS_TOKEN_ANON_MAX_REQUESTS=50
