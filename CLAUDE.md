# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TransReed is a PDF-to-translated-audio web app built with Next.js 14. Users upload PDFs, select target language and voice, and receive page-by-page translated audio with intelligent prefetch, TOC navigation, and playback controls.

## Essential Commands

### Development

- `npm run dev` — Start dev server at http://localhost:3000
- `npm run build` — Generate Prisma client and build Next.js for production
- `npm run lint` — Run ESLint
- `npm run lint:fix` — Auto-fix linting issues

### Redis Setup

Redis is **automatically started** when you run `npm run dev` via Docker.

Manual commands (usually not needed):
- `docker start transread-redis` — Start existing Redis container
- `docker ps | grep redis` — Check if Redis is running
- `docker-compose up -d redis` — Start Redis via Docker Compose

### Database & Setup

- `npm run setup:db` — One-shot setup: creates/prepares Postgres (optionally via Docker), runs Prisma generate + migrations
- `npm run db:generate` — Generate Prisma client
- `npm run db:migrate` — Apply schema migrations (production)
- `npm run db:migrate:dev` — Apply schema migrations with dev prompts
- `npm run db:push` — Push schema to DB without migrations (dev)
- `npm run db:reset` — Reset database (destructive)

### Data Import

- `npm run data:import-docs` — Import PDFs from `data/documents/<docId>/original.pdf` into DB
- `npm run data:import-docs -- --force` — Re-import and overwrite existing
- `npm run data:index-artifacts` — Index pre-existing page PDFs/translations/audio under `data/documents/<docId>/pages`

### Production

- `npm run build:prod` — Build with `.env.production`
- `npm run prisma:prod` — Generate Prisma client and deploy migrations with `.env.production`

## Architecture Overview

### Core Processing Pipeline

1. **Upload** → User uploads PDF via `/api/upload`
2. **Splitting** → Backend splits PDF into single-page PDFs using `pdf-lib`
3. **Translation + Chapter Detection** → AI (Gemini) extracts text from each page PDF, translates to target language, detects chapter starts/skippable pages, and handles sentence carryover between pages
4. **TTS** → Gemini TTS synthesizes translated text to audio (MP3)
5. **Playback** → Frontend streams page-by-page audio with intelligent prefetch (at 55% progress or ≤10s remaining)

### Key Processing Logic

- **Sentence boundaries**: Pages always end at complete sentences. Incomplete sentences carry over to next page for TTS continuity
- **Chapter detection**: AI analyzes typography and content to detect chapter starts, headings, and skippable front matter
- **Smart start**: Auto-detects and suggests skipping front matter to jump to first meaningful content
- **Carryover handling**: Last incomplete sentence from page N prepends to page N+1 during translation and TTS

### Directory Structure

- `app/` — Next.js 14 App Router pages and API routes
  - `app/api/` — API endpoints (upload, audio, doc, page, auth, health)
  - `app/doc/[id]/` — Document viewer with `Client.tsx` (server) and `Interactive.tsx` (client player)
- `lib/` — Shared utilities and core business logic
  - `lib/ai/` — AI integrations (Gemini translation, TTS, audio utilities)
  - `lib/processor.ts` — Main document processing pipeline
  - `lib/file-service.ts` — Unified storage abstraction (local/S3/both)
  - `lib/db.ts` — Database helpers wrapping Prisma
  - `lib/pdf.ts` — PDF manipulation utilities
- `prisma/` — Prisma schema and migrations
- `scripts/` — Setup and data import utilities
- `data/` — Local document storage (dev mode)
- `types/` — Shared TypeScript types

### Data Model (Prisma)

Core entities:

- **User** — Clerk auth, subscription plan, billing, usage tracking
- **Plan** — Subscription tiers with listening time limits
- **Document** — Uploaded PDFs with metadata, status, page count, suggested start page
- **Page** — Individual page with PDF, text, translation, audio, role (front_matter/content/appendix), chapter detection
- **Chapter** — TOC entries from PDF outline or AI inference
- **PageAudio** — TTS audio with language, voice, engine, cached by content hash
- **Usage**, **CreditLedger**, **PaymentEvent** — Billing and usage tracking

Important relationships:

- Documents have many Pages and Chapters
- Pages have PageAudio for each language/voice/engine combination
- Users track Usage cycles and CreditLedger for billing

### Storage Architecture

Configured via `STORAGE_MODE` env var:

- `local` — Store files in `data/` directory (dev default)
- `s3` — Store files in AWS S3 (production default)
- `local_and_s3` — Write to both, read prefers local

File structure:

```
documents/{docId}/
├── original.pdf
├── pages/
│   └── page-{n}/
│       ├── raw.pdf                    # AI input for translation
│       ├── translations/{language}/
│       │   ├── sentences.json         # Translated sentences
│       │   └── meta.json             # Translation metadata (isChapterStart, etc.)
│       └── audio/{language}/{voice}/
│           └── audio.mp3
└── audio/full/{language}/{voice}/
    └── complete.mp3                   # Full concatenated audio
```

### AI Processing

**Translation** (`lib/ai/gemini.ts`):

- Model: `gemini-2.5-flash-lite`
- Input: Single-page PDF buffer + optional carryover text
- Output: Strict JSON with `sentences`, `translatedSentences`, `endsWithCompleteSentence`, `isChapterStart`, `isSkippable`, `detectedHeading`, `headingConfidence`
- Handles sentence splitting and 1:1 mapping between source and target

**TTS** (`lib/ai/tts.ts`):

- Providers: Gemini GenAI SDK (`gemini-2.5-pro-preview-tts`) or Google Cloud TTS API
- Voice selection: Male/female via style prompt
- Chunking: Splits long text into ~260-word chunks for processing
- Handles carryover prepend and incomplete sentence exclusion

### Authentication & Usage Limits

- **Auth**: Clerk with anonymous session support
- **Anonymous**: 5 minutes free preview
- **Free Account**: 10 minutes total (5 + 5 after signup)
- **Paid Plans**: 1-15 hours/month depending on tier
- Usage counted from `suggestedStartPage` (skips front matter)

### Environment Configuration

Required for production:

- `DATABASE_URL` — PostgreSQL connection string
- `CLERK_PUBLISHABLE_KEY`, `CLERK_SECRET_KEY` — Authentication
- `GEMINI_API_KEY` — AI translation and TTS
- `AWS_REGION`, `S3_BUCKET_NAME`, `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY` — S3 storage

Optional:

- `STORAGE_MODE` — `local` (dev) / `s3` (prod) / `local_and_s3`
- `TTS_CHUNK_WORDS` — Words per TTS chunk (default: 260)
- `NEXT_PUBLIC_FREE_TRIAL_SECONDS` — Anonymous trial duration
- `GCP_TTS_*` — Google Cloud TTS fallback config
- `GEMINI_API_KEY2`, `GEMINI_API_KEY3`, ... — Additional Gemini API keys for load balancing and parallel processing. When multiple keys are provided, the system automatically rotates between them when processing audio chunks in parallel, distributing load and avoiding rate limits

See `.env.example` for full list.

## Code Conventions

- **Language**: TypeScript + React Server/Client Components
- **Indentation**: 2 spaces
- **Strings**: Single quotes
- **Naming**: `camelCase` for vars/functions, `PascalCase` for components/types, `kebab-case` for route files
- **Imports**: Prefer absolute paths from app root (e.g., `@/lib/db`)
- **Linting**: ESLint with `eslint-config-next` and TypeScript rules

## Development Workflow

1. Copy `.env.example` to `.env` and configure required vars
2. Run `npm run setup:db` to initialize Postgres and apply schema
3. Seed subscription plans: `npx tsx scripts/seed-plans.ts`
4. (Optional) Place PDFs in `data/documents/<docId>/original.pdf` and run `npm run data:import-docs`
5. Start dev server with `npm run dev`
6. Upload PDFs via UI or import existing ones
7. Run `npm run lint:fix` before committing

## Key Implementation Details

### Processing Pipeline (`lib/processor.ts`)

`processDocumentPipeline()` orchestrates:

1. Save original PDF to storage
2. Split into single-page PDFs with `pdf-lib`
3. For each page sequentially:
   - Extract page to single PDF
   - Call Gemini to translate + detect chapters (with carryover from previous page)
   - Save translation and metadata
   - Synthesize audio if `targetLanguage` and `voice` provided
   - Track carryover for next page

### Audio Prefetch Strategy

Frontend prefetches next page audio when:

- Current page progress > 55% (configurable via `NEXT_PUBLIC_PREFETCH_PERCENT`)
- OR remaining seconds ≤ 10 (configurable via `NEXT_PUBLIC_MIN_REMAIN_SEC`)
- OR elapsed time > 5s when duration unknown (configurable via `NEXT_PUBLIC_ELAPSED_FALLBACK_SEC`)

### Parallel Audio Processing with API Key Rotation

To accelerate audio generation and avoid rate limits:

- Text for each page is split into chunks (~60-160 words per chunk)
- All chunks are processed in **true parallel** (not sequential batches)
- API keys are **rotated** across chunks using round-robin strategy
- Chunk N uses `GEMINI_API_KEY[N % keyCount]` for load distribution
- Supports unlimited API keys via `GEMINI_API_KEY`, `GEMINI_API_KEY2`, `GEMINI_API_KEY3`, etc.
- Implementation: `lib/ai/api-key-rotation.ts` provides `ApiKeyRotator` class with singleton pattern
- Used in: `app/api/page/[id]/ensure-tts/route.ts` (lines 768-897)
- **Singleton**: A single `ApiKeyRotator` instance is shared across all requests for proper round-robin distribution

Benefits:
- **Speed**: All chunks process simultaneously instead of waiting in batches
- **Reliability**: Distributes load across multiple API keys to avoid individual key rate limits
- **Scalability**: Add more keys to increase throughput proportionally
- **Efficiency**: Singleton pattern ensures one rotator instance is reused across all requests

### Chapter Detection

Two modes:

- **PDF outline**: Extract from PDF metadata (`tocSource='pdf_outline'`)
- **AI inference**: Gemini analyzes each page for chapter starts based on typography and keywords (`tocSource='ai_inferred'`)

Hybrid approach preferred: use PDF outline if available, supplement with AI detection.

### Anonymous User Handling

- Anonymous users get temporary session via `lib/anon.ts` (FingerprintJS)
- Documents expire after configurable period (`expiresAt` column)
- Cleanup job should periodically delete expired anonymous documents

## Testing

No formal test suite yet. For new features:

- Add lightweight integration tests as `*.test.ts` beside the unit under test
- Focus on critical routes: upload, doc view, audio retrieval, translation pipeline
- Use Playwright or Next.js API route testing

## Docker & Deployment

- **Docker**: `./docker-build.sh` then `docker-compose up -d`
- **Hosting**: Vercel (Next.js) + dedicated worker instances (Render/Railway/EC2)
- **Database**: Postgres 15+ (included in Docker setup or managed service)
- **Worker**: Fully implemented using BullMQ + Redis for background job processing
  - **Status**: Required for both development and production
  - **Queue**: BullMQ with Redis backend
  - **Setup**: See Redis Setup and Troubleshooting sections

## Migration Notes

If migrating from old `transread` → `transreed` naming:

- Use `scripts/sql/migrate_transread_to_transreed.sql` to rename DB and role
- Update `DATABASE_URL` in `.env` to new database name
- Re-run `npm run setup:db`

See README.md "Data Migration" section for details.

## Troubleshooting

### Redis Connection Errors

**Symptom:** `ECONNREFUSED 127.0.0.1:6379` during development

**This should not happen** - Redis auto-starts with `npm run dev`.

**If it does happen:**

1. **Check Docker:**
   ```bash
   docker ps | grep redis
   ```

2. **Restart Redis:**
   ```bash
   docker restart transread-redis
   ```

3. **Check port conflict:**
   ```bash
   lsof -i :6379  # macOS/Linux
   ```

4. **Manual start:**
   ```bash
   docker start transread-redis
   npm run dev
   ```

5. **If Docker not installed:**
   - Install Docker: https://docs.docker.com/get-docker/
   - Or install Redis locally:
     - macOS: `brew install redis && brew services start redis`
     - Ubuntu: `sudo apt install redis-server && sudo systemctl start redis`

**Production (K8s):**
- ✅ Redis auto-deploys via `k3s-deploy.sh` - no setup needed
- Service name: `redis` (accessible at `redis:6379` within cluster)
- Persistent storage: Uses emptyDir volume (consider PersistentVolumeClaim for production)
