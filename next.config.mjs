/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  eslint: {
    // Don't block builds on ESLint warnings
    // Run `npm run lint` separately to check for issues
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Don't fail builds on type errors in production
    // Run `tsc --noEmit` separately for type checking
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
};

export default nextConfig;
