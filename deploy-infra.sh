#!/bin/bash

# Deploy Infrastructure (Grafana + Caddy) to VPS
# This script updates only <PERSON><PERSON> and <PERSON><PERSON><PERSON> without affecting transread-app

set -e

# Change to script directory
cd "$(dirname "$0")"

# VPS Configuration
VPS_HOST="***************"
VPS_USER="root"
VPS_PASSWORD="TDCloud@1720"
VPS_DEPLOY_DIR="/root/transread"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Deploying Infrastructure (Grafana + Caddy) ===${NC}"
echo -e "${YELLOW}VPS: ${VPS_USER}@${VPS_HOST}${NC}"
echo ""

# Check VPS connectivity
echo -e "${BLUE}Checking VPS connectivity...${NC}"
if ! sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${VPS_USER}@${VPS_HOST} "echo 'Connected'" > /dev/null 2>&1; then
    echo -e "${RED}Error: Cannot connect to VPS${NC}"
    exit 1
fi
echo -e "${GREEN}✓ VPS accessible${NC}"

# Copy files to VPS
echo -e "${BLUE}Copying files to VPS...${NC}"
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no docker-compose.vps.yml ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/docker-compose.yml
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no Caddyfile ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no loki-config.yml ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no promtail-config.yml ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/
sshpass -p "${VPS_PASSWORD}" scp -o StrictHostKeyChecking=no grafana-datasources.yml ${VPS_USER}@${VPS_HOST}:${VPS_DEPLOY_DIR}/
echo -e "${GREEN}✓ Files copied to VPS${NC}"

# Deploy infrastructure on VPS
echo -e "${BLUE}Deploying infrastructure on VPS...${NC}"
sshpass -p "${VPS_PASSWORD}" ssh -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} << 'EOF'
cd /root/transread

# Stop and remove old portainer if exists
echo "Removing old portainer container..."
docker-compose stop portainer 2>/dev/null || true
docker-compose rm -f portainer 2>/dev/null || true

# Start Loki stack
echo "Starting Loki..."
docker-compose up -d loki

echo "Starting Promtail..."
docker-compose up -d promtail

echo "Starting Grafana..."
docker-compose up -d grafana

# Reload Caddy configuration
echo "Reloading Caddy configuration..."
docker-compose up -d caddy

# Wait for services
echo ""
echo "Waiting for services to be ready..."
sleep 10

# Show status
echo ""
echo "Service status:"
docker-compose ps loki promtail grafana caddy

# Show logs
echo ""
echo "Loki logs:"
docker-compose logs --tail=10 loki

echo ""
echo "Promtail logs:"
docker-compose logs --tail=10 promtail

echo ""
echo "Grafana logs:"
docker-compose logs --tail=10 grafana

echo ""
echo "✓ Infrastructure deployment complete"
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Infrastructure deployment successful${NC}"
else
    echo -e "${RED}Error: Infrastructure deployment failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== Infrastructure Deployment Complete! ===${NC}"
echo -e "${YELLOW}Grafana: https://grafana.transreed.com (or http://***************:3001)${NC}"
echo -e "${YELLOW}Login: admin / @Nautilus1995${NC}"
echo ""
echo -e "${BLUE}Services:${NC}"
echo -e "- Loki: Log aggregation (http://localhost:3100)"
echo -e "- Promtail: Log collection from Docker containers"
echo -e "- Grafana: Visualization (auto-configured with Loki datasource)"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo -e "1. Access Grafana at https://grafana.transreed.com"
echo -e "2. Go to 'Explore' and select 'Loki' datasource"
echo -e "3. Query logs with: {container=\"transread-app-1\"}"
echo -e "4. View all containers: {job=\"docker_all\"}"
echo ""
