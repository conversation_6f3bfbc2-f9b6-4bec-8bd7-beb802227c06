import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create users
  const users = await Promise.all([
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        avatar: 'https://i.pravatar.cc/150?u=john',
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        avatar: 'https://i.pravatar.cc/150?u=jane',
      },
    }),
    prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: '<PERSON>',
        avatar: 'https://i.pravatar.cc/150?u=bob',
      },
    }),
  ])

  console.log('✅ Created users:', users.length)

  // Create tags
  const tags = await Promise.all([
    prisma.tag.upsert({
      where: { slug: 'nextjs' },
      update: {},
      create: {
        name: 'Next.js',
        slug: 'nextjs',
        color: '#000000',
      },
    }),
    prisma.tag.upsert({
      where: { slug: 'react' },
      update: {},
      create: {
        name: 'React',
        slug: 'react',
        color: '#61DAFB',
      },
    }),
    prisma.tag.upsert({
      where: { slug: 'typescript' },
      update: {},
      create: {
        name: 'TypeScript',
        slug: 'typescript',
        color: '#3178C6',
      },
    }),
    prisma.tag.upsert({
      where: { slug: 'prisma' },
      update: {},
      create: {
        name: 'Prisma',
        slug: 'prisma',
        color: '#2D3748',
      },
    }),
  ])

  console.log('✅ Created tags:', tags.length)

  // Create posts
  const posts = await Promise.all([
    prisma.post.upsert({
      where: { slug: 'welcome-to-transread' },
      update: {},
      create: {
        title: 'Welcome to TransRead',
        slug: 'welcome-to-transread',
        content: 'This is the first post on TransRead! We\'re excited to have you here. This platform is built with Next.js, Prisma, and PostgreSQL.',
        published: true,
        authorId: users[0].id,
      },
    }),
    prisma.post.upsert({
      where: { slug: 'getting-started-with-nextjs' },
      update: {},
      create: {
        title: 'Getting Started with Next.js',
        slug: 'getting-started-with-nextjs',
        content: 'Next.js is a powerful React framework that makes building web applications a breeze. In this post, we\'ll explore the key features that make Next.js so popular among developers.',
        published: true,
        authorId: users[1].id,
      },
    }),
    prisma.post.upsert({
      where: { slug: 'prisma-database-magic' },
      update: {},
      create: {
        title: 'Prisma: Database Magic',
        slug: 'prisma-database-magic',
        content: 'Prisma is a next-generation ORM that makes database access easy and type-safe. Learn how to use Prisma to build robust applications with confidence.',
        published: false,
        authorId: users[2].id,
      },
    }),
    prisma.post.upsert({
      where: { slug: 'typescript-best-practices' },
      update: {},
      create: {
        title: 'TypeScript Best Practices',
        slug: 'typescript-best-practices',
        content: 'TypeScript brings type safety to JavaScript, making your code more reliable and maintainable. Here are some best practices to follow when using TypeScript.',
        published: true,
        authorId: users[0].id,
      },
    }),
  ])

  console.log('✅ Created posts:', posts.length)

  // Create post-tag relationships
  await Promise.all([
    prisma.postTag.upsert({
      where: { postId_tagId: { postId: posts[0].id, tagId: tags[0].id } },
      update: {},
      create: { postId: posts[0].id, tagId: tags[0].id },
    }),
    prisma.postTag.upsert({
      where: { postId_tagId: { postId: posts[1].id, tagId: tags[0].id } },
      update: {},
      create: { postId: posts[1].id, tagId: tags[0].id },
    }),
    prisma.postTag.upsert({
      where: { postId_tagId: { postId: posts[1].id, tagId: tags[1].id } },
      update: {},
      create: { postId: posts[1].id, tagId: tags[1].id },
    }),
    prisma.postTag.upsert({
      where: { postId_tagId: { postId: posts[2].id, tagId: tags[3].id } },
      update: {},
      create: { postId: posts[2].id, tagId: tags[3].id },
    }),
    prisma.postTag.upsert({
      where: { postId_tagId: { postId: posts[3].id, tagId: tags[2].id } },
      update: {},
      create: { postId: posts[3].id, tagId: tags[2].id },
    }),
  ])

  console.log('✅ Created post-tag relationships')

  // Create some comments
  await Promise.all([
    prisma.comment.create({
      data: {
        content: 'Great introduction! Looking forward to more content.',
        postId: posts[0].id,
        authorId: users[1].id,
      },
    }),
    prisma.comment.create({
      data: {
        content: 'This is exactly what I was looking for. Thanks for sharing!',
        postId: posts[1].id,
        authorId: users[2].id,
      },
    }),
    prisma.comment.create({
      data: {
        content: 'TypeScript has really improved my development experience.',
        postId: posts[3].id,
        authorId: users[1].id,
      },
    }),
  ])

  console.log('✅ Created comments')
  console.log('🎉 Database seeded successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
