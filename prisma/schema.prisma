// Prisma schema for TransReed — aligns with plan.md models

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AccessToken {
  id             String   @id @default(cuid()) // used as jti
  type           String   // 'anon_doc' | 'user'
  docId          String?
  userId         String?
  sessionId      String?
  expiresAt      DateTime
  revokedAt      DateTime?
  maxRequests    Int?
  usedRequests   Int      @default(0)
  maxSeconds     Int?
  usedSeconds    Int      @default(0)
  ipHash         String?
  userAgentHash  String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([docId])
  @@index([userId])
  @@index([sessionId])
  @@map("access_tokens")
}

model User {
  id                 String   @id @default(cuid())
  clerkUserId        String   @unique
  email              String?
  name               String?
  planKey            String   @default("free")
  plan               Plan     @relation(fields: [planKey], references: [key])
  subscriptionStatus String   @default("inactive")
  stripeCustomerId   String?
  paypalPayerId      String?
  creditBalance      Int      @default(0)
  billingPeriodStart DateTime?
  billingPeriodEnd   DateTime?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  documents          Document[]
  usage              Usage[]
  creditLedger       CreditLedger[]
  paymentEvents      PaymentEvent[]

  @@map("users")
}

model Plan {
  key                   String  @id
  name                  String
  priceMonthlyCents     Int
  maxDocumentsPerCycle  Int?
  includedPageCredits   Int?
  includedListeningTime Int?
  description           String
  isActive              Boolean @default(true)
  users                 User[]

  @@map("plans")
}

model Usage {
  id                 String   @id @default(cuid())
  userId             String
  cycleStart         DateTime
  cycleEnd           DateTime
  documentsProcessed Int      @default(0)
  pagesProcessed     Int      @default(0)
  listeningTime      Int      @default(0)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, cycleStart])
  @@map("usage")
}

model CreditLedger {
  id          String   @id @default(cuid())
  userId      String
  change      Int
  reason      String
  description String?
  meta        Json?
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, createdAt])
  @@map("credit_ledger")
}

model PaymentEvent {
  id         String   @id @default(cuid())
  userId     String
  provider   String
  type       String
  amount     Int?
  currency   String?
  status     String
  externalId String
  raw        Json
  createdAt  DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([provider, externalId])
  @@map("payment_events")
}

model Document {
  id                   String    @id @default(cuid())
  userId               String?
  sessionId            String?
  originalFilename     String
  authors              String?
  contentType          String
  sizeBytes            Int
  s3KeyOriginal        String?
  pageCount            Int?
  hasFullAudio         Boolean   @default(false)
  targetLanguage       String?
  voice                String?
  status               String    @default("uploaded")
  suggestedStartPageId String?
  lastListenedPageId   String?
  lastListenedAt       DateTime?
  tocSource            String?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  expiresAt            DateTime?

  user                User?     @relation(fields: [userId], references: [id], onDelete: Cascade)
  suggestedStartPage   Page?     @relation("DocumentSuggestedStartPage", fields: [suggestedStartPageId], references: [id])
  lastListenedPage     Page?     @relation("DocumentLastListenedPage", fields: [lastListenedPageId], references: [id])
  pages                Page[]    @relation("DocumentPages")
  chapters             Chapter[]
  headingDetections    PageHeadingDetection[]

  @@index([userId, createdAt])
  @@index([sessionId])
  @@index([status])
  @@index([expiresAt])
  @@index([suggestedStartPageId])
  @@map("documents")
}

model Page {
  id                         String    @id @default(cuid())
  documentId                 String
  pageNumber                 Int
  s3KeySinglePagePdf         String?
  charCount                  Int?
  charCountTranslated        Int?
  endsWithCompleteSentence   Boolean   @default(true)
  isChapterStart             Boolean   @default(false)
  isSkippableByDefault       Boolean   @default(false)
  textSource                 String?
  estimatedDuration          Int?
  status                     String    @default("pending")
  role                       String    @default("unknown")
  createdAt                  DateTime  @default(now())
  updatedAt                  DateTime  @updatedAt

  document                   Document  @relation("DocumentPages", fields: [documentId], references: [id], onDelete: Cascade)
  pageAudios                 PageAudio[]
  headingDetections          PageHeadingDetection[]
  suggestedStartForDocuments Document[] @relation("DocumentSuggestedStartPage")
  lastListenedForDocuments   Document[] @relation("DocumentLastListenedPage")
  chaptersStartingHere       Chapter[] @relation("ChapterStartPage")
  chaptersEndingHere         Chapter[] @relation("ChapterEndPage")

  @@unique([documentId, pageNumber])
  @@index([documentId, role])
  @@index([status])
  @@map("pages")
}

model Chapter {
  id         String   @id @default(cuid())
  documentId String
  name       String
  startPageId String
  endPageId   String?
  parentId    String?
  order       Int
  level       Int      @default(1)
  source      String
  confidence  Float    @default(1.0)
  createdAt   DateTime @default(now())

  document  Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  parent    Chapter? @relation("ChapterHierarchy", fields: [parentId], references: [id])
  children  Chapter[] @relation("ChapterHierarchy")
  startPage Page     @relation("ChapterStartPage", fields: [startPageId], references: [id])
  endPage   Page?    @relation("ChapterEndPage", fields: [endPageId], references: [id])

  @@index([documentId, order])
  @@index([parentId])
  @@index([startPageId])
  @@index([endPageId])
  @@map("chapters")
}

model PageHeadingDetection {
  id               String   @id @default(cuid())
  documentId       String
  pageId           String
  language         String
  detectedHeading  String?
  headingConfidence Float  @default(0)
  processingMode   String
  modelVersion     String?
  createdAt        DateTime @default(now())

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  page     Page     @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@index([pageId, language])
  @@index([documentId])
  @@map("page_heading_detections")
}

model PageAudio {
  id                  String   @id @default(cuid())
  pageId              String
  language            String
  voice               String
  ttsEngine           String
  ttsParamsHash       String
  translatedTextHash  String?
  modelVersion        String
  s3KeyTranslatedText String?
  s3KeyAudioMp3       String?
  duration            Int?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  page Page @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@unique([pageId, language, voice, ttsEngine, ttsParamsHash, modelVersion])
  @@index([pageId, language, voice])
  @@map("page_audios")
}

model VbeeTtsRequest {
  id        String   @id @default(cuid())
  requestId String   @unique
  chunkId   String
  pageId    String
  status    String   @default("pending")
  audioLink String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([pageId])
  @@index([requestId])
  @@index([status])
  @@map("vbee_tts_requests")
}
