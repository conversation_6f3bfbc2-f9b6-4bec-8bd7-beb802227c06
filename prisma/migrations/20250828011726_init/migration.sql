-- CreateTable
CREATE TABLE "public"."users" (
    "id" TEXT NOT NULL,
    "clerkUserId" TEXT NOT NULL,
    "email" TEXT,
    "name" TEXT,
    "planKey" TEXT NOT NULL DEFAULT 'free',
    "subscriptionStatus" TEXT NOT NULL DEFAULT 'inactive',
    "stripeCustomerId" TEXT,
    "paypalPayerId" TEXT,
    "creditBalance" INTEGER NOT NULL DEFAULT 0,
    "billingPeriodStart" TIMESTAMP(3),
    "billingPeriodEnd" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."plans" (
    "key" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "priceMonthlyCents" INTEGER NOT NULL,
    "maxDocumentsPerCycle" INTEGER,
    "includedPageCredits" INTEGER,
    "includedListeningTime" INTEGER,
    "description" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "plans_pkey" PRIMARY KEY ("key")
);

-- CreateTable
CREATE TABLE "public"."usage" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "cycleStart" TIMESTAMP(3) NOT NULL,
    "cycleEnd" TIMESTAMP(3) NOT NULL,
    "documentsProcessed" INTEGER NOT NULL DEFAULT 0,
    "pagesProcessed" INTEGER NOT NULL DEFAULT 0,
    "listeningTime" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "usage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."credit_ledger" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "change" INTEGER NOT NULL,
    "reason" TEXT NOT NULL,
    "description" TEXT,
    "meta" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "credit_ledger_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."payment_events" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "amount" INTEGER,
    "currency" TEXT,
    "status" TEXT NOT NULL,
    "externalId" TEXT NOT NULL,
    "raw" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "payment_events_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."documents" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "sessionId" TEXT,
    "originalFilename" TEXT NOT NULL,
    "authors" TEXT,
    "contentType" TEXT NOT NULL,
    "sizeBytes" INTEGER NOT NULL,
    "s3KeyOriginal" TEXT,
    "pageCount" INTEGER,
    "hasFullAudio" BOOLEAN NOT NULL DEFAULT false,
    "targetLanguage" TEXT,
    "voice" TEXT,
    "status" TEXT NOT NULL DEFAULT 'uploaded',
    "suggestedStartPageId" TEXT,
    "tocSource" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "expiresAt" TIMESTAMP(3),

    CONSTRAINT "documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pages" (
    "id" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "pageNumber" INTEGER NOT NULL,
    "s3KeySinglePagePdf" TEXT,
    "charCount" INTEGER,
    "charCountTranslated" INTEGER,
    "endsWithCompleteSentence" BOOLEAN NOT NULL DEFAULT true,
    "isChapterStart" BOOLEAN NOT NULL DEFAULT false,
    "isSkippableByDefault" BOOLEAN NOT NULL DEFAULT false,
    "textSource" TEXT,
    "estimatedDuration" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "role" TEXT NOT NULL DEFAULT 'unknown',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."chapters" (
    "id" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "startPageId" TEXT NOT NULL,
    "endPageId" TEXT,
    "parentId" TEXT,
    "order" INTEGER NOT NULL,
    "level" INTEGER NOT NULL DEFAULT 1,
    "source" TEXT NOT NULL,
    "confidence" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "chapters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."page_heading_detections" (
    "id" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "pageId" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "detectedHeading" TEXT,
    "headingConfidence" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "processingMode" TEXT NOT NULL,
    "modelVersion" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "page_heading_detections_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."page_audios" (
    "id" TEXT NOT NULL,
    "pageId" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "voice" TEXT NOT NULL,
    "ttsEngine" TEXT NOT NULL,
    "ttsParamsHash" TEXT NOT NULL,
    "translatedTextHash" TEXT,
    "modelVersion" TEXT NOT NULL,
    "s3KeyTranslatedText" TEXT,
    "s3KeyAudioMp3" TEXT,
    "duration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "page_audios_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_clerkUserId_key" ON "public"."users"("clerkUserId");

-- CreateIndex
CREATE UNIQUE INDEX "usage_userId_cycleStart_key" ON "public"."usage"("userId", "cycleStart");

-- CreateIndex
CREATE INDEX "credit_ledger_userId_createdAt_idx" ON "public"."credit_ledger"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "payment_events_provider_externalId_idx" ON "public"."payment_events"("provider", "externalId");

-- CreateIndex
CREATE INDEX "documents_userId_createdAt_idx" ON "public"."documents"("userId", "createdAt");

-- CreateIndex
CREATE INDEX "documents_sessionId_idx" ON "public"."documents"("sessionId");

-- CreateIndex
CREATE INDEX "documents_status_idx" ON "public"."documents"("status");

-- CreateIndex
CREATE INDEX "documents_expiresAt_idx" ON "public"."documents"("expiresAt");

-- CreateIndex
CREATE INDEX "documents_suggestedStartPageId_idx" ON "public"."documents"("suggestedStartPageId");

-- CreateIndex
CREATE INDEX "pages_documentId_role_idx" ON "public"."pages"("documentId", "role");

-- CreateIndex
CREATE INDEX "pages_status_idx" ON "public"."pages"("status");

-- CreateIndex
CREATE UNIQUE INDEX "pages_documentId_pageNumber_key" ON "public"."pages"("documentId", "pageNumber");

-- CreateIndex
CREATE INDEX "chapters_documentId_order_idx" ON "public"."chapters"("documentId", "order");

-- CreateIndex
CREATE INDEX "chapters_parentId_idx" ON "public"."chapters"("parentId");

-- CreateIndex
CREATE INDEX "chapters_startPageId_idx" ON "public"."chapters"("startPageId");

-- CreateIndex
CREATE INDEX "chapters_endPageId_idx" ON "public"."chapters"("endPageId");

-- CreateIndex
CREATE INDEX "page_heading_detections_pageId_language_idx" ON "public"."page_heading_detections"("pageId", "language");

-- CreateIndex
CREATE INDEX "page_heading_detections_documentId_idx" ON "public"."page_heading_detections"("documentId");

-- CreateIndex
CREATE INDEX "page_audios_pageId_language_voice_idx" ON "public"."page_audios"("pageId", "language", "voice");

-- CreateIndex
CREATE UNIQUE INDEX "page_audios_pageId_language_voice_ttsEngine_ttsParamsHash_m_key" ON "public"."page_audios"("pageId", "language", "voice", "ttsEngine", "ttsParamsHash", "modelVersion");

-- AddForeignKey
ALTER TABLE "public"."users" ADD CONSTRAINT "users_planKey_fkey" FOREIGN KEY ("planKey") REFERENCES "public"."plans"("key") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."usage" ADD CONSTRAINT "usage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."credit_ledger" ADD CONSTRAINT "credit_ledger_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payment_events" ADD CONSTRAINT "payment_events_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."documents" ADD CONSTRAINT "documents_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."documents" ADD CONSTRAINT "documents_suggestedStartPageId_fkey" FOREIGN KEY ("suggestedStartPageId") REFERENCES "public"."pages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."pages" ADD CONSTRAINT "pages_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "public"."documents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."chapters" ADD CONSTRAINT "chapters_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "public"."documents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."chapters" ADD CONSTRAINT "chapters_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "public"."chapters"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."chapters" ADD CONSTRAINT "chapters_startPageId_fkey" FOREIGN KEY ("startPageId") REFERENCES "public"."pages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."chapters" ADD CONSTRAINT "chapters_endPageId_fkey" FOREIGN KEY ("endPageId") REFERENCES "public"."pages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."page_heading_detections" ADD CONSTRAINT "page_heading_detections_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "public"."documents"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."page_heading_detections" ADD CONSTRAINT "page_heading_detections_pageId_fkey" FOREIGN KEY ("pageId") REFERENCES "public"."pages"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."page_audios" ADD CONSTRAINT "page_audios_pageId_fkey" FOREIGN KEY ("pageId") REFERENCES "public"."pages"("id") ON DELETE CASCADE ON UPDATE CASCADE;
