-- CreateTable
CREATE TABLE "public"."access_tokens" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "docId" TEXT,
    "userId" TEXT,
    "sessionId" TEXT,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "revokedAt" TIMESTAMP(3),
    "maxRequests" INTEGER,
    "usedRequests" INTEGER NOT NULL DEFAULT 0,
    "maxSeconds" INTEGER,
    "usedSeconds" INTEGER NOT NULL DEFAULT 0,
    "ipHash" TEXT,
    "userAgentHash" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "access_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."vbee_tts_requests" (
    "id" TEXT NOT NULL,
    "requestId" TEXT NOT NULL,
    "chunkId" TEXT NOT NULL,
    "pageId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "audioLink" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "vbee_tts_requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "access_tokens_docId_idx" ON "public"."access_tokens"("docId");

-- CreateIndex
CREATE INDEX "access_tokens_userId_idx" ON "public"."access_tokens"("userId");

-- CreateIndex
CREATE INDEX "access_tokens_sessionId_idx" ON "public"."access_tokens"("sessionId");

-- CreateIndex
CREATE UNIQUE INDEX "vbee_tts_requests_requestId_key" ON "public"."vbee_tts_requests"("requestId");

-- CreateIndex
CREATE INDEX "vbee_tts_requests_pageId_idx" ON "public"."vbee_tts_requests"("pageId");

-- CreateIndex
CREATE INDEX "vbee_tts_requests_requestId_idx" ON "public"."vbee_tts_requests"("requestId");

-- CreateIndex
CREATE INDEX "vbee_tts_requests_status_idx" ON "public"."vbee_tts_requests"("status");
