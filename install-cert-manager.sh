#!/bin/bash

# Install cert-manager and configure Let's Encrypt on K3s
# Usage: ./install-cert-manager.sh [vps_host] [vps_user] [vps_password] [vps_port] [email]
# Example: ./install-cert-manager.sh ************ root mypassword 22 <EMAIL>

set -e

# Default values
VPS_HOST="${1:-************}"
VPS_USER="${2:-root}"
VPS_PASSWORD="${3}"
VPS_PORT="${4:-22}"
LETSENCRYPT_EMAIL="${5:-<EMAIL>}"

# Cert-manager version
CERT_MANAGER_VERSION="v1.13.3"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

if [ -z "$VPS_PASSWORD" ]; then
    echo -e "${RED}Error: VPS password is required${NC}"
    echo "Usage: $0 <vps_host> <vps_user> <vps_password> [vps_port] [email]"
    exit 1
fi

echo -e "${BLUE}=== Installing cert-manager on K3s ===${NC}"
echo -e "${YELLOW}Host: ${VPS_USER}@${VPS_HOST}:${VPS_PORT}${NC}"
echo -e "${YELLOW}Version: ${CERT_MANAGER_VERSION}${NC}"
echo -e "${YELLOW}Email: ${LETSENCRYPT_EMAIL}${NC}"
echo ""

# Check sshpass
if ! command -v sshpass &> /dev/null; then
    echo -e "${YELLOW}Installing sshpass...${NC}"
    sudo apt-get update && sudo apt-get install -y sshpass
fi

# Check VPS connectivity
echo -e "${BLUE}Checking VPS connectivity...${NC}"
if ! sshpass -p "${VPS_PASSWORD}" ssh -p ${VPS_PORT} -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${VPS_USER}@${VPS_HOST} "echo 'Connected'" > /dev/null 2>&1; then
    echo -e "${RED}Error: Cannot connect to ${VPS_HOST}:${VPS_PORT}${NC}"
    exit 1
fi
echo -e "${GREEN}✓ VPS accessible${NC}"

# Install cert-manager on VPS
echo -e "${BLUE}Installing cert-manager and configuring Let's Encrypt...${NC}"

sshpass -p "${VPS_PASSWORD}" ssh -p ${VPS_PORT} -o StrictHostKeyChecking=no ${VPS_USER}@${VPS_HOST} << EOF
set -e

# Check if K3s is installed
if ! command -v k3s &> /dev/null; then
    echo "Error: K3s is not installed. Please install K3s first."
    exit 1
fi

# Check if cert-manager is already installed
if k3s kubectl get namespace cert-manager &> /dev/null; then
    echo ""
    echo "✓ cert-manager namespace already exists"
    echo ""
    echo "Cert-manager pods:"
    k3s kubectl get pods -n cert-manager
    echo ""
    echo "ClusterIssuers:"
    k3s kubectl get clusterissuer
    echo ""
    exit 0
fi

echo ""
echo "=== Installing cert-manager ${CERT_MANAGER_VERSION} ==="
echo ""

# Install cert-manager
k3s kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/${CERT_MANAGER_VERSION}/cert-manager.yaml

# Wait for cert-manager to be ready
echo "Waiting for cert-manager to be ready..."
sleep 30

# Wait for cert-manager pods to be running
echo "Checking cert-manager pods..."
TIMEOUT=120
ELAPSED=0
while [ \$ELAPSED -lt \$TIMEOUT ]; do
    READY=\$(k3s kubectl get pods -n cert-manager --no-headers 2>/dev/null | grep -c "1/1.*Running" || echo 0)
    TOTAL=\$(k3s kubectl get pods -n cert-manager --no-headers 2>/dev/null | wc -l)

    echo "  - Pods ready: \$READY/\$TOTAL (elapsed: \${ELAPSED}s)"

    if [ "\$READY" = "\$TOTAL" ] && [ "\$TOTAL" -ge 3 ]; then
        echo "✓ All cert-manager pods are ready!"
        break
    fi

    sleep 5
    ELAPSED=\$((ELAPSED + 5))
done

if [ \$ELAPSED -ge \$TIMEOUT ]; then
    echo "⚠ Warning: Timeout waiting for cert-manager pods"
    exit 1
fi

echo ""
echo "Cert-manager pods:"
k3s kubectl get pods -n cert-manager
echo ""

# Create Let's Encrypt ClusterIssuer (Production)
echo "Creating Let's Encrypt ClusterIssuer (Production)..."
cat <<ISSUER | k3s kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: ${LETSENCRYPT_EMAIL}
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: traefik
ISSUER

echo ""

# Create Let's Encrypt ClusterIssuer (Staging - for testing)
echo "Creating Let's Encrypt ClusterIssuer (Staging)..."
cat <<ISSUER | k3s kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
spec:
  acme:
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: ${LETSENCRYPT_EMAIL}
    privateKeySecretRef:
      name: letsencrypt-staging
    solvers:
    - http01:
        ingress:
          class: traefik
ISSUER

echo ""

# Wait for ClusterIssuers to be ready
echo "Waiting for ClusterIssuers to be ready..."
sleep 5

echo ""
echo "✓ cert-manager installed successfully"
echo ""

# Show ClusterIssuers
echo "ClusterIssuers:"
k3s kubectl get clusterissuer
echo ""

# Show cert-manager version
echo "Cert-manager components:"
k3s kubectl get deployment -n cert-manager
echo ""

EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ cert-manager installation completed${NC}"
else
    echo -e "${RED}Error: cert-manager installation failed${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}=== cert-manager Installation Complete! ===${NC}"
echo -e "${YELLOW}Host: ${VPS_USER}@${VPS_HOST}:${VPS_PORT}${NC}"
echo -e "${YELLOW}Version: ${CERT_MANAGER_VERSION}${NC}"
echo -e "${YELLOW}Email: ${LETSENCRYPT_EMAIL}${NC}"
echo ""
echo -e "${BLUE}Available ClusterIssuers:${NC}"
echo -e "${YELLOW}  - letsencrypt-prod: Production Let's Encrypt (use this for production)${NC}"
echo -e "${YELLOW}  - letsencrypt-staging: Staging Let's Encrypt (use for testing)${NC}"
echo ""
echo -e "${BLUE}How to use in Ingress:${NC}"
echo -e "${YELLOW}Add these annotations to your Ingress:${NC}"
cat << 'USAGE'
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  tls:
  - hosts:
    - your-domain.com
    secretName: your-domain-tls
USAGE
echo ""
echo -e "${BLUE}Useful commands:${NC}"
echo ""
echo -e "${YELLOW}# Check cert-manager pods${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl get pods -n cert-manager'"
echo ""
echo -e "${YELLOW}# Check ClusterIssuers${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl get clusterissuer'"
echo ""
echo -e "${YELLOW}# Check certificates${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl get certificate -A'"
echo ""
echo -e "${YELLOW}# Check certificate requests${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl get certificaterequest -A'"
echo ""
echo -e "${YELLOW}# View cert-manager logs${NC}"
echo -e "sshpass -p '${VPS_PASSWORD}' ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} 'k3s kubectl logs -n cert-manager -l app=cert-manager'"
echo ""
