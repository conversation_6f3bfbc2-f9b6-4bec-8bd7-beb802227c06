#!/usr/bin/env node
/**
 * Document Processing Worker
 *
 * This worker consumes jobs from the document-processing queue and processes them.
 * Jobs include: process-document, analyze-document-structure, translate-page, generate-page-audio,
 *               fast-analyze-and-prime, split-all-pages
 *
 * Usage:
 *   npx tsx workers/document-processor.ts
 *   or
 *   node workers/document-processor.js (after build)
 */

import { Worker, Job } from 'bullmq';
import IORedis from 'ioredis';
import { redisConnection, QUEUE_NAMES, JOB_TYPES } from '../lib/queue/config';
import { processDocumentJob } from '../lib/jobs/process-document';
import { analyzeDocumentStructureJob } from '../lib/jobs/analyze-structure';
import { translatePageJob } from '../lib/jobs/translate-page';
import { generatePageAudioJob } from '../lib/jobs/generate-page-audio';
import { processPageJob } from '../lib/jobs/process-page';
import { fastAnalyzeAndPrimeJob } from '../lib/jobs/fast-analyze-and-prime';
import { splitAllPagesJob } from '../lib/jobs/split-all-pages';
import { logger } from '../lib/logger';
import type {
  ProcessDocumentJobData,
  AnalyzeDocumentStructureJobData,
  TranslatePageJobData,
  GeneratePageAudioJobData,
  ProcessPageJobData,
  FastAnalyzeAndPrimeJobData,
  SplitAllPagesJobData,
} from '../lib/queue/jobs';

/**
 * Check Redis health before starting worker
 */
async function checkRedisHealth(): Promise<{
  healthy: boolean;
  error?: string;
}> {
  const testClient = new IORedis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    lazyConnect: true,
    maxRetriesPerRequest: 0,
    retryStrategy: () => null, // No retries
  });

  try {
    await testClient.connect();
    await testClient.ping();
    await testClient.quit();
    return { healthy: true };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Job processor function
 */
async function processJob(job: Job) {
  const { name, data, id } = job;

  logger.log('[worker] Processing job:', name, `(${id})`, {
    docId: data.docId,
  });

  try {
    switch (name) {
      case JOB_TYPES.PROCESS_DOCUMENT:
        return await processDocumentJob(job as Job<ProcessDocumentJobData>);

      case JOB_TYPES.ANALYZE_DOCUMENT_STRUCTURE:
        return await analyzeDocumentStructureJob(
          job as Job<AnalyzeDocumentStructureJobData>,
        );

      case JOB_TYPES.TRANSLATE_PAGE:
        return await translatePageJob(job as Job<TranslatePageJobData>);

      case JOB_TYPES.GENERATE_PAGE_AUDIO:
        return await generatePageAudioJob(
          job as Job<GeneratePageAudioJobData>,
        );

      case JOB_TYPES.PROCESS_PAGE:
        return await processPageJob(job as Job<ProcessPageJobData>);

      case JOB_TYPES.FAST_ANALYZE_AND_PRIME:
        return await fastAnalyzeAndPrimeJob(
          job as Job<FastAnalyzeAndPrimeJobData>,
        );

      case JOB_TYPES.SPLIT_ALL_PAGES:
        return await splitAllPagesJob(job as Job<SplitAllPagesJobData>);

      default:
        logger.warn('[worker] Unknown job type:', name);
        throw new Error(`Unknown job type: ${name}`);
    }
  } catch (error) {
    logger.error('[worker] Job failed:', name, `(${id})`, error);
    throw error;
  }
}

/**
 * Create and start the worker
 */
async function startWorker() {
  console.log('[worker] Starting document processing worker...');
  console.log('[worker] Checking Redis connection...');

  // Check Redis health before creating worker
  const health = await checkRedisHealth();

  if (!health.healthy) {
    console.error('\n╔═══════════════════════════════════════════════════════════╗');
    console.error('║  REDIS CONNECTION FAILED                                  ║');
    console.error('╚═══════════════════════════════════════════════════════════╝');
    console.error('\nError:', health.error);
    console.error('\nNote: Redis should auto-start with npm run dev.');
    console.error('\nIf you see this error, try:');
    console.error('  1. Restart: npm run dev');
    console.error('  2. Manual start: docker start transread-redis');
    console.error('  3. Check Docker: docker ps | grep redis\n');
    process.exit(1);
  }

  console.log('[worker] ✓ Redis connection healthy');

  const worker = new Worker(QUEUE_NAMES.DOCUMENT_PROCESSING, processJob, {
    connection: redisConnection,
    concurrency: 5, // Process up to 5 jobs concurrently
    limiter: {
      max: 10, // Max 10 jobs
      duration: 1000, // per second
    },
  });

  // Event handlers
  worker.on('completed', (job) => {
    logger.log('[worker] Job completed:', job.name, `(${job.id})`, {
      docId: job.data?.docId,
      returnValue: job.returnvalue,
    });
  });

  worker.on('failed', (job, error) => {
    logger.error('[worker] Job failed:', job?.name, `(${job?.id})`, {
      docId: job?.data?.docId,
      error: error.message,
      stack: error.stack,
    });
  });

  worker.on('error', (error) => {
    logger.error('[worker] Worker error:', error);
    // Exit on connection errors to prevent endless retry loops
    if (error.message?.includes('ECONNREFUSED') || error.message?.includes('ECONNRESET')) {
      console.error('\n[worker] Lost connection to Redis. Shutting down...');
      process.exit(1);
    }
  });

  worker.on('ready', () => {
    logger.log('[worker] Worker ready and listening for jobs');
  });

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    logger.log('[worker] SIGTERM received, shutting down gracefully...');
    await worker.close();
    await redisConnection.quit();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('[worker] SIGINT received, shutting down gracefully...');
    await worker.close();
    await redisConnection.quit();
    process.exit(0);
  });

  console.log('[worker] ✓ Worker started successfully');
  logger.log('[worker] Queue:', QUEUE_NAMES.DOCUMENT_PROCESSING);
  logger.log('[worker] Concurrency: 5');
  logger.log('[worker] Supported job types:', Object.values(JOB_TYPES));
  console.log('');

  return worker;
}

// Start the worker if running as main module
if (require.main === module) {
  startWorker().catch((error) => {
    console.error('[worker] Fatal error during startup:', error);
    process.exit(1);
  });
}

export { startWorker };
