import './globals.css';
import type { Metadata } from 'next';
import { ReactNode } from 'react';
import Providers from './providers';
import { appConfig } from 'lib/config';
import CrispChat from './components/CrispChat';

export const metadata: Metadata = {
  title: 'TransReed',
  description: 'PDF-to-Translated-Audio — page-by-page playback',
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/icon.svg', type: 'image/svg+xml' },
    ],
    apple: '/icon.svg',
  },
};

type RootLayoutProps = {
  children: ReactNode;
};

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <body
        className="min-h-screen bg-white antialiased"
        suppressHydrationWarning
      >
        {appConfig.crispWebsiteId && (
          <CrispChat websiteId={appConfig.crispWebsiteId} />
        )}
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
