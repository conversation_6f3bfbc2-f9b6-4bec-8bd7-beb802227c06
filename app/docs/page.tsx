import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import { ensureUserByClerkId } from '@/lib/user';

export const dynamic = 'force-dynamic';

export default async function DocsPage() {
  const { userId: clerkUserId } = await auth();
  if (!clerkUserId) redirect('/');

  const dbUserId = await ensureUserByClerkId(clerkUserId);
  const docs = await prisma.document.findMany({
    where: { userId: dbUserId },
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      originalFilename: true,
      pageCount: true,
      createdAt: true,
      _count: { select: { pages: true } },
    },
  });

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold">My Documents</h2>
        <p className="text-sm text-gray-600">
          All PDFs you’ve uploaded in this account.
        </p>
      </div>

      {docs.length === 0 ? (
        <div className="rounded border bg-white p-6 text-sm text-gray-700">
          <div>No documents yet.</div>
          <a
            href="/app"
            className="mt-3 inline-flex items-center justify-center rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            Upload a PDF
          </a>
        </div>
      ) : (
        <ul className="divide-y rounded border bg-white">
          {docs.map((d) => {
            const pages =
              typeof d.pageCount === 'number' && d.pageCount > 0
                ? d.pageCount
                : d._count.pages;
            return (
              <li
                key={d.id}
                className="flex items-center justify-between p-4 hover:bg-gray-50"
              >
                <a href={`/doc/${d.id}`} className="flex-1">
                  <div className="font-medium text-gray-900">
                    {d.originalFilename}
                  </div>
                  <div className="text-xs text-gray-600">{pages} pages</div>
                </a>
                <a
                  href={`/doc/${d.id}`}
                  className="ml-4 inline-flex items-center justify-center rounded border border-gray-300 px-3 py-1.5 text-sm hover:bg-gray-50"
                >
                  Open
                </a>
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
}
