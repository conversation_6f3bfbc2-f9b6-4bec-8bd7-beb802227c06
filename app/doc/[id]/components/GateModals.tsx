'use client';
import React, { useState } from 'react';
import { SignInButton, SignUpButton } from '@clerk/nextjs';
import { Button } from '@heroui/react';
import { PricingModal } from '@/app/(protected)/components/PricingModal';

export function GateModals({
  showLogin,
  showUpgrade,
  freeTrialSeconds,
  signedFreeSeconds,
  onSaveResume,
  onDismissUpgrade,
}: {
  showLogin: boolean;
  showUpgrade: boolean;
  freeTrialSeconds: number;
  signedFreeSeconds: number;
  onSaveResume: (why: 'gate' | 'signup') => void;
  onDismissUpgrade: () => void;
}) {
  const [showPricingModal, setShowPricingModal] = useState(false);

  return (
    <>
      {showLogin && (
        <div className="fixed inset-0 z-50 !mt-0 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/40" />
          <div className="relative z-10 w-[92%] max-w-md rounded-2xl bg-white p-6 shadow-xl">
            <h4 className="text-lg font-semibold text-gray-900">
              Continue Listening
            </h4>
            <p className="mt-2 text-sm text-gray-700">
              Your free{' '}
              {Math.floor(freeTrialSeconds / 60) >= 1
                ? `${Math.floor(freeTrialSeconds / 60)} minute`
                : `${freeTrialSeconds} second`}{' '}
              preview has ended. Sign up or log in to keep listening and save
              your progress.
            </p>
            <div className="mt-4 flex flex-col gap-3">
              <SignUpButton mode="modal">
                <Button
                  onPress={() => onSaveResume('signup')}
                  color="primary"
                  className="w-full"
                >
                  Sign up
                </Button>
              </SignUpButton>
              <SignInButton mode="modal">
                <Button
                  onPress={() => onSaveResume('signup')}
                  variant="bordered"
                  className="w-full"
                >
                  Log in
                </Button>
              </SignInButton>
            </div>
          </div>
        </div>
      )}

      {showUpgrade && (
        <div className="fixed inset-0 z-50 !mt-0 flex items-center justify-center">
          <div className="absolute inset-0 bg-black/40" />
          <div className="relative z-10 w-[92%] max-w-md rounded-2xl bg-white p-6 shadow-xl">
            <h4 className="text-lg font-semibold text-gray-900">
              Upgrade to Continue
            </h4>
            <p className="mt-2 text-sm text-gray-700">
              You&apos;ve used your free {signedFreeSeconds} seconds. Upgrade
              your plan to keep listening and save full progress.
            </p>
            <div className="mt-4 flex items-center gap-2">
              <Button
                color="primary"
                size="sm"
                onPress={() => setShowPricingModal(true)}
              >
                View plans
              </Button>
              <Button variant="bordered" size="sm" onPress={onDismissUpgrade}>
                Not now
              </Button>
            </div>
          </div>
        </div>
      )}

      <PricingModal
        isOpen={showPricingModal}
        onClose={() => setShowPricingModal(false)}
      />
    </>
  );
}
