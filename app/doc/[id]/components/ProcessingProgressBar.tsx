interface ProcessingProgressBarProps {
  progress: number; // 0-100
  message: string;
}

export function ProcessingProgressBar({
  progress,
  message,
}: ProcessingProgressBarProps) {
  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-4 px-8">
      {/* Status message with spinning icon */}
      <div className="flex items-center justify-center gap-2">
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500" />
        <div className="text-sm font-medium text-gray-700">{message}</div>
      </div>

      {/* Progress bar */}
      <div className="w-full max-w-md">
        <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
          {/* Filled progress */}
          <div
            className="h-full rounded-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </div>
  );
}
