'use client';
import React from 'react';

function formatTime(sec: number) {
  if (!isFinite(sec) || sec < 0) return '0:00';
  const m = Math.floor(sec / 60);
  const s = Math.floor(sec % 60);
  return `${m}:${s.toString().padStart(2, '0')}`;
}

export function Scrubber({
  currentTime,
  duration,
  allowedMax,
  disabled,
  onStart,
  onChange,
  onEnd,
}: {
  currentTime: number;
  duration: number;
  allowedMax: number;
  disabled?: boolean;
  onStart: () => void;
  onChange: (val: number) => void;
  onEnd: (val: number) => void;
}) {
  const value = Math.min(currentTime, allowedMax);
  const max = Math.max(1, duration || 0);
  return (
    <div className="px-1">
      <input
        type="range"
        min={0}
        max={max}
        step={0.1}
        value={value}
        onMouseDown={onStart}
        onTouchStart={onStart}
        onChange={(e) => onChange(Number(e.target.value))}
        onMouseUp={(e) => onEnd(Number((e.target as HTMLInputElement).value))}
        onTouchEnd={() => onEnd(value)}
        className="w-full cursor-pointer accent-blue-600"
        disabled={!!disabled}
      />
      <div className="mt-1.5 flex justify-between text-xs font-medium text-gray-600">
        <span>{formatTime(value)}</span>
        <span>{duration ? formatTime(duration) : '--:--'}</span>
      </div>
    </div>
  );
}
