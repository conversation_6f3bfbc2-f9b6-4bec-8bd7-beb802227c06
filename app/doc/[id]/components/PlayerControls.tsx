'use client';
import React from 'react';
import { Button } from '@heroui/react';
import {
  IconRewindBackward15,
  IconRewindForward15,
  IconPlayerTrackPrev,
  IconPlayerTrackNext,
} from '@tabler/icons-react';

export function PlayerControls({
  isPlaying,
  isAudioReady = true,
  onSeekBack,
  onTogglePlay,
  onSeekForward,
  onPreviousPage,
  onNextPage,
  canGoToPrevious,
  canGoToNext,
  isNavigating = false,
}: {
  isPlaying: boolean;
  isAudioReady?: boolean;
  onSeekBack: () => void;
  onTogglePlay: () => void;
  onSeekForward: () => void;
  onPreviousPage: () => void;
  onNextPage: () => void;
  canGoToPrevious: boolean;
  canGoToNext: boolean;
  isNavigating?: boolean;
}) {
  return (
    <div>
      <div className="flex items-center justify-center gap-4 sm:gap-6">
        <Button
          isIconOnly
          radius="full"
          variant="flat"
          onPress={onPreviousPage}
          isDisabled={!canGoToPrevious || isNavigating}
          title="Previous page"
          aria-label="Previous page"
          className="h-12 w-12 bg-blue-50 text-blue-800 ring-1 ring-inset ring-blue-100 transition-transform hover:bg-blue-100 active:scale-95 disabled:opacity-40 sm:h-11 sm:w-11"
        >
          <IconPlayerTrackPrev className="h-5 w-5" />
        </Button>

        <Button
          isIconOnly
          radius="full"
          variant="flat"
          onPress={onSeekBack}
          isDisabled={isNavigating}
          title="Back 15s"
          aria-label="Back 15 seconds"
          className="h-12 w-12 bg-blue-50 text-blue-800 ring-1 ring-inset ring-blue-100 transition-transform hover:bg-blue-100 active:scale-95 disabled:opacity-40 sm:h-11 sm:w-11"
        >
          <IconRewindBackward15 className="h-6 w-6" stroke={1.8} />
        </Button>

        <Button
          isIconOnly
          radius="full"
          color="primary"
          isDisabled={!isAudioReady || isNavigating}
          onPress={onTogglePlay}
          title={
            !isAudioReady ? 'Loading audio...' : isPlaying ? 'Pause' : 'Play'
          }
          aria-label={
            !isAudioReady ? 'Loading audio...' : isPlaying ? 'Pause' : 'Play'
          }
          className="h-16 w-16 text-white shadow-lg transition-transform hover:opacity-90 active:scale-95 sm:h-14 sm:w-14"
        >
          {!isAudioReady ? (
            <div className="h-7 w-7 animate-spin rounded-full border-2 border-white border-t-transparent sm:h-6 sm:w-6" />
          ) : isPlaying ? (
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="h-8 w-8 sm:h-7 sm:w-7"
            >
              <rect x="6" y="5" width="4" height="14" rx="1" />
              <rect x="14" y="5" width="4" height="14" rx="1" />
            </svg>
          ) : (
            <svg
              viewBox="0 0 24 24"
              fill="currentColor"
              className="h-8 w-8 sm:h-7 sm:w-7"
            >
              <path d="M8 5v14l11-7-11-7z" />
            </svg>
          )}
        </Button>

        <Button
          isIconOnly
          radius="full"
          variant="flat"
          onPress={onSeekForward}
          isDisabled={isNavigating}
          title="Forward 15s"
          aria-label="Forward 15 seconds"
          className="h-12 w-12 bg-blue-50 text-blue-800 ring-1 ring-inset ring-blue-100 transition-transform hover:bg-blue-100 active:scale-95 disabled:opacity-40 sm:h-11 sm:w-11"
        >
          <IconRewindForward15 className="h-6 w-6" stroke={1.8} />
        </Button>

        <Button
          isIconOnly
          radius="full"
          variant="flat"
          onPress={onNextPage}
          isDisabled={!canGoToNext || isNavigating}
          title="Next page"
          aria-label="Next page"
          className="h-12 w-12 bg-blue-50 text-blue-800 ring-1 ring-inset ring-blue-100 transition-transform hover:bg-blue-100 active:scale-95 disabled:opacity-40 sm:h-11 sm:w-11"
        >
          <IconPlayerTrackNext className="h-5 w-5" />
        </Button>
      </div>
    </div>
  );
}
