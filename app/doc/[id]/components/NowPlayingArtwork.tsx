'use client';
import React from 'react';

export function NowPlayingArtwork({
  currentPageNumber,
  nextPageNumber,
  nextReady,
}: {
  currentPageNumber?: number;
  nextPageNumber?: number;
  nextReady?: boolean;
}) {
  return (
    <div className="flex aspect-square w-[min(80vw,60vh,400px)] items-center justify-center rounded-3xl bg-gradient-to-br from-blue-600 via-blue-500 to-green-600 text-white shadow-lg">
      <div className="text-center">
        <div className="text-xs font-medium uppercase tracking-wider opacity-90">
          Document
        </div>
        <div className="mt-1 text-2xl font-bold sm:text-3xl">
          Page {currentPageNumber}
        </div>
        {typeof nextPageNumber === 'number' ? (
          <div className="mt-2 text-xs opacity-90">
            Next: {nextPageNumber}
            {nextReady ? ' • ready' : ''}
          </div>
        ) : null}
      </div>
    </div>
  );
}
