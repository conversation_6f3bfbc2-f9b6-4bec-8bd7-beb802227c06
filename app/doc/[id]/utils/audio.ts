'use client';

import { logger } from '@/lib/logger';

export function getAudioContext(): AudioContext {
  const AC = (window as any).AudioContext || (window as any).webkitAudioContext;
  // Create a singleton on window to avoid multiple contexts
  const key = '__transread_audio_ctx__';
  const w = window as any;
  if (!w[key]) w[key] = new AC();
  return w[key] as AudioContext;
}

// Cache decoded audio buffers to avoid re-fetching the same chunk multiple times
// when progressively combining chunks (chunk1+chunk2, then combined+chunk3, etc.)
const audioBufferCache = new Map<string, Promise<AudioBuffer>>();

export async function fetchAndDecode(url: string): Promise<AudioBuffer> {
  // Check cache first
  const cached = audioBufferCache.get(url);
  if (cached) {
    logger.log('[audio] Using cached buffer for', url);
    return cached;
  }

  // Fetch and decode, storing the promise in cache immediately to prevent duplicate requests
  const promise = (async () => {
    logger.log('[audio] Fetching and decoding', url);
    const res = await fetch(url);
    const arr = await res.arrayBuffer();
    const ctx = getAudioContext();
    return await ctx.decodeAudioData(arr.slice(0));
  })();

  audioBufferCache.set(url, promise);

  try {
    return await promise;
  } catch (error) {
    // Remove failed fetch from cache so it can be retried
    audioBufferCache.delete(url);
    throw error;
  }
}

// Clear cache for a specific page's chunks when page changes
export function clearAudioBufferCache(pageIdOrPattern?: string) {
  if (!pageIdOrPattern) {
    audioBufferCache.clear();
    logger.log('[audio] Cleared entire audio buffer cache');
    return;
  }

  // Clear only URLs matching the pattern (e.g., containing a specific page ID)
  for (const [url] of audioBufferCache) {
    if (url.includes(pageIdOrPattern)) {
      audioBufferCache.delete(url);
    }
  }
  logger.log('[audio] Cleared audio buffer cache for', pageIdOrPattern);
}

export function encodeWav(buffer: AudioBuffer): Blob {
  const numChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const length = buffer.length;
  const bytesPerSample = 2; // 16-bit PCM
  const blockAlign = numChannels * bytesPerSample;
  const dataSize = length * blockAlign;
  const headerSize = 44;
  const totalSize = headerSize + dataSize;
  const out = new DataView(new ArrayBuffer(totalSize));

  writeStr(out, 0, 'RIFF');
  out.setUint32(4, 36 + dataSize, true);
  writeStr(out, 8, 'WAVE');
  writeStr(out, 12, 'fmt ');
  out.setUint32(16, 16, true);
  out.setUint16(20, 1, true);
  out.setUint16(22, numChannels, true);
  out.setUint32(24, sampleRate, true);
  out.setUint32(28, sampleRate * blockAlign, true);
  out.setUint16(32, blockAlign, true);
  out.setUint16(34, bytesPerSample * 8, true);
  writeStr(out, 36, 'data');
  out.setUint32(40, dataSize, true);

  const channels: Float32Array[] = [];
  for (let c = 0; c < numChannels; c++) channels.push(buffer.getChannelData(c));
  let offset = 44;
  for (let i = 0; i < length; i++) {
    for (let c = 0; c < numChannels; c++) {
      let s = channels[c][i];
      s = Math.max(-1, Math.min(1, s));
      out.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
      offset += 2;
    }
  }
  return new Blob([out.buffer], { type: 'audio/wav' });

  function writeStr(view: DataView, offset: number, str: string) {
    for (let i = 0; i < str.length; i++)
      view.setUint8(offset + i, str.charCodeAt(i));
  }
}
