'use client';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import Interactive from './Interactive';
import { useUser } from '@clerk/nextjs';
import { getAnonCandidates } from '@/lib/anon';
import { But<PERSON>, Chip } from '@heroui/react';
import { logger } from '@/lib/logger';

type DocData = {
  document: any;
  pages: any[];
  chapters: any[];
};

export default function DocumentClient({ docId }: { docId: string }) {
  const [data, setData] = useState<DocData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { isSignedIn } = useUser();

  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        // Include anonymous session headers so server can authorize when signed-out
        const cand = await getAnonCandidates().catch(() => null);
        const headers: Record<string, string> = {};
        let sids: string[] = [];
        if (cand) {
          sids = Array.from(
            new Set(
              [cand.stableId, cand.fpjsId, cand.coarseId].filter(
                Boolean,
              ) as string[],
            ),
          );
          if (sids[0]) headers['x-anon-id'] = sids[0];
          if (sids.length > 1)
            headers['x-anon-alt-ids'] = JSON.stringify(sids.slice(1));
        }
        const qs = sids.length
          ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(
              sids.join(','),
            )}`
          : '';
        const res = await fetch(`/api/doc/${docId}${qs}`, {
          cache: 'no-store',
          headers,
        });
        if (!res.ok) throw new Error(`Failed to load document: ${res.status}`);
        const j = (await res.json()) as DocData;
        if (mounted) setData(j);
      } catch (err) {
        if (mounted) setError((err as Error).message || 'Load failed');
      }
    })();
    return () => {
      mounted = false;
    };
  }, [docId]);

  // After sign-in, claim ownership of this document if it was uploaded anonymously
  useEffect(() => {
    if (!isSignedIn) return;
    // Only attempt claim if document exists and has no userId (is anonymous)
    if (!data?.document || data.document.userId) return;
    let cancelled = false;
    (async () => {
      try {
        const cand = await getAnonCandidates();
        if (!cand) return;
        const sids = Array.from(
          new Set(
            [cand.stableId, cand.fpjsId, cand.coarseId].filter(
              Boolean,
            ) as string[],
          ),
        );
        if (sids.length === 0) return;
        const r = await fetch(`/api/doc/${docId}/claim`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sids }),
        });
        if (!cancelled) {
          try {
            const result = await r.json();
            logger.log('[claim] Document claimed successfully', {
              docId,
              claimed: result.claimed,
              alreadyOwned: result.alreadyOwned,
            });
            // Refresh document data after successful claim to trigger resume
            const headers: Record<string, string> = {};
            if (sids[0]) headers['x-anon-id'] = sids[0];
            if (sids.length > 1)
              headers['x-anon-alt-ids'] = JSON.stringify(sids.slice(1));
            const qs = sids.length
              ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(
                  sids.join(','),
                )}`
              : '';
            const res = await fetch(`/api/doc/${docId}${qs}`, {
              cache: 'no-store',
              headers,
            });
            if (res.ok) {
              const refreshed = await res.json();
              setData(refreshed);
              logger.log('[claim] Document data refreshed after claim');
            }
          } catch (e) {
            logger.error('[claim] Failed to process claim result', e);
          }
        }
      } catch (e) {
        logger.error('[claim] Failed to claim document', e);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [isSignedIn, docId, data?.document]);

  if (error) {
    return (
      <div>
        <p className="text-red-600">{error}</p>
        <p className="mt-4">
          <Link href="/">Go back</Link>
        </p>
      </div>
    );
  }
  if (!data) {
    return;
  }

  const { document, pages, chapters, lastListenedPageId } = data;
  // Choose a sensible starting page:
  // 1) lastListenedPageId if present (resume position)
  // 2) document.suggestedStartPageId if present (smart start)
  // 3) first non-skippable ready page
  // 4) first ready page
  // 5) first known non-skippable page
  // 6) page 1

  // Check if lastListenedPage is valid and has audio
  let resumePage = null;
  if (lastListenedPageId) {
    resumePage = pages.find((p: any) => p.id === lastListenedPageId);
    // Only use resume page if it's ready or was previously played (not pending)
    if (resumePage && resumePage.status === 'pending') {
      logger.log('[Client] Resume page is pending, will use fallback logic');
      resumePage = null;
    }
  }

  const firstReadyNonSkippable = pages.find(
    (p: any) => p.status === 'ready' && !p.isSkippableByDefault,
  );
  const firstReady = pages.find((p: any) => p.status === 'ready');
  const firstKnownNonSkippable = pages.find(
    (p: any) => p.status !== 'pending' && !p.isSkippableByDefault,
  );
  const firstId =
    resumePage?.id ||
    document.suggestedStartPageId ||
    firstReadyNonSkippable?.id ||
    firstReady?.id ||
    firstKnownNonSkippable?.id ||
    pages[0]?.id;

  logger.log('[Client] Determined firstPageId', {
    firstId,
    resumePageId: resumePage?.id,
    resumePageNumber: resumePage?.pageNumber,
    suggestedStartPageId: document.suggestedStartPageId,
  });

  return (
    <div className="flex h-full flex-col">
      {/* Responsive container: max-width on desktop, full-width on mobile */}
      <div className="mx-auto flex h-full w-full max-w-2xl flex-col">
        {/* Title - fixed height */}
        <div className="shrink-0 px-4 py-3 sm:px-6">
          <div className="flex items-center justify-between gap-3">
            <div className="min-w-0 flex-1">
              <h2 className="truncate text-sm font-semibold text-gray-900 sm:text-base">
                {document.originalFilename}
              </h2>
              <div className="mt-1 flex flex-wrap items-center gap-1.5">
                {document.targetLanguage ? (
                  <Chip color="primary" variant="flat" size="sm">
                    {document.targetLanguage}
                  </Chip>
                ) : null}
                {document.voice ? (
                  <Chip color="success" variant="flat" size="sm">
                    {document.voice}
                  </Chip>
                ) : null}
              </div>
            </div>
            <Button
              as={Link}
              href="/"
              size="sm"
              color="primary"
              className="shrink-0"
            >
              Upload
            </Button>
          </div>
        </div>

        {/* Player - takes remaining space */}
        <div className="flex min-h-0 flex-1 flex-col px-4 sm:px-6">
          <Interactive
            docId={document.id}
            firstPageId={firstId}
            pageOrder={pages.map((p: any) => ({
              id: p.id,
              number: p.pageNumber,
              status: p.status,
            }))}
            documentUserId={document.userId}
          />
        </div>
      </div>
    </div>
  );
}
