'use client';
import React from 'react';
import { useUser } from '@clerk/nextjs';
import {
  FREE_TRIAL_SECONDS,
  SIGNED_IN_FREE_SECONDS,
  ENABLE_SIGNED_IN_GATE,
} from './utils/constants';
import { useAnonHeaders } from './utils/anonHeaders';
import { useAnonFreeTrialGate } from './core/useAnonFreeTrialGate';
import { useSignedTierGate } from './core/useSignedTierGate';
import { usePrefetchOnPlay } from './core/usePrefetchOnPlay';
import { useTimeUpdatePrefetch } from './core/useTimeUpdatePrefetch';
import { useProactiveNextPageProcessing } from './core/useProactiveNextPageProcessing';
import { useAdvanceAndCombine } from './core/useAdvanceAndCombine';
import { useProgressBar } from './core/useProgressBar';
import { useAudioControls } from './core/useAudioControls';
import { useInitialPageEnsure } from './core/useInitialPageEnsure';
import { useChunkPolling } from './core/useChunkPolling';
import { useResumePosition } from './core/useResumePosition';
import { useCacheBusting } from './core/useCacheBusting';
import { getProcessingMessage } from './utils/processingStage';

type PageRef = { id: string, number: number, status: string };

export function useInteractiveCore({
  docId,
  firstPageId,
  pageOrder,
}: {
  docId: string;
  firstPageId?: string;
  pageOrder: PageRef[];
}) {
  const { isSignedIn } = useUser();
  const [audioUrl, setAudioUrl] = React.useState<string | null>(null);
  const [processing, setProcessing] = React.useState<boolean>(true);
  const [processingStage, setProcessingStage] =
    React.useState<string>('processing_file');
  const [currentIdx, setCurrentIdx] = React.useState<number>(
    Math.max(
      0,
      pageOrder.findIndex((p) => p.id === firstPageId),
    ),
  );
  const [nextUrl, setNextUrl] = React.useState<string | null>(null);
  const [isChunked, setIsChunked] = React.useState<boolean>(false);
  const [chunkTotal, setChunkTotal] = React.useState<number>(0);
  const [chunkCurrent, setChunkCurrent] = React.useState<number>(0);
  const [chunkNextUrl, setChunkNextUrl] = React.useState<string | null>(null);
  const chunkCurrentRef = React.useRef<number>(0);
  const chunkPollCleanupRef = React.useRef<(() => void) | null>(null);
  const knownChunksRef = React.useRef<Map<number, string>>(new Map());
  const requestedChunksRef = React.useRef<Set<number>>(new Set());
  const chunkEndedNeedingNextRef = React.useRef<boolean>(false);
  const audioRef = React.useRef<HTMLAudioElement | null>(null);
  const requestedRef = React.useRef<Set<string>>(new Set());
  const processingTriggeredRef = React.useRef<Set<string>>(new Set());
  const initialEnsuredRef = React.useRef<boolean>(false);
  const pendingAutoplayRef = React.useRef<boolean>(false);
  const nextPreloadAudioRef = React.useRef<HTMLAudioElement | null>(null);

  const [isCombined, setIsCombined] = React.useState<boolean>(false);
  const [segmentBoundarySec, setSegmentBoundarySec] = React.useState<
    number | null
  >(null);
  const combinedBaseIdxRef = React.useRef<number | null>(null);
  const lastObjectUrlRef = React.useRef<string | null>(null);

  const current = pageOrder[currentIdx];
  const next = pageOrder[currentIdx + 1];

  const anonHeaders = useAnonHeaders();

  // Clear known chunks, requested chunks, and audio buffer cache when page changes
  React.useEffect(() => {
    knownChunksRef.current.clear();
    requestedChunksRef.current.clear();
    processingTriggeredRef.current.clear();
    chunkEndedNeedingNextRef.current = false;
    // Clear audio buffer cache for previous page to free memory
    import('../utils/audio').then((m) => m.clearAudioBufferCache(current?.id));
  }, [current?.id]);

  const resumeLocalSecRef = React.useRef<number | null>(null);
  const trialBaseOffsetRef = React.useRef<number>(0);
  const {
    requireLogin,
    trialDisplay,
    enforceGate,
    triggerGate: triggerAnonGate,
  } = useAnonFreeTrialGate(
    audioRef,
    !!isSignedIn,
    trialBaseOffsetRef,
    audioUrl,
  );
  const signedBaseOffsetRef = React.useRef<number>(0);
  const {
    requireUpgrade,
    signedDisplay,
    enforceSignedGate,
    dismissUpgrade,
    resetUpgradeDismissal,
    triggerGate: triggerSignedGate,
  } = useSignedTierGate(audioRef, !!isSignedIn, signedBaseOffsetRef, audioUrl);

  // Progress bar hook
  const processingProgress = useProgressBar(
    processing,
    audioUrl,
    setProcessing,
  );

  // Audio controls hook
  const {
    isPlaying,
    isAudioReady,
    duration,
    currentTime,
    playbackRate,
    isScrubbing,
    audioEnded,
    togglePlay,
    seekBy,
    onScrubStart,
    onScrub,
    onScrubEnd,
    changeSpeed,
  } = useAudioControls(
    audioRef,
    audioUrl,
    !!isSignedIn,
    requireLogin,
    enforceGate,
    enforceSignedGate,
    resetUpgradeDismissal,
    trialBaseOffsetRef,
    signedBaseOffsetRef,
    resumeLocalSecRef,
  );

  // Initial ensure and doc status polling
  useInitialPageEnsure(
    docId,
    firstPageId,
    pageOrder,
    audioUrl,
    anonHeaders,
    requestedRef,
    initialEnsuredRef,
    knownChunksRef,
    nextPreloadAudioRef,
    pendingAutoplayRef,
    isPlaying,
    {
      setProcessing,
      setProcessingStage,
      setAudioUrl,
      setNextUrl,
      setIsChunked,
      setChunkTotal,
      setChunkCurrent,
      setChunkNextUrl,
      setCurrentIdx,
      triggerAnonGate,
      triggerSignedGate,
    },
  );

  // Chunk polling lifecycle
  useChunkPolling(
    isChunked,
    isPlaying,
    chunkTotal,
    chunkCurrent,
    chunkNextUrl,
    current?.id,
    audioEnded,
    anonHeaders,
    chunkCurrentRef,
    chunkPollCleanupRef,
    knownChunksRef,
    requestedChunksRef,
    chunkEndedNeedingNextRef,
    nextPreloadAudioRef,
    setChunkNextUrl,
  );

  // Prefetch hooks
  usePrefetchOnPlay(
    audioRef,
    isChunked,
    next,
    nextUrl,
    requestedRef,
    anonHeaders,
    (u) => setNextUrl(u),
  );
  useTimeUpdatePrefetch(
    audioRef,
    isChunked,
    next,
    nextUrl,
    requestedRef,
    anonHeaders,
    (u) => setNextUrl(u),
  );

  // Proactive next page processing hook
  // Triggers translation + TTS for next page when remaining duration is low
  useProactiveNextPageProcessing(
    audioRef,
    isChunked,
    next,
    anonHeaders,
    processingTriggeredRef,
  );

  // Advance and combine on ended
  useAdvanceAndCombine(
    audioRef,
    audioUrl,
    next,
    nextUrl,
    isChunked,
    chunkNextUrl,
    audioEnded,
    (u) => setAudioUrl(u),
    (u) => setNextUrl(u),
    (fn) => setCurrentIdx(fn),
    (b) => setIsCombined(b),
    (n) => setSegmentBoundarySec(n),
    combinedBaseIdxRef,
    lastObjectUrlRef,
    currentIdx,
    !!isSignedIn,
    enforceGate,
    enforceSignedGate,
    trialBaseOffsetRef,
    signedBaseOffsetRef,
    (fn) => setChunkCurrent(fn),
    (u) => setChunkNextUrl(u),
    pendingAutoplayRef,
  );

  // Combined track monitor across boundary
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    if (!isCombined || segmentBoundarySec == null) return;
    const onTU = () => {
      const base = combinedBaseIdxRef.current;
      if (base == null) return;
      const t = el.currentTime;
      const eps = 0.05;
      if (t < segmentBoundarySec - eps) {
        if (currentIdx !== base) setCurrentIdx(base);
      } else {
        if (currentIdx !== base + 1) setCurrentIdx(base + 1);
      }
    };
    el.addEventListener('timeupdate', onTU);
    return () => {
      el.removeEventListener('timeupdate', onTU);
    };
  }, [isCombined, segmentBoundarySec, currentIdx]);

  // Autoplay newly set source after programmatic advance
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    const maybePlay = () => {
      if (!pendingAutoplayRef.current) return;
      pendingAutoplayRef.current = false;
      try {
        el.play();
      } catch {}
    };
    el.addEventListener('loadedmetadata', maybePlay);
    return () => {
      el.removeEventListener('loadedmetadata', maybePlay);
    };
  }, [audioUrl]);

  // Bust cache after sign-in to avoid anon partial cache
  useCacheBusting(
    !!isSignedIn,
    audioUrl,
    nextUrl,
    audioRef,
    nextPreloadAudioRef,
    resumeLocalSecRef,
    setAudioUrl,
    setNextUrl,
  );

  // Resume position save/restore
  const { saveResumePosition } = useResumePosition(
    docId,
    !!isSignedIn,
    requireLogin,
    currentIdx,
    isCombined,
    segmentBoundarySec,
    pageOrder,
    current,
    anonHeaders,
    isPlaying,
    audioRef,
    combinedBaseIdxRef,
    resumeLocalSecRef,
    pendingAutoplayRef,
    knownChunksRef,
    nextPreloadAudioRef,
    {
      setProcessing,
      setIsChunked,
      setAudioUrl,
      setChunkTotal,
      setChunkCurrent,
      setChunkNextUrl,
      setCurrentIdx,
    },
  );

  return {
    isSignedIn,
    FREE_TRIAL_SECONDS,
    SIGNED_IN_FREE_SECONDS,
    ENABLE_SIGNED_IN_GATE,
    audioRef,
    audioUrl,
    processing,
    processingStage,
    processingProgress,
    processingMessage: getProcessingMessage(processingStage, !!audioUrl),
    current,
    next,
    nextUrl,
    isPlaying,
    isAudioReady,
    currentTime,
    duration,
    playbackRate,
    requireLogin,
    requireUpgrade,
    trialDisplay,
    signedDisplay,
    onScrubStart,
    onScrub,
    onScrubEnd,
    seekBy,
    togglePlay,
    changeSpeed,
    saveResumePosition,
    dismissUpgrade,
    allowedMax: ((): number => {
      const d = duration || Number.MAX_SAFE_INTEGER;
      if (!isSignedIn)
        return Math.max(
          0,
          Math.min(d, FREE_TRIAL_SECONDS - trialBaseOffsetRef.current),
        );
      if (ENABLE_SIGNED_IN_GATE)
        return Math.max(
          0,
          Math.min(d, SIGNED_IN_FREE_SECONDS - signedBaseOffsetRef.current),
        );
      return d;
    })(),
  } as const;
}
