import { logger } from '@/lib/logger';

export interface AudioResponseHandlers {
  setIsChunked: (val: boolean) => void;
  setAudioUrl: (url: string) => void;
  setNextUrl?: (url: string) => void;
  setChunkTotal: (total: number) => void;
  setChunkCurrent: (chunk: number) => void;
  setChunkNextUrl?: (url: string | null) => void;
  knownChunksRef: React.MutableRefObject<Map<number, string>>;
  nextPreloadAudioRef: React.MutableRefObject<HTMLAudioElement | null>;
  pendingAutoplayRef?: React.MutableRefObject<boolean>;
  isPlaying?: boolean;
}

export interface AudioResponse {
  ok?: boolean;
  streaming?: boolean;
  streamUrl?: string;
  url?: string;
  urls?: string[];
  chunked?: boolean;
  available?: number[];
  total?: number;
}

/**
 * Handle audio API response and update state accordingly.
 * Supports streaming, single URL, and chunked audio responses.
 *
 * @param response - Audio API response
 * @param setAsCurrent - Whether to set as current audio (true) or next audio (false)
 * @param handlers - State setters and refs for managing audio
 */
export function handleAudioResponse(
  response: AudioResponse,
  setAsCurrent: boolean,
  handlers: AudioResponseHandlers,
): void {
  const {
    setIsChunked,
    setAudioUrl,
    setNextUrl,
    setChunkTotal,
    setChunkCurrent,
    setChunkNextUrl,
    knownChunksRef,
    nextPreloadAudioRef,
    pendingAutoplayRef,
    isPlaying,
  } = handlers;

  // Handle streaming mode (returns complete MP3, just like response.url)
  if (response.streaming && response.streamUrl) {
    logger.log('[interactive] Using streaming mode', response.streamUrl);
    setIsChunked(false);

    if (setAsCurrent) {
      setAudioUrl(response.streamUrl);
      if (isPlaying && pendingAutoplayRef) {
        pendingAutoplayRef.current = true;
      }
    } else if (setNextUrl) {
      setNextUrl(response.streamUrl);
      try {
        if (!nextPreloadAudioRef.current) {
          nextPreloadAudioRef.current = new Audio();
        }
        nextPreloadAudioRef.current.src = response.streamUrl;
        nextPreloadAudioRef.current.preload = 'auto';
        nextPreloadAudioRef.current.load();
      } catch {}
    }
  } else if (response.url) {
    // Handle single URL response
    setIsChunked(false);
    if (setAsCurrent) {
      setAudioUrl(response.url);
      if (isPlaying && pendingAutoplayRef) {
        pendingAutoplayRef.current = true;
      }
    } else if (setNextUrl) {
      setNextUrl(response.url);
      try {
        if (!nextPreloadAudioRef.current) {
          nextPreloadAudioRef.current = new Audio();
        }
        nextPreloadAudioRef.current.src = response.url;
        nextPreloadAudioRef.current.preload = 'auto';
        nextPreloadAudioRef.current.load();
      } catch {}
    }
  } else if (
    (response.urls && Array.isArray(response.urls)) ||
    response.chunked
  ) {
    // Handle chunked audio response
    setIsChunked(true);
    const urls: string[] = Array.isArray(response.urls) ? response.urls : [];
    const available: number[] = Array.isArray(response.available)
      ? response.available
      : [];
    const total = Number(response.total || 0) || 0;
    setChunkTotal(total);

    // Store all known chunks in the map
    if (available.length > 0 && urls.length > 0) {
      available.forEach((idx, i) => {
        if (urls[i]) {
          knownChunksRef.current.set(idx, urls[i]);
        }
      });
    }

    if (setAsCurrent && urls.length > 0) {
      setAudioUrl(urls[0]);
      setChunkCurrent(available[0] || 1);
      if (isPlaying && pendingAutoplayRef) {
        pendingAutoplayRef.current = true;
      }

      // If we have more chunks ready, prepare the next one
      if (urls.length > 1 && available.length > 1 && setChunkNextUrl) {
        setChunkNextUrl(urls[1]);
        try {
          if (!nextPreloadAudioRef.current) {
            nextPreloadAudioRef.current = new Audio();
          }
          nextPreloadAudioRef.current.src = urls[1];
          nextPreloadAudioRef.current.preload = 'auto';
          nextPreloadAudioRef.current.load();
        } catch {}
      }
    }
  }
}
