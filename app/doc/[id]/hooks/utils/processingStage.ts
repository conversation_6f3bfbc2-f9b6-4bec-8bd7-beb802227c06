/**
 * Processing stage messages for user feedback
 */

export type ProcessingStage =
  | 'loading'
  | 'processing_file'
  | 'translating'
  | 'generating_audio'
  | 'loading_audio'
  | 'complete';

export function getProcessingMessage(
  stage?: string | null,
  hasAudio?: boolean,
): string {
  if (hasAudio) {
    return 'Loading audio...';
  }

  switch (stage) {
    case 'complete':
      return 'Ready';
    case 'generating_audio':
      return 'Synthesizing audio...';
    case 'loading_audio':
      return 'Loading audio...';
    case 'translating':
      return 'Translating document...';
    case 'processing_file':
      return 'Processing file...';
    default:
      return 'Loading...';
  }
}

export function determineProcessingStage(
  documentStatus?: string,
  pageStatus?: string,
  hasTranslation?: boolean,
  hasAudio?: boolean,
): ProcessingStage {
  if (hasAudio) {
    return 'complete';
  }

  if (hasTranslation) {
    return 'generating_audio';
  }

  if (pageStatus === 'processing' || documentStatus === 'processing') {
    return 'translating';
  }

  if (documentStatus === 'uploaded') {
    return 'processing_file';
  }

  return 'loading';
}
