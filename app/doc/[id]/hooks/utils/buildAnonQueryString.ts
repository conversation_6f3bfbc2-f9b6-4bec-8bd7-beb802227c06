import { getAuthHeaders } from '@/lib/access/client';
import { getAnonCandidates } from '@/lib/anon';

/**
 * Build anonymous session query string for API requests.
 * Handles extracting session IDs from headers and fallback to anon candidates.
 *
 * @param anonHeaders - Anonymous headers object
 * @returns Query string in format `?sid=xxx&sids=xxx,yyy,zzz` or empty string
 */
export async function buildAnonQueryString(
  anonHeaders: Record<string, string>,
): Promise<string> {
  const headers = getAuthHeaders({ ...anonHeaders }) as Record<string, string>;
  const sid = headers['x-anon-id'];
  let sids: string[] = [];

  try {
    const raw = headers['x-anon-alt-ids'];
    if (raw) {
      if (raw.trim().startsWith('[')) {
        sids = JSON.parse(raw);
      } else {
        sids = raw.split(',').map((x) => x.trim());
      }
    }
  } catch {}

  if (sid) sids = [sid, ...sids];

  if (sids.length === 0) {
    try {
      const cand2 = await getAnonCandidates();
      if (cand2) {
        sids = Array.from(
          new Set(
            [cand2.stableId, cand2.fpjsId, cand2.coarseId].filter(
              Boolean,
            ) as string[],
          ),
        );
      }
    } catch {}
  }

  return sids.length
    ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
    : '';
}
