import { getAuthHeaders } from '@/lib/access/client';
import { getAnonCandidates } from '@/lib/anon';

export function startChunkPolling(
  pageId: string,
  headers: Record<string, string>,
  getNextIndex: () => number,
  onNextUrl: (url: string, idx: number) => void,
  onCleanup?: () => void,
) {
  let stopped = false;
  const timer = window.setInterval(async () => {
    if (stopped) return;
    try {
      const nextIdx = getNextIndex();
      const h = getAuthHeaders({ ...headers }) as Record<string, string>;
      const sid = h['x-anon-id'];
      let sids: string[] = [];
      try {
        const raw = h['x-anon-alt-ids'];
        if (raw) {
          if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
          else sids = raw.split(',').map((x) => x.trim());
        }
      } catch {}
      if (sid) sids = [sid, ...sids];
      if (sids.length === 0) {
        try {
          const cand2 = await getAnonCandidates();
          if (cand2) {
            sids = Array.from(
              new Set(
                [cand2.stableId, cand2.fpjsId, cand2.coarseId].filter(
                  Boolean,
                ) as string[],
              ),
            );
          }
        } catch {}
      }
      const qs = sids.length
        ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
        : '';

      // Check if the specific chunk exists directly instead of listing all
      const chunkUrl = `/api/page/${pageId}/chunk/${nextIdx}${qs}`;
      const resp = await fetch(chunkUrl, {
        method: 'HEAD',
        cache: 'no-store',
        headers: h,
      });

      if (resp.ok) {
        // Chunk exists, notify and stop polling
        stopped = true;
        onNextUrl(chunkUrl, nextIdx);
      }
    } catch {}
  }, 1500);
  return () => {
    stopped = true;
    try {
      clearInterval(timer);
    } catch {}
    // Call cleanup callback when polling is stopped
    if (onCleanup) {
      try {
        onCleanup();
      } catch {}
    }
  };
}
