/**
 * Streaming Audio Utility for Gemini Live TTS
 *
 * This utility handles real-time audio streaming from the server,
 * converting PCM chunks to a playable audio format using Web Audio API.
 */

import { logger } from '@/lib/logger';

export interface StreamingAudioOptions {
  pageId: string;
  onProgress?: (bytesReceived: number) => void;
  onComplete?: (audioUrl: string) => void;
  onError?: (error: Error) => void;
  anonSid?: string | null;
}

export class StreamingAudioPlayer {
  private audioContext: AudioContext | null = null;
  private sourceNode: AudioBufferSourceNode | null = null;
  private pcmChunks: ArrayBuffer[] = [];
  private isPlaying = false;
  private sampleRate = 24000; // Gemini uses 24kHz
  private channels = 1; // Mono
  private startTime = 0;
  private pauseTime = 0;

  constructor(private options: StreamingAudioOptions) {}

  async start(): Promise<string> {
    try {
      // Build URL with anonymous session if needed
      let url = `/api/page/${this.options.pageId}/stream-tts`;
      if (this.options.anonSid) {
        url += `?sid=${encodeURIComponent(this.options.anonSid)}`;
      }

      logger.log('[streaming-audio] Starting stream from', url);

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(
          `Stream failed: ${response.status} ${response.statusText}`,
        );
      }

      // Check content type to determine response format
      const contentType = response.headers.get('content-type') || '';

      // Handle JSON responses (cached audio)
      if (contentType.includes('application/json')) {
        const data = await response.json();
        if (data.cached && data.url) {
          logger.log('[streaming-audio] Using cached audio', data.url);
          if (this.options.onComplete) {
            this.options.onComplete(data.url);
          }
          return data.url;
        }
        throw new Error('Unexpected JSON response from stream endpoint');
      }

      // Handle MP3 responses (complete audio)
      if (
        contentType.includes('audio/mpeg') ||
        contentType.includes('audio/mp3')
      ) {
        logger.log('[streaming-audio] Received complete MP3 audio');
        const arrayBuffer = await response.arrayBuffer();
        const blob = new Blob([arrayBuffer], { type: 'audio/mpeg' });
        const audioUrl = URL.createObjectURL(blob);

        logger.log('[streaming-audio] Created MP3 blob URL', {
          size: arrayBuffer.byteLength,
          url: audioUrl,
        });

        if (this.options.onProgress) {
          this.options.onProgress(arrayBuffer.byteLength);
        }

        if (this.options.onComplete) {
          this.options.onComplete(audioUrl);
        }

        return audioUrl;
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      const reader = response.body.getReader();
      let bytesReceived = 0;

      // Initialize Audio Context on first chunk
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext ||
          (window as any).webkitAudioContext)();
      }

      // Read and accumulate chunks
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          logger.log('[streaming-audio] Stream complete', {
            totalBytes: bytesReceived,
          });
          break;
        }

        if (value) {
          bytesReceived += value.length;
          this.pcmChunks.push(
            value.buffer.slice(
              value.byteOffset,
              value.byteOffset + value.byteLength,
            ),
          );

          if (this.options.onProgress) {
            this.options.onProgress(bytesReceived);
          }

          // Start playing after receiving first chunk
          if (this.pcmChunks.length === 1) {
            logger.log(
              '[streaming-audio] First chunk received, converting to audio...',
            );
            // Give a small buffer before starting playback
            setTimeout(() => {
              this.convertAndPlay().catch(logger.error);
            }, 100);
          }
        }
      }

      // Convert all PCM chunks to a single audio buffer
      const audioUrl = await this.createAudioUrl();

      if (this.options.onComplete) {
        this.options.onComplete(audioUrl);
      }

      return audioUrl;
    } catch (error) {
      logger.error('[streaming-audio] Error:', error);
      if (this.options.onError) {
        this.options.onError(error as Error);
      }
      throw error;
    }
  }

  private async convertAndPlay(): Promise<void> {
    if (!this.audioContext || this.pcmChunks.length === 0) return;

    try {
      // Concatenate all PCM chunks received so far
      const totalLength = this.pcmChunks.reduce(
        (sum, chunk) => sum + chunk.byteLength,
        0,
      );
      const combinedPcm = new ArrayBuffer(totalLength);
      const combinedView = new Uint8Array(combinedPcm);

      let offset = 0;
      for (const chunk of this.pcmChunks) {
        combinedView.set(new Uint8Array(chunk), offset);
        offset += chunk.byteLength;
      }

      // Convert PCM to AudioBuffer
      const audioBuffer = this.pcmToAudioBuffer(combinedPcm);

      // Create source and play
      if (this.sourceNode) {
        this.sourceNode.stop();
      }

      this.sourceNode = this.audioContext.createBufferSource();
      this.sourceNode.buffer = audioBuffer;
      this.sourceNode.connect(this.audioContext.destination);

      this.startTime = this.audioContext.currentTime;
      this.sourceNode.start(0);
      this.isPlaying = true;

      logger.log('[streaming-audio] Playing audio buffer', {
        duration: audioBuffer.duration,
        chunks: this.pcmChunks.length,
      });
    } catch (error) {
      logger.error('[streaming-audio] Playback error:', error);
    }
  }

  private pcmToAudioBuffer(pcmData: ArrayBuffer): AudioBuffer {
    if (!this.audioContext) {
      throw new Error('AudioContext not initialized');
    }

    // PCM is 16-bit signed integers
    const pcmView = new Int16Array(pcmData);
    const numSamples = pcmView.length;

    // Create AudioBuffer
    const audioBuffer = this.audioContext.createBuffer(
      this.channels,
      numSamples,
      this.sampleRate,
    );

    // Convert 16-bit PCM to float32 samples
    const channelData = audioBuffer.getChannelData(0);
    for (let i = 0; i < numSamples; i++) {
      channelData[i] = pcmView[i] / 32768; // Convert to -1.0 to 1.0 range
    }

    return audioBuffer;
  }

  private async createAudioUrl(): Promise<string> {
    // Combine all PCM chunks into a WAV file
    const totalLength = this.pcmChunks.reduce(
      (sum, chunk) => sum + chunk.byteLength,
      0,
    );
    const combinedPcm = new ArrayBuffer(totalLength);
    const combinedView = new Uint8Array(combinedPcm);

    let offset = 0;
    for (const chunk of this.pcmChunks) {
      combinedView.set(new Uint8Array(chunk), offset);
      offset += chunk.byteLength;
    }

    // Create WAV file
    const wavBuffer = this.createWavFile(combinedPcm);
    const blob = new Blob([wavBuffer], { type: 'audio/wav' });
    const url = URL.createObjectURL(blob);

    logger.log('[streaming-audio] Created audio URL', {
      totalBytes: totalLength,
      wavSize: wavBuffer.byteLength,
    });

    return url;
  }

  private createWavFile(pcmData: ArrayBuffer): ArrayBuffer {
    const numSamples = pcmData.byteLength / 2; // 16-bit samples
    const bitsPerSample = 16;
    const bytesPerSample = bitsPerSample / 8;
    const blockAlign = this.channels * bytesPerSample;
    const byteRate = this.sampleRate * blockAlign;
    const dataSize = numSamples * bytesPerSample * this.channels;
    const wavSize = 44 + dataSize;

    const buffer = new ArrayBuffer(wavSize);
    const view = new DataView(buffer);
    const pcmView = new Uint8Array(pcmData);

    // Write WAV header
    let offset = 0;

    // "RIFF" chunk descriptor
    this.writeString(view, offset, 'RIFF');
    offset += 4;
    view.setUint32(offset, wavSize - 8, true);
    offset += 4;
    this.writeString(view, offset, 'WAVE');
    offset += 4;

    // "fmt " sub-chunk
    this.writeString(view, offset, 'fmt ');
    offset += 4;
    view.setUint32(offset, 16, true);
    offset += 4; // Subchunk size
    view.setUint16(offset, 1, true);
    offset += 2; // Audio format (1 = PCM)
    view.setUint16(offset, this.channels, true);
    offset += 2;
    view.setUint32(offset, this.sampleRate, true);
    offset += 4;
    view.setUint32(offset, byteRate, true);
    offset += 4;
    view.setUint16(offset, blockAlign, true);
    offset += 2;
    view.setUint16(offset, bitsPerSample, true);
    offset += 2;

    // "data" sub-chunk
    this.writeString(view, offset, 'data');
    offset += 4;
    view.setUint32(offset, dataSize, true);
    offset += 4;

    // Copy PCM data
    new Uint8Array(buffer, offset).set(pcmView);

    return buffer;
  }

  private writeString(view: DataView, offset: number, str: string): void {
    for (let i = 0; i < str.length; i++) {
      view.setUint8(offset + i, str.charCodeAt(i));
    }
  }

  stop(): void {
    if (this.sourceNode) {
      this.sourceNode.stop();
      this.sourceNode = null;
    }
    this.isPlaying = false;
  }

  cleanup(): void {
    this.stop();
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.pcmChunks = [];
  }
}

/**
 * Simple hook to use streaming audio in React components
 */
export function useStreamingAudio(pageId: string, anonSid?: string | null) {
  const [isStreaming, setIsStreaming] = React.useState(false);
  const [audioUrl, setAudioUrl] = React.useState<string | null>(null);
  const [error, setError] = React.useState<Error | null>(null);
  const [progress, setProgress] = React.useState(0);
  const playerRef = React.useRef<StreamingAudioPlayer | null>(null);

  const startStreaming = React.useCallback(async () => {
    if (isStreaming) return;

    setIsStreaming(true);
    setError(null);
    setProgress(0);

    const player = new StreamingAudioPlayer({
      pageId,
      anonSid,
      onProgress: (bytes) => {
        setProgress(bytes);
      },
      onComplete: (url) => {
        setAudioUrl(url);
        setIsStreaming(false);
      },
      onError: (err) => {
        setError(err);
        setIsStreaming(false);
      },
    });

    playerRef.current = player;

    try {
      await player.start();
    } catch (err) {
      setError(err as Error);
      setIsStreaming(false);
    }
  }, [pageId, anonSid, isStreaming]);

  const stopStreaming = React.useCallback(() => {
    if (playerRef.current) {
      playerRef.current.cleanup();
      playerRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  React.useEffect(() => {
    return () => {
      if (playerRef.current) {
        playerRef.current.cleanup();
      }
    };
  }, []);

  return {
    isStreaming,
    audioUrl,
    error,
    progress,
    startStreaming,
    stopStreaming,
  };
}

// Add React import
import React from 'react';
