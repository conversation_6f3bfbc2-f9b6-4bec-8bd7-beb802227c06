import { getAudioContext, fetchAndDecode, encodeWav } from '../../utils/audio';

export async function combineTwoUrlsToWav(
  currentUrl: string,
  nextUrl: string,
): Promise<{
  url: string;
  boundarySec: number;
} | null> {
  try {
    const [a1, a2] = await Promise.all([
      fetchAndDecode(currentUrl),
      fetchAndDecode(nextUrl),
    ]);
    const ctx = getAudioContext();
    const numChannels = Math.max(a1.numberOfChannels, a2.numberOfChannels);
    const sampleRate = ctx.sampleRate;
    const totalLength = a1.length + a2.length;
    const out = ctx.createBuffer(numChannels, totalLength, sampleRate);
    for (let ch = 0; ch < numChannels; ch++) {
      const ch1 = a1.getChannelData(Math.min(ch, a1.numberOfChannels - 1));
      const ch2 = a2.getChannelData(Math.min(ch, a2.numberOfChannels - 1));
      const dest = out.getChannelData(ch);
      dest.set(ch1, 0);
      dest.set(ch2, a1.length);
    }
    const boundarySec = a1.duration;
    const blob = encodeWav(out);
    const url = URL.createObjectURL(blob);
    return { url, boundarySec };
  } catch {
    return null;
  }
}
