/**
 * Universal Audio Fetcher
 *
 * Automatically handles both streaming and chunked TTS modes.
 * Detects which mode the server is using and adapts accordingly.
 */

import { StreamingAudioPlayer } from './streamingAudio';
import { logger } from '@/lib/logger';

export interface EnsureAudioResult {
  // For chunked mode
  ok: boolean;
  chunked?: boolean;
  total?: number;
  available?: number[];
  urls?: string[];
  url?: string; // Single complete audio
  status?: string;
  processingStage?: string;

  // For streaming mode
  streaming?: boolean;
  streamUrl?: string;

  // Common
  skip?: boolean;
  requireLogin?: boolean;
  requireUpgrade?: boolean;
  reason?: string;
}

export interface AudioPlayerResult {
  url: string;
  isStreaming: boolean;
  cleanup?: () => void;
}

/**
 * Ensures audio is ready for a page, handling both streaming and chunked modes
 */
export async function ensurePageAudio(
  pageId: string,
  anonSid?: string | null,
): Promise<AudioPlayerResult> {
  // Call new audio retrieval endpoint (GET, read-only)
  let url = `/api/page/${pageId}/audio`;
  if (anonSid) {
    url += `?sid=${encodeURIComponent(anonSid)}`;
  }

  logger.log('[ensure-audio] Requesting audio for page', pageId);

  const response = await fetch(url, { method: 'GET', cache: 'no-store' });
  if (!response.ok) {
    throw new Error(`Failed to get audio: ${response.status}`);
  }

  const data: EnsureAudioResult = await response.json();

  // Handle streaming mode
  if (data.streaming && data.streamUrl) {
    logger.log('[ensure-audio] Using streaming mode', data.streamUrl);

    // Use the streaming player
    const player = new StreamingAudioPlayer({
      pageId,
      anonSid,
      onProgress: (bytes) => {
        logger.log('[ensure-audio] Stream progress:', bytes, 'bytes');
      },
      onComplete: (audioUrl) => {
        logger.log('[ensure-audio] Stream complete, audio ready:', audioUrl);
      },
      onError: (error) => {
        logger.error('[ensure-audio] Stream error:', error);
      },
    });

    const audioUrl = await player.start();

    return {
      url: audioUrl,
      isStreaming: true,
      cleanup: () => player.cleanup(),
    };
  }

  // Handle chunked mode
  if (data.chunked && data.urls && data.urls.length > 0) {
    logger.log('[ensure-audio] Using chunked mode, first chunk:', data.urls[0]);
    return {
      url: data.urls[0],
      isStreaming: false,
    };
  }

  // Handle single complete audio
  if (data.url) {
    logger.log('[ensure-audio] Audio already complete:', data.url);
    return {
      url: data.url,
      isStreaming: false,
    };
  }

  // Handle skippable page
  if (data.skip) {
    throw new Error('Page is skippable');
  }

  // Handle auth/upgrade required
  if (data.requireLogin) {
    throw new Error('Login required');
  }
  if (data.requireUpgrade) {
    throw new Error('Upgrade required');
  }

  throw new Error('Unexpected response from ensure-tts');
}

/**
 * React hook for ensuring audio with automatic mode detection
 */
export function useEnsureAudio(pageId: string, anonSid?: string | null) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [audioUrl, setAudioUrl] = React.useState<string | null>(null);
  const [error, setError] = React.useState<Error | null>(null);
  const [isStreaming, setIsStreaming] = React.useState(false);
  const cleanupRef = React.useRef<(() => void) | null>(null);

  const loadAudio = React.useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await ensurePageAudio(pageId, anonSid);
      setAudioUrl(result.url);
      setIsStreaming(result.isStreaming);
      cleanupRef.current = result.cleanup || null;
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [pageId, anonSid, isLoading]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
      }
    };
  }, []);

  return {
    isLoading,
    audioUrl,
    error,
    isStreaming,
    loadAudio,
  };
}

// Add React import
import React from 'react';
