import React from 'react';
import { getAnonCandidates } from '@/lib/anon';

function headersFromCookies(): Record<string, string> {
  if (typeof document === 'undefined') return {};
  try {
    const map = Object.fromEntries(
      document.cookie
        .split(';')
        .map((p) => p.trim())
        .filter(Boolean)
        .map((kv) => {
          const i = kv.indexOf('=');
          if (i === -1) return [kv, ''];
          const k = decodeURIComponent(kv.slice(0, i).trim());
          const v = decodeURIComponent(kv.slice(i + 1).trim());
          return [k, v];
        }),
    ) as Record<string, string>;
    const sid = map['tr_anon_sid'];
    const sidsCsv = map['tr_anon_sids'];
    const h: Record<string, string> = {};
    if (sid) h['x-anon-id'] = sid;
    if (sidsCsv) {
      const arr = sidsCsv
        .split(',')
        .map((x) => x.trim())
        .filter(Boolean);
      if (arr.length > 1) h['x-anon-alt-ids'] = JSON.stringify(arr.slice(1));
    }
    return h;
  } catch {
    return {};
  }
}

export function useAnonHeaders(): Record<string, string> {
  const [headers, setHeaders] =
    React.useState<Record<string, string>>(headersFromCookies());
  React.useEffect(() => {
    let cancelled = false;
    (async () => {
      try {
        const cand = await getAnonCandidates();
        if (cancelled) return;
        if (!cand) return;
        const sids = Array.from(
          new Set(
            [cand.stableId, cand.fpjsId, cand.coarseId].filter(
              Boolean,
            ) as string[],
          ),
        );
        const h: Record<string, string> = {};
        if (sids[0]) h['x-anon-id'] = sids[0];
        if (sids.length > 1)
          h['x-anon-alt-ids'] = JSON.stringify(sids.slice(1));
        setHeaders(h);
      } catch {}
    })();
    return () => {
      cancelled = true;
    };
  }, []);
  return headers;
}
