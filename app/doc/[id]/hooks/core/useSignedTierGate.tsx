import React from 'react';
import {
  ENABLE_SIGNED_IN_GATE,
  SIGNED_IN_FREE_SECONDS,
} from '../utils/constants';

export function useSignedTierGate(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  isSignedIn: boolean,
  signedBaseOffsetRef: React.MutableRefObject<number>,
  audioUrl?: string | null,
) {
  const [requireUpgrade, setRequireUpgrade] = React.useState(false);
  const [signedDisplay, setSignedDisplay] = React.useState(0);
  const upgradeDismissedRef = React.useRef(false);
  // Gate by absolute content position for signed-in users as well
  const signedRafRef = React.useRef<number | null>(null);
  const signedTimeoutRef = React.useRef<number | null>(null);

  const updateSignedDisplayAndGate = React.useCallback(
    (el: HTMLAudioElement | null) => {
      if (!el) return false;
      if (!isSignedIn || !ENABLE_SIGNED_IN_GATE) return false;
      const used = Math.max(
        0,
        signedBaseOffsetRef.current + (el.currentTime || 0),
      );
      const clamped = Math.min(used, SIGNED_IN_FREE_SECONDS);
      setSignedDisplay(clamped);
      try {
        sessionStorage.setItem(
          'transread_signed_free_elapsed_sec',
          String(Math.floor(clamped)),
        );
      } catch {}
      if (used >= SIGNED_IN_FREE_SECONDS) {
        if (!upgradeDismissedRef.current) setRequireUpgrade(true);
        try {
          el.pause();
        } catch {}
        return true;
      }
      return false;
    },
    [isSignedIn, signedBaseOffsetRef],
  );

  const dismissUpgrade = React.useCallback(() => {
    upgradeDismissedRef.current = true;
    setRequireUpgrade(false);
  }, []);

  // Expose a way to clear the dismissal state so that
  // subsequent play attempts can re-trigger the upgrade modal
  const resetUpgradeDismissal = React.useCallback(() => {
    upgradeDismissedRef.current = false;
  }, []);

  const enforceSignedGate = React.useCallback(
    (el: HTMLAudioElement | null) => {
      if (!el) return false;
      if (!isSignedIn) return false;
      if (!ENABLE_SIGNED_IN_GATE) return false;
      const allowedLocalMax = Math.max(
        0,
        SIGNED_IN_FREE_SECONDS - signedBaseOffsetRef.current,
      );
      const eps = 0.02;
      if (el.currentTime >= allowedLocalMax - eps) {
        try {
          el.currentTime = allowedLocalMax;
        } catch {}
        if (!upgradeDismissedRef.current) setRequireUpgrade(true);
        try {
          el.pause();
        } catch {}
        return true;
      }
      return false;
    },
    [isSignedIn, signedBaseOffsetRef],
  );

  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    const onPlay = () => {
      if (!isSignedIn || !ENABLE_SIGNED_IN_GATE) return;
      upgradeDismissedRef.current = false;
      if (enforceSignedGate(el)) return;
      if (updateSignedDisplayAndGate(el)) return;
      if (signedTimeoutRef.current != null) {
        try {
          clearTimeout(signedTimeoutRef.current);
        } catch {}
        signedTimeoutRef.current = null;
      }
      const allowedLocalMax = Math.max(
        0,
        SIGNED_IN_FREE_SECONDS - signedBaseOffsetRef.current,
      );
      const msLeft = Math.max(0, (allowedLocalMax - el.currentTime) * 1000);
      if (msLeft <= 0) {
        enforceSignedGate(el);
      } else {
        signedTimeoutRef.current = window.setTimeout(
          () => enforceSignedGate(audioRef.current),
          Math.min(msLeft, 60_000),
        );
      }
      if (signedRafRef.current == null) {
        const tick = () => {
          const node = audioRef.current;
          if (!node) return;
          if (!isSignedIn || !ENABLE_SIGNED_IN_GATE) return;
          updateSignedDisplayAndGate(node);
          if (enforceSignedGate(node)) {
            if (signedRafRef.current)
              cancelAnimationFrame(signedRafRef.current);
            signedRafRef.current = null;
            return;
          }
          signedRafRef.current = requestAnimationFrame(tick);
        };
        signedRafRef.current = requestAnimationFrame(tick);
      }
    };
    const onTimeUpdate = () => {
      if (!isSignedIn || !ENABLE_SIGNED_IN_GATE) return;
      const el2 = audioRef.current;
      if (updateSignedDisplayAndGate(el2 || null)) return;
      enforceSignedGate(el2 || null);
    };
    const onPauseOrEnded = () => {
      if (!isSignedIn || !ENABLE_SIGNED_IN_GATE) return;
      updateSignedDisplayAndGate(audioRef.current);
      if (signedTimeoutRef.current != null) {
        try {
          clearTimeout(signedTimeoutRef.current);
        } catch {}
        signedTimeoutRef.current = null;
      }
      if (signedRafRef.current != null) {
        try {
          cancelAnimationFrame(signedRafRef.current);
        } catch {}
        signedRafRef.current = null;
      }
    };
    el.addEventListener('play', onPlay);
    el.addEventListener('timeupdate', onTimeUpdate);
    el.addEventListener('pause', onPauseOrEnded);
    el.addEventListener('ended', onPauseOrEnded);
    return () => {
      el.removeEventListener('play', onPlay);
      el.removeEventListener('timeupdate', onTimeUpdate);
      el.removeEventListener('pause', onPauseOrEnded);
      el.removeEventListener('ended', onPauseOrEnded);
      if (signedTimeoutRef.current != null) {
        try {
          clearTimeout(signedTimeoutRef.current);
        } catch {}
        signedTimeoutRef.current = null;
      }
      if (signedRafRef.current != null) {
        try {
          cancelAnimationFrame(signedRafRef.current);
        } catch {}
        signedRafRef.current = null;
      }
    };
    // signedBaseOffsetRef is a ref and doesn't need to be in the dependency array
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    audioRef,
    isSignedIn,
    enforceSignedGate,
    updateSignedDisplayAndGate,
    audioUrl,
  ]);

  const triggerGate = React.useCallback(() => {
    upgradeDismissedRef.current = false;
    setRequireUpgrade(true);
    const el = audioRef.current;
    if (el) {
      try {
        el.pause();
      } catch {}
    }
  }, [audioRef]);

  return {
    requireUpgrade,
    signedDisplay,
    enforceSignedGate,
    dismissUpgrade,
    resetUpgradeDismissal,
    triggerGate,
  } as const;
}
