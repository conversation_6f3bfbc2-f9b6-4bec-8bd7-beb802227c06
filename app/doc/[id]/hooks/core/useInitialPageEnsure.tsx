'use client';
import React from 'react';
import { getAuthHeaders } from '@/lib/access/client';
import { logger } from '@/lib/logger';
import { buildAnonQueryString } from '../utils/buildAnonQueryString';
import { handleAudioResponse } from '../utils/handleAudioResponse';

type PageRef = { id: string; number: number; status: string };

export interface InitialPageEnsureHandlers {
  setProcessing: (val: boolean) => void;
  setProcessingStage: (stage: string) => void;
  setAudioUrl: (url: string) => void;
  setNextUrl: (url: string) => void;
  setIsChunked: (val: boolean) => void;
  setChunkTotal: (total: number) => void;
  setChunkCurrent: (chunk: number) => void;
  setChunkNextUrl: (url: string | null) => void;
  setCurrentIdx: (idx: number | ((prev: number) => number)) => void;
  triggerAnonGate: () => void;
  triggerSignedGate: () => void;
}

/**
 * Manages initial page audio fetching and document status polling.
 * Handles retry logic, processing stages, and automatic page skipping.
 */
export function useInitialPageEnsure(
  docId: string,
  firstPageId: string | undefined,
  pageOrder: PageRef[],
  audioUrl: string | null,
  anonHeaders: Record<string, string>,
  requestedRef: React.MutableRefObject<Set<string>>,
  initialEnsuredRef: React.MutableRefObject<boolean>,
  knownChunksRef: React.MutableRefObject<Map<number, string>>,
  nextPreloadAudioRef: React.MutableRefObject<HTMLAudioElement | null>,
  pendingAutoplayRef: React.MutableRefObject<boolean>,
  isPlaying: boolean,
  handlers: InitialPageEnsureHandlers,
): void {
  const {
    setProcessing,
    setProcessingStage,
    setAudioUrl,
    setNextUrl,
    setIsChunked,
    setChunkTotal,
    setChunkCurrent,
    setChunkNextUrl,
    setCurrentIdx,
    triggerAnonGate,
    triggerSignedGate,
  } = handlers;

  // Use refs for values that change but shouldn't restart polling
  const audioUrlRef = React.useRef(audioUrl);
  const anonHeadersRef = React.useRef(anonHeaders);
  const pageOrderRef = React.useRef(pageOrder);
  const firstPageIdRef = React.useRef(firstPageId);

  // Update refs when values change
  React.useEffect(() => {
    audioUrlRef.current = audioUrl;
  }, [audioUrl]);

  React.useEffect(() => {
    anonHeadersRef.current = anonHeaders;
  }, [anonHeaders]);

  React.useEffect(() => {
    pageOrderRef.current = pageOrder;
  }, [pageOrder]);

  React.useEffect(() => {
    firstPageIdRef.current = firstPageId;
  }, [firstPageId]);

  // Initial ensure and doc status polling
  React.useEffect(() => {
    async function ensure(
      pageId?: string | null,
      setAsCurrent?: boolean,
      retryCount: number = 0,
    ) {
      if (!pageId) return;
      try {
        // Prevent infinite retry loops
        if (retryCount > 20) {
          logger.error('[interactive] Max retries exceeded for page', pageId);
          setProcessing(false);
          return;
        }

        if (requestedRef.current.has(pageId)) return;
        requestedRef.current.add(pageId);
        const qs = await buildAnonQueryString(anonHeadersRef.current);
        const r = await fetch(`/api/page/${pageId}/audio${qs}`, {
          method: 'GET',
          headers: getAuthHeaders(anonHeadersRef.current),
          cache: 'no-store',
        });
        const j = await r.json();

        // Handle plan limit responses from API
        if (j.requireLogin) {
          logger.log(
            '[interactive] Anonymous free trial limit reached via API',
          );
          setProcessing(false);
          triggerAnonGate();
          return;
        }
        if (j.requireUpgrade) {
          logger.log(
            '[interactive] Plan limit reached for signed-in user via API',
          );
          setProcessing(false);
          triggerSignedGate();
          return;
        }

        // Handle processing/not started status - poll until ready
        if (j.status === 'processing' || j.status === 'not_started') {
          logger.log(
            `[interactive] Page still processing (attempt ${retryCount + 1}), status:`,
            j.status,
            'stage:',
            j.processingStage,
          );
          if (j.processingStage) {
            setProcessingStage(j.processingStage);
          }
          // Remove from requested set so we can retry
          requestedRef.current.delete(pageId);
          // Wait 2 seconds and retry
          await new Promise((resolve) => setTimeout(resolve, 2000));
          logger.log('[interactive] Retrying audio fetch...');
          await ensure(pageId, setAsCurrent, retryCount + 1);
          return;
        }

        // Handle error responses
        if (!j.ok) {
          logger.error('[interactive] Audio fetch failed:', j);
          setProcessing(false);
          return;
        }

        if (j.ok) {
          if (j.skip || j.skippable) {
            if (setAsCurrent) {
              const idx = Math.max(
                0,
                pageOrderRef.current.findIndex((p) => p.id === pageId),
              );
              const nextP = pageOrderRef.current[idx + 1];
              if (nextP) {
                setCurrentIdx(idx + 1);
                try {
                  requestedRef.current.delete(pageId);
                } catch {}
                await ensure(nextP.id, true);
              }
            }
            return;
          }
          // Update processing stage from API response
          if (j.processingStage) {
            setProcessingStage(j.processingStage);
          }

          // Handle audio response (streaming, single URL, or chunked)
          handleAudioResponse(j, !!setAsCurrent, {
            setIsChunked,
            setAudioUrl,
            setNextUrl,
            setChunkTotal,
            setChunkCurrent,
            setChunkNextUrl,
            knownChunksRef,
            nextPreloadAudioRef,
            pendingAutoplayRef,
            isPlaying,
          });
        }
      } catch {}
    }

    // Poll document status periodically
    (async () => {
      try {
        const qs = await buildAnonQueryString(anonHeadersRef.current);
        const res = await fetch(`/api/doc/${docId}${qs}`, {
          cache: 'no-store',
          headers: getAuthHeaders(anonHeadersRef.current),
        });
        const j = await res.json();
        const st = j.document?.status;

        // Handle new queue-based statuses
        const isProcessing =
          st === 'uploaded' ||
          st === 'translating_first_page' ||
          st === 'translated_first_page' ||
          st === 'generating_audio_first_page' ||
          (st === 'generated_audio_first_page' && !j.firstPageAudioReady);

        // Update processing stage based on document status
        if (st === 'translating_first_page') {
          setProcessingStage('translating');
        } else if (st === 'translated_first_page') {
          setProcessingStage('translated');
        } else if (
          st === 'generating_audio_first_page' ||
          st === 'generated_audio_first_page'
        ) {
          setProcessingStage('generating_audio');
        } else if (st === 'uploaded') {
          setProcessingStage('processing_file');
        }

        setProcessing(isProcessing && !audioUrlRef.current);

        // Only retrieve audio AFTER document is ready
        const initialId =
          (firstPageIdRef.current && firstPageIdRef.current) ||
          pageOrderRef.current[0]?.id;
        if (
          initialId &&
          !audioUrlRef.current &&
          !initialEnsuredRef.current &&
          st === 'generated_audio_first_page' &&
          j.firstPageAudioReady
        ) {
          initialEnsuredRef.current = true;
          logger.log(
            '[interactive] Document ready, fetching audio for first page',
          );
          ensure(initialId, true);
        }
      } catch {}
    })();

    // Set up polling interval (every 1.5 seconds) to keep checking status
    const pollInterval = setInterval(async () => {
      // STOP polling once audio is loaded
      if (audioUrlRef.current) {
        logger.log(
          '[interactive] Audio loaded, stopping document status polling',
        );
        clearInterval(pollInterval);
        return;
      }

      // Continue polling if audio not yet loaded
      try {
        const qs = await buildAnonQueryString(anonHeadersRef.current);
        const res = await fetch(`/api/doc/${docId}${qs}`, {
          cache: 'no-store',
          headers: getAuthHeaders(anonHeadersRef.current),
        });
        const j = await res.json();
        const st = j.document?.status;

        const isProcessing =
          st === 'uploaded' ||
          st === 'translating_first_page' ||
          st === 'translated_first_page' ||
          st === 'generating_audio_first_page' ||
          (st === 'generated_audio_first_page' && !j.firstPageAudioReady);

        if (st === 'translating_first_page') {
          setProcessingStage('translating');
        } else if (st === 'translated_first_page') {
          setProcessingStage('translated');
        } else if (
          st === 'generating_audio_first_page' ||
          st === 'generated_audio_first_page'
        ) {
          setProcessingStage('generating_audio');
        } else if (st === 'uploaded') {
          setProcessingStage('processing_file');
        }

        setProcessing(isProcessing && !audioUrlRef.current);

        const initialId =
          (firstPageIdRef.current && firstPageIdRef.current) ||
          pageOrderRef.current[0]?.id;
        if (
          initialId &&
          !audioUrlRef.current &&
          !initialEnsuredRef.current &&
          st === 'generated_audio_first_page' &&
          j.firstPageAudioReady
        ) {
          initialEnsuredRef.current = true;
          logger.log(
            '[interactive] Document ready, fetching audio for first page',
          );
          ensure(initialId, true);
        }
      } catch {}
    }, 1500);

    return () => clearInterval(pollInterval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [docId]);
}
