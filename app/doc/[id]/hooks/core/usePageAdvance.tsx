'use client';
import React from 'react';
import { logger } from '@/lib/logger';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';

type PageRef = { id: string; number: number; status: string };

/**
 * Simplified page/chunk advance hook.
 * Advances to next page/chunk when current one ends.
 * Phase 1: No combined audio, no usage tracking.
 *
 * Handles case where user scrubs to end before prefetch completes.
 */
export function usePageAdvance(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  audioUrl: string | null,
  next: PageRef | undefined,
  nextUrl: string | null,
  isChunked: boolean,
  chunkNextUrl: string | null,
  anonHeaders: Record<string, string>,
  requestedRef: React.MutableRefObject<Set<string>>,
  setAudioUrl: (u: string) => void,
  setNextUrl: (u: string | null) => void,
  setCurrentIdx: (fn: (i: number) => number) => void,
  setChunkCurrent: (fn: (i: number) => number) => void,
  setChunkNextUrl: (u: string | null) => void,
  pendingAutoplayRef: React.MutableRefObject<boolean>,
  setProcessing: (processing: boolean) => void,
  setProcessingStage: (stage: string) => void,
): void {
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;

    const onEnded = async () => {
      logger.log('[usePageAdvance] Audio ended event fired', {
        isChunked,
        hasNext: !!next,
        hasNextUrl: !!nextUrl,
        hasChunkNextUrl: !!chunkNextUrl,
        nextPageNumber: next?.number,
      });

      // Handle chunked audio - advance to next chunk if available
      if (isChunked && chunkNextUrl) {
        logger.log('[usePageAdvance] Chunk ended, switching to next chunk');
        setAudioUrl(chunkNextUrl);
        setChunkCurrent((c) => c + 1);
        setChunkNextUrl(null);
        pendingAutoplayRef.current = true;
        return;
      }

      // If chunked audio ended with no more chunks, it means page ended
      // Handle page advance (works for both chunked and non-chunked)
      if (next) {
        if (nextUrl) {
          // Next page already prefetched - advance immediately
          logger.log('[usePageAdvance] Page ended, switching to next page', {
            nextPage: next.number,
          });

          setAudioUrl(nextUrl);
          setNextUrl(null);
          setCurrentIdx((i) => i + 1);
          pendingAutoplayRef.current = true;
        } else {
          // Next page not prefetched (user scrubbed to end or chunked page ended) - poll until ready
          logger.log(
            '[usePageAdvance] Page ended but next not ready, polling until ready',
            {
              nextPage: next.number,
              wasChunked: isChunked,
            },
          );

          // Check if already requesting (from prefetch)
          if (!requestedRef.current.has(next.id)) {
            requestedRef.current.add(next.id);
          }
          // Continue polling even if already requested - we need the result!
          logger.log('[usePageAdvance] Polling for next page audio', {
            alreadyRequested: requestedRef.current.has(next.id),
          });

          // Show processing overlay
          setProcessing(true);
          setProcessingStage('processing');

          let pollAttempts = 0;
          const maxPollAttempts = 60; // 60 seconds timeout

          const pollInterval = setInterval(async () => {
            pollAttempts++;

            if (pollAttempts >= maxPollAttempts) {
              clearInterval(pollInterval);
              setProcessing(false);
              setProcessingStage('timeout');
              logger.error('[usePageAdvance] Timeout waiting for next page');
              return;
            }

            try {
              // Build auth headers
              const headers = getAuthHeaders({ ...anonHeaders }) as Record<
                string,
                string
              >;
              const sid = headers['x-anon-id'];
              let sids: string[] = [];

              // Parse alt IDs
              try {
                const raw = headers['x-anon-alt-ids'];
                if (raw) {
                  if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
                  else sids = raw.split(',').map((x) => x.trim());
                }
              } catch {}

              if (sid) sids = [sid, ...sids];

              // Fallback: get anon candidates if no sids
              if (sids.length === 0) {
                try {
                  const cand = await getAnonCandidates();
                  if (cand) {
                    sids = Array.from(
                      new Set(
                        [cand.stableId, cand.fpjsId, cand.coarseId].filter(
                          Boolean,
                        ) as string[],
                      ),
                    );
                  }
                } catch {}
              }

              // Build query string
              const qs = sids.length
                ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
                : '';

              // Fetch next page audio
              const response = await fetch(
                `/api/page/${next.id}/ensure-tts${qs}`,
                {
                  method: 'POST',
                  headers,
                },
              );
              const data = await response.json();

              // Handle response
              if (data?.ok) {
                clearInterval(pollInterval);
                setProcessing(false);

                if (data.skip || data.skippable) {
                  // Skippable page detected during polling
                  logger.log(
                    '[usePageAdvance] Next page is skippable, stopping auto-advance',
                    {
                      skippedPage: next.number,
                    },
                  );

                  // DON'T increment currentIdx - this was causing the bug
                  // The UI will stay on current page, user can navigate manually
                  return;
                }

                const url =
                  data.streamUrl || data.url || (data.urls && data.urls[0]);
                if (url) {
                  logger.log('[usePageAdvance] Next page ready, advancing now');
                  setAudioUrl(url);
                  setCurrentIdx((i) => i + 1);
                  pendingAutoplayRef.current = true;
                }
              } else if (data?.processingStage) {
                // Still processing - update stage
                setProcessingStage(data.processingStage);
                logger.log(
                  '[usePageAdvance] Still processing:',
                  data.processingStage,
                );
              }
            } catch (err) {
              logger.error('[usePageAdvance] Error polling next page:', err);
              // Keep polling on error
            }
          }, 1000);
        }
      } else {
        logger.log('[usePageAdvance] No next page available (end of document)');
      }
    };

    el.addEventListener('ended', onEnded);

    return () => {
      el.removeEventListener('ended', onEnded);
    };
  }, [
    audioRef,
    audioUrl,
    next,
    nextUrl,
    isChunked,
    chunkNextUrl,
    anonHeaders,
    requestedRef,
    setAudioUrl,
    setNextUrl,
    setCurrentIdx,
    setChunkCurrent,
    setChunkNextUrl,
    pendingAutoplayRef,
    setProcessing,
    setProcessingStage,
  ]);
}
