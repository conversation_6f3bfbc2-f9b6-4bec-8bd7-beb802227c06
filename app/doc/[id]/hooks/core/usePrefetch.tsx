'use client';
import React from 'react';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';
import {
  ELAPSED_FALLBACK_SEC,
  MIN_REMAIN_SEC,
  PREFETCH_PERCENT,
} from '../utils/constants';

type PageRef = { id: string; number: number; status: string };

/**
 * Prefetches next page audio when current page plays near the end.
 * Triggers at 55% progress OR ≤10 seconds remaining.
 * Also triggers on play event as fallback.
 */
export function usePrefetch(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  isChunked: boolean,
  next: PageRef | undefined,
  nextUrl: string | null,
  anonHeaders: Record<string, string>,
  requestedRef: React.MutableRefObject<Set<string>>,
  setNextUrl: (u: string) => void,
): void {
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;

    /**
     * Fetch next page audio
     */
    const triggerPrefetch = async () => {
      // Skip if chunked (chunk polling handles this)
      if (isChunked) return;
      // Skip if no next page or already prefetched
      if (!next || nextUrl) return;
      // Guard against duplicate requests
      if (requestedRef.current.has(next.id)) return;

      requestedRef.current.add(next.id);

      try {
        // Build auth headers
        const headers = getAuthHeaders({ ...anonHeaders }) as Record<
          string,
          string
        >;
        const sid = headers['x-anon-id'];
        let sids: string[] = [];

        // Parse alt IDs
        try {
          const raw = headers['x-anon-alt-ids'];
          if (raw) {
            if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
            else sids = raw.split(',').map((x) => x.trim());
          }
        } catch {}

        if (sid) sids = [sid, ...sids];

        // Fallback: get anon candidates if no sids
        if (sids.length === 0) {
          try {
            const cand = await getAnonCandidates();
            if (cand) {
              sids = Array.from(
                new Set(
                  [cand.stableId, cand.fpjsId, cand.coarseId].filter(
                    Boolean,
                  ) as string[],
                ),
              );
            }
          } catch {}
        }

        // Build query string
        const qs = sids.length
          ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
          : '';

        // Fetch next page audio
        const response = await fetch(`/api/page/${next.id}/ensure-tts${qs}`, {
          method: 'POST',
          headers,
        });
        const data = await response.json();

        // Handle response (streaming, complete, or chunked)
        if (data?.ok) {
          if (data.skip || data.skippable) {
            // Skippable page - don't set nextUrl
            return;
          }
          const url = data.streamUrl || data.url || (data.urls && data.urls[0]);
          if (url) setNextUrl(url);
        }
      } catch (err) {
        // Silent fail - prefetch is best-effort
      }
    };

    /**
     * Check if prefetch should trigger based on progress
     */
    const onTimeUpdate = () => {
      if (!next || nextUrl) return;

      const d = el.duration;

      // Fallback for unknown duration: prefetch after 5s
      if (!isFinite(d) || d <= 0) {
        if (el.currentTime >= ELAPSED_FALLBACK_SEC) {
          triggerPrefetch();
        }
        return;
      }

      const t = el.currentTime;
      const percent = t / d;
      const remaining = d - t;

      // Trigger at 55% OR ≤10s remaining
      if (percent >= PREFETCH_PERCENT || remaining <= MIN_REMAIN_SEC) {
        triggerPrefetch();
      }
    };

    /**
     * Fallback: prefetch on play event if not already done
     * Only triggers for unknown/invalid duration - otherwise let onTimeUpdate handle timing
     */
    const onPlay = () => {
      if (!nextUrl && next && !requestedRef.current.has(next.id)) {
        const d = el.duration;

        // Only use onPlay as fallback for unknown/invalid duration
        if (!isFinite(d) || d <= 0) {
          triggerPrefetch();
          return;
        }

        // Otherwise, let onTimeUpdate handle it (respect timing thresholds)
        // Don't trigger immediately on play
      }
    };

    // Pre-calculate timer for known durations
    let timer: ReturnType<typeof setTimeout> | null = null;
    if (isFinite(el.duration) && el.duration > 0 && next && !nextUrl) {
      const ms = Math.max(
        0,
        (el.duration * PREFETCH_PERCENT - el.currentTime) * 1000,
      );
      timer = setTimeout(triggerPrefetch, ms);
    }

    // Attach event listeners
    el.addEventListener('timeupdate', onTimeUpdate);
    el.addEventListener('play', onPlay);

    return () => {
      el.removeEventListener('timeupdate', onTimeUpdate);
      el.removeEventListener('play', onPlay);
      if (timer) clearTimeout(timer);
    };
  }, [
    audioRef,
    isChunked,
    next,
    nextUrl,
    anonHeaders,
    requestedRef,
    setNextUrl,
  ]);
}
