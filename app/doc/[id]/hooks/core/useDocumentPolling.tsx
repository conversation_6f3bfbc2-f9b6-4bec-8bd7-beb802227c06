'use client';
import React from 'react';
import { getAuthHeaders } from '@/lib/access/client';
import { logger } from '@/lib/logger';
import { buildAnonQueryString } from '../utils/buildAnonQueryString';
import { handleAudioResponse } from '../utils/handleAudioResponse';

type PageRef = { id: string; number: number; status: string };

export interface DocumentPollingHandlers {
  setProcessing: (val: boolean) => void;
  setProcessingStage: (stage: string) => void;
  setAudioUrl: (url: string) => void;
  setNextUrl: (url: string) => void;
  setIsChunked: (val: boolean) => void;
  setChunkTotal: (total: number) => void;
  setChunkCurrent: (chunk: number) => void;
  setChunkNextUrl: (url: string | null) => void;
  setCurrentIdx: (idx: number | ((prev: number) => number)) => void;
}

/**
 * Simplified document polling hook.
 * Polls document status and fetches first page audio when ready.
 * Phase 1: No usage gating logic.
 */
export function useDocumentPolling(
  docId: string,
  firstPageId: string | undefined,
  pageOrder: PageRef[],
  audioUrl: string | null,
  anonHeaders: Record<string, string>,
  requestedRef: React.MutableRefObject<Set<string>>,
  initialEnsuredRef: React.MutableRefObject<boolean>,
  knownChunksRef: React.MutableRefObject<Map<number, string>>,
  nextPreloadAudioRef: React.MutableRefObject<HTMLAudioElement | null>,
  pendingAutoplayRef: React.MutableRefObject<boolean>,
  isPlaying: boolean,
  handlers: DocumentPollingHandlers,
): void {
  const {
    setProcessing,
    setProcessingStage,
    setAudioUrl,
    setNextUrl,
    setIsChunked,
    setChunkTotal,
    setChunkCurrent,
    setChunkNextUrl,
    setCurrentIdx,
  } = handlers;

  // Use refs for values that change but shouldn't restart polling
  const audioUrlRef = React.useRef(audioUrl);
  const anonHeadersRef = React.useRef(anonHeaders);
  const pageOrderRef = React.useRef(pageOrder);
  const firstPageIdRef = React.useRef(firstPageId);

  // Update refs when values change
  React.useEffect(() => {
    audioUrlRef.current = audioUrl;
  }, [audioUrl]);

  React.useEffect(() => {
    anonHeadersRef.current = anonHeaders;
  }, [anonHeaders]);

  React.useEffect(() => {
    pageOrderRef.current = pageOrder;
  }, [pageOrder]);

  React.useEffect(() => {
    firstPageIdRef.current = firstPageId;
  }, [firstPageId]);

  // Main effect: document polling and audio fetching
  React.useEffect(() => {
    /**
     * Fetch audio for a specific page with retry logic
     */
    async function fetchPageAudio(
      pageId?: string | null,
      setAsCurrent?: boolean,
      retryCount: number = 0,
    ) {
      if (!pageId) return;

      try {
        // Prevent infinite retry loops
        if (retryCount > 20) {
          logger.error(
            '[useDocumentPolling] Max retries exceeded for page',
            pageId,
          );
          setProcessing(false);
          return;
        }

        // Deduplicate requests
        if (requestedRef.current.has(pageId)) return;
        requestedRef.current.add(pageId);

        const qs = await buildAnonQueryString(anonHeadersRef.current);
        const r = await fetch(`/api/page/${pageId}/audio${qs}`, {
          method: 'GET',
          headers: getAuthHeaders(anonHeadersRef.current),
          cache: 'no-store',
        });
        const j = await r.json();

        // Handle still processing - retry after delay
        if (j.status === 'processing' || j.status === 'not_started') {
          logger.log(
            `[useDocumentPolling] Page not ready (attempt ${retryCount + 1}/20)`,
            {
              pageId,
              status: j.status,
              stage: j.processingStage,
            },
          );
          if (j.processingStage) {
            setProcessingStage(j.processingStage);
          }
          // Remove from requested set so we can retry
          requestedRef.current.delete(pageId);
          // Wait 2 seconds and retry
          await new Promise((resolve) => setTimeout(resolve, 2000));
          await fetchPageAudio(pageId, setAsCurrent, retryCount + 1);
          return;
        }

        // Handle error responses
        if (!j.ok) {
          logger.error('[useDocumentPolling] Audio fetch failed:', j);
          setProcessing(false);
          return;
        }

        // Handle skippable pages
        if (j.ok && (j.skip || j.skippable)) {
          if (setAsCurrent) {
            const idx = Math.max(
              0,
              pageOrderRef.current.findIndex((p) => p.id === pageId),
            );
            const nextP = pageOrderRef.current[idx + 1];
            if (nextP) {
              setCurrentIdx(idx + 1);
              requestedRef.current.delete(pageId);
              await fetchPageAudio(nextP.id, true);
            }
          }
          return;
        }

        // Update processing stage from API response
        if (j.processingStage) {
          setProcessingStage(j.processingStage);
        }

        // Handle successful audio response (streaming, single URL, or chunked)
        handleAudioResponse(j, !!setAsCurrent, {
          setIsChunked,
          setAudioUrl,
          setNextUrl,
          setChunkTotal,
          setChunkCurrent,
          setChunkNextUrl,
          knownChunksRef,
          nextPreloadAudioRef,
          pendingAutoplayRef,
          isPlaying,
        });
      } catch (err) {
        logger.error('[useDocumentPolling] Error fetching page audio:', err);
      }
    }

    /**
     * Check document status and trigger audio fetch when ready
     */
    async function checkDocumentStatus() {
      try {
        const qs = await buildAnonQueryString(anonHeadersRef.current);
        const res = await fetch(`/api/doc/${docId}${qs}`, {
          cache: 'no-store',
          headers: getAuthHeaders(anonHeadersRef.current),
        });
        const j = await res.json();
        const st = j.document?.status;

        // Determine if document is still processing
        const isProcessing =
          st === 'uploaded' ||
          st === 'translating_first_page' ||
          st === 'translated_first_page' ||
          st === 'generating_audio_first_page' ||
          (st === 'generated_audio_first_page' && !j.firstPageAudioReady);

        // Update processing stage based on document status
        if (st === 'translating_first_page') {
          setProcessingStage('translating');
        } else if (st === 'translated_first_page') {
          setProcessingStage('translated');
        } else if (
          st === 'generating_audio_first_page' ||
          st === 'generated_audio_first_page'
        ) {
          setProcessingStage('generating_audio');
        } else if (st === 'uploaded') {
          setProcessingStage('processing_file');
        }

        setProcessing(isProcessing && !audioUrlRef.current);

        // Fetch audio when document is ready
        const initialId = firstPageIdRef.current;
        if (
          initialId &&
          !audioUrlRef.current &&
          !initialEnsuredRef.current &&
          st === 'generated_audio_first_page' &&
          j.firstPageAudioReady
        ) {
          initialEnsuredRef.current = true;
          logger.log(
            '[useDocumentPolling] Document ready, fetching audio for first page',
          );
          fetchPageAudio(initialId, true);
        }
      } catch (err) {
        logger.error(
          '[useDocumentPolling] Error checking document status:',
          err,
        );
      }
    }

    // Initial check
    checkDocumentStatus();

    // Set up polling interval (every 1.5 seconds)
    const pollInterval = setInterval(() => {
      if (!audioUrlRef.current) {
        checkDocumentStatus();
      }
    }, 1500);

    return () => clearInterval(pollInterval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [docId]);
}
