import React from 'react';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';
import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

type PageRef = { id: string; number: number; status: string };

/**
 * Automatically triggers processing of subsequent pages when audio
 * duration is below the threshold.
 *
 * This creates a "chain reaction" where short pages automatically trigger
 * processing of the next page, ensuring sufficient buffer time ahead.
 *
 * KEY BEHAVIOR:
 * - Monitors nextPreloadAudioRef (next page being prefetched)
 * - When next page duration < 150s, triggers processing of page N+2
 * - Also monitors current audioRef for manual navigation scenarios
 *
 * Example flow:
 * - On page 8, page 9 loads into nextPreloadAudioRef with 10s duration
 * - 10s < 150s threshold → trigger page 10 processing
 * - Page 10 loads with 20s duration → trigger page 11 processing
 * - Page 11 loads with 180s duration → stop (sufficient buffer)
 */
export function useAutoChainProcessing(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  nextPreloadAudioRef: React.RefObject<HTMLAudioElement | null>,
  currentPageId: string | undefined,
  next: PageRef | undefined,
  pageOrder: PageRef[],
  currentIdx: number,
  anonHeaders: Record<string, string>,
  processingTriggeredRef: React.MutableRefObject<Set<string>>,
) {
  // Track which page's audio loading was last processed to prevent duplicates
  const lastProcessedNextPageIdRef = React.useRef<string | null>(null);
  const lastProcessedCurrentPageIdRef = React.useRef<string | null>(null);

  // Shared function to trigger page processing
  const triggerPageProcessing = React.useCallback(
    async (targetPage: PageRef, context: string) => {
      // Guard against duplicate processing requests
      if (processingTriggeredRef.current.has(targetPage.id)) {
        logger.log(
          `[auto-chain-processing:${context}] Page already triggered, skipping`,
          {
            pageId: targetPage.id,
            pageNumber: targetPage.number,
          },
        );
        return;
      }

      logger.log(
        `[auto-chain-processing:${context}] Triggering page processing`,
        {
          pageId: targetPage.id,
          pageNumber: targetPage.number,
        },
      );

      processingTriggeredRef.current.add(targetPage.id);

      try {
        const headers = getAuthHeaders({ ...anonHeaders }) as Record<
          string,
          string
        >;
        const sid = headers['x-anon-id'];
        let sids: string[] = [];
        try {
          const raw = headers['x-anon-alt-ids'];
          if (raw) {
            if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
            else sids = raw.split(',').map((x) => x.trim());
          }
        } catch {}
        if (sid) sids = [sid, ...sids];
        if (sids.length === 0) {
          try {
            const cand2 = await getAnonCandidates();
            if (cand2) {
              sids = Array.from(
                new Set(
                  [cand2.stableId, cand2.fpjsId, cand2.coarseId].filter(
                    Boolean,
                  ) as string[],
                ),
              );
            }
          } catch {}
        }
        const qs = sids.length
          ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
          : '';

        // Trigger ensure-tts which will start translation + TTS if not already processed
        const response = await fetch(
          `/api/page/${targetPage.id}/ensure-tts${qs}`,
          {
            method: 'POST',
            headers,
          },
        );
        const result = await response.json();

        if (result?.ok) {
          logger.log(
            `[auto-chain-processing:${context}] Page processing triggered successfully`,
            {
              pageId: targetPage.id,
              pageNumber: targetPage.number,
            },
          );
        } else if (result?.retry) {
          logger.log(
            `[auto-chain-processing:${context}] Page processing in progress`,
            {
              pageId: targetPage.id,
              pageNumber: targetPage.number,
              stage: result.processingStage,
            },
          );
        }
      } catch (error) {
        logger.error(
          `[auto-chain-processing:${context}] Failed to trigger processing`,
          {
            pageId: targetPage.id,
            pageNumber: targetPage.number,
            error,
          },
        );
        // Remove from triggered set so we can retry
        processingTriggeredRef.current.delete(targetPage.id);
      }
    },
    [anonHeaders, processingTriggeredRef],
  );

  // EFFECT 1: Monitor nextPreloadAudioRef (next page being prefetched)
  // This is the PRIMARY chain processing logic
  React.useEffect(() => {
    const el = nextPreloadAudioRef.current;
    if (!el || !next) return;

    const onLoadedMetadata = () => {
      const duration = el.duration;

      // Check if duration is valid
      if (!isFinite(duration) || duration <= 0) {
        logger.log(
          '[auto-chain-processing:next-preload] Invalid duration, skipping',
          { duration },
        );
        return;
      }

      // PREVENT DUPLICATES: Skip if we already processed this page's audio loading
      if (!next) return;
      if (lastProcessedNextPageIdRef.current === next.id) {
        logger.log(
          '[auto-chain-processing:next-preload] Already processed this page, skipping',
          {
            pageId: next.id,
            pageNumber: next.number,
          },
        );
        return;
      }
      lastProcessedNextPageIdRef.current = next.id;

      logger.log(
        '[auto-chain-processing:next-preload] Next page audio loaded',
        {
          nextPageId: next.id,
          nextPageNumber: next.number,
          duration,
          currentIdx,
          calculatedPageAfterNext: currentIdx + 2,
          threshold: appConfig.nextPageProcessThresholdSeconds,
          audioSrc: el.src,
        },
      );

      // Get page N+2 (page after next)
      const pageAfterNext = pageOrder[currentIdx + 2];
      if (!pageAfterNext) {
        logger.log(
          '[auto-chain-processing:next-preload] No page N+2 available, stopping chain',
        );
        return;
      }

      // If next page duration < threshold, trigger page N+2
      if (duration < appConfig.nextPageProcessThresholdSeconds) {
        logger.log(
          '[auto-chain-processing:next-preload] Next page duration below threshold',
          {
            nextPageNumber: next.number,
            duration,
            threshold: appConfig.nextPageProcessThresholdSeconds,
            triggeringPage: pageAfterNext.number,
          },
        );
        triggerPageProcessing(pageAfterNext, 'next-preload');
      } else {
        logger.log(
          '[auto-chain-processing:next-preload] Next page duration sufficient, stopping chain',
          {
            nextPageNumber: next.number,
            duration,
            threshold: appConfig.nextPageProcessThresholdSeconds,
          },
        );
      }
    };

    // Listen for metadata loaded event
    el.addEventListener('loadedmetadata', onLoadedMetadata);

    // REMOVED: el.readyState >= 1 check (caused handler to fire with mismatched state)

    return () => {
      el.removeEventListener('loadedmetadata', onLoadedMetadata);
    };
  }, [nextPreloadAudioRef, next, pageOrder, currentIdx, triggerPageProcessing]);

  // EFFECT 2: Monitor audioRef (current page)
  // This handles manual navigation scenarios
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el || !currentPageId) return;

    const onLoadedMetadata = () => {
      const duration = el.duration;

      // Check if duration is valid
      if (!isFinite(duration) || duration <= 0) {
        logger.log(
          '[auto-chain-processing:current] Invalid duration, skipping',
          { duration },
        );
        return;
      }

      // PREVENT DUPLICATES: Skip if we already processed this page's audio loading
      if (!currentPageId) return;
      if (lastProcessedCurrentPageIdRef.current === currentPageId) {
        logger.log(
          '[auto-chain-processing:current] Already processed this page, skipping',
          {
            pageId: currentPageId,
            pageNumber: pageOrder[currentIdx]?.number,
          },
        );
        return;
      }
      lastProcessedCurrentPageIdRef.current = currentPageId;

      logger.log('[auto-chain-processing:current] Current page audio loaded', {
        currentPageId,
        currentPageNumber: pageOrder[currentIdx]?.number,
        duration,
        currentIdx,
        calculatedNextPage: currentIdx + 1,
        threshold: appConfig.nextPageProcessThresholdSeconds,
        audioSrc: el.src,
      });

      // Get next page (N+1)
      const nextPage = pageOrder[currentIdx + 1];
      if (!nextPage) {
        logger.log(
          '[auto-chain-processing:current] No next page available, stopping chain',
        );
        return;
      }

      // If current page duration < threshold, trigger next page
      if (duration < appConfig.nextPageProcessThresholdSeconds) {
        logger.log(
          '[auto-chain-processing:current] Current page duration below threshold',
          {
            currentPageNumber: pageOrder[currentIdx]?.number,
            duration,
            threshold: appConfig.nextPageProcessThresholdSeconds,
            triggeringPage: nextPage.number,
          },
        );
        triggerPageProcessing(nextPage, 'current');
      } else {
        logger.log(
          '[auto-chain-processing:current] Current page duration sufficient, no chain needed',
          {
            currentPageNumber: pageOrder[currentIdx]?.number,
            duration,
            threshold: appConfig.nextPageProcessThresholdSeconds,
          },
        );
      }
    };

    // Listen for metadata loaded event
    el.addEventListener('loadedmetadata', onLoadedMetadata);

    // REMOVED: el.readyState >= 1 check (caused handler to fire with mismatched state)

    return () => {
      el.removeEventListener('loadedmetadata', onLoadedMetadata);
    };
  }, [audioRef, currentPageId, pageOrder, currentIdx, triggerPageProcessing]);
}
