'use client';
import React from 'react';
import { logger } from '@/lib/logger';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';

type PageRef = { id: string; number: number; status: string };

export interface UsePageNavigationProps {
  currentIdx: number;
  setCurrentIdx: (fn: (i: number) => number) => void;
  pageOrder: PageRef[];
  anonHeaders: Record<string, string>;
  requestedRef: React.MutableRefObject<Set<string>>;
  setAudioUrl: (url: string) => void;
  setIsChunked: (val: boolean) => void;
  setChunkTotal: (total: number) => void;
  setChunkCurrent: (chunk: number) => void;
  pendingAutoplayRef: React.MutableRefObject<boolean>;
}

export interface UsePageNavigationReturn {
  goToPreviousPage: () => void;
  goToNextPage: () => void;
  canGoToPrevious: boolean;
  canGoToNext: boolean;
  isNavigating: boolean;
}

/**
 * Hook for manual page navigation (previous/next buttons).
 * Handles direction-aware skippable page skipping and audio fetching.
 */
export function usePageNavigation({
  currentIdx,
  setCurrentIdx,
  pageOrder,
  anonHeaders,
  requestedRef,
  setAudioUrl,
  setIsChunked,
  setChunkTotal,
  setChunkCurrent,
  pendingAutoplayRef,
}: UsePageNavigationProps): UsePageNavigationReturn {
  // Track if navigation is in progress to prevent concurrent navigations
  const navigatingRef = React.useRef<boolean>(false);

  // State to disable buttons during navigation
  const [isNavigating, setIsNavigating] = React.useState<boolean>(false);

  /**
   * Navigate to a specific page index and load its audio.
   * Auto-skips skippable pages in the specified direction.
   *
   * @param direction - 'previous' or 'next'
   */
  const navigateToPage = React.useCallback(
    async (direction: 'previous' | 'next') => {
      logger.log('[usePageNavigation] navigateToPage called', {
        direction,
        currentIdx,
        pageOrderLength: pageOrder.length,
      });

      // Prevent concurrent navigations
      if (navigatingRef.current) {
        logger.log('[usePageNavigation] Navigation already in progress');
        return;
      }

      const step = direction === 'next' ? 1 : -1;
      let targetIdx = currentIdx + step;

      logger.log('[usePageNavigation] Navigation starting', {
        direction,
        step,
        currentIdx,
        targetIdx,
      });

      // Check bounds
      if (targetIdx < 0 || targetIdx >= pageOrder.length) {
        logger.log('[usePageNavigation] Target index out of bounds', {
          targetIdx,
        });
        return;
      }

      navigatingRef.current = true;
      setIsNavigating(true);

      try {
        // Find next non-skippable page in the direction
        let attempts = 0;
        const maxAttempts = pageOrder.length; // Prevent infinite loop

        while (attempts < maxAttempts) {
          const targetPage = pageOrder[targetIdx];
          if (!targetPage) {
            logger.log('[usePageNavigation] No page at target index', {
              targetIdx,
            });
            break;
          }

          // For manual navigation, allow re-requesting pages
          // Remove from requestedRef if it exists (page may have been requested before)
          if (requestedRef.current.has(targetPage.id)) {
            logger.log(
              '[usePageNavigation] Page was previously requested, allowing re-fetch',
              {
                pageId: targetPage.id,
                pageNumber: targetPage.number,
              },
            );
            requestedRef.current.delete(targetPage.id);
          }

          requestedRef.current.add(targetPage.id);

          try {
            // Build auth headers
            const headers = getAuthHeaders({ ...anonHeaders }) as Record<
              string,
              string
            >;
            const sid = headers['x-anon-id'];
            let sids: string[] = [];

            // Parse alt IDs
            try {
              const raw = headers['x-anon-alt-ids'];
              if (raw) {
                if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
                else sids = raw.split(',').map((x) => x.trim());
              }
            } catch {}

            if (sid) sids = [sid, ...sids];

            // Fallback: get anon candidates if no sids
            if (sids.length === 0) {
              try {
                const cand = await getAnonCandidates();
                if (cand) {
                  sids = Array.from(
                    new Set(
                      [cand.stableId, cand.fpjsId, cand.coarseId].filter(
                        Boolean,
                      ) as string[],
                    ),
                  );
                }
              } catch {}
            }

            // Build query string
            const qs = sids.length
              ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
              : '';

            // Fetch target page audio
            logger.log('[usePageNavigation] Fetching page audio', {
              direction,
              targetIdx,
              pageNumber: targetPage.number,
            });

            const response = await fetch(
              `/api/page/${targetPage.id}/ensure-tts${qs}`,
              {
                method: 'POST',
                headers,
              },
            );
            const data = await response.json();

            // Handle response
            if (data?.ok) {
              if (data.skip || data.skippable) {
                // Skippable page - skip in the navigation direction
                logger.log(
                  '[usePageNavigation] Page is skippable, continuing',
                  {
                    direction,
                    pageNumber: targetPage.number,
                  },
                );

                // Move to next page in the same direction
                targetIdx += step;

                // Check bounds again
                if (targetIdx < 0 || targetIdx >= pageOrder.length) {
                  logger.log(
                    '[usePageNavigation] Reached boundary while skipping',
                  );
                  break;
                }

                attempts++;
                continue; // Try next page
              }

              // Found non-skippable page with audio
              const url =
                data.streamUrl || data.url || (data.urls && data.urls[0]);
              if (url) {
                logger.log('[usePageNavigation] Found page audio, navigating', {
                  direction,
                  targetIdx,
                  pageNumber: targetPage.number,
                });

                // Clear chunked state (chunk cache will be cleared by useInteractiveCore effect)
                setIsChunked(false);
                setChunkTotal(0);
                setChunkCurrent(0);

                // Set the new audio URL
                setAudioUrl(url);

                // Update current page index
                setCurrentIdx(() => targetIdx);

                // Trigger autoplay
                pendingAutoplayRef.current = true;

                // Re-enable buttons after successful navigation
                setIsNavigating(false);

                break; // Success - exit loop
              } else {
                logger.error('[usePageNavigation] No URL in response', data);
                break;
              }
            } else {
              logger.error(
                '[usePageNavigation] Failed to fetch page audio',
                data,
              );
              break;
            }
          } catch (err) {
            logger.error('[usePageNavigation] Error fetching page audio', err);
            break;
          }

          attempts++;
        }

        if (attempts >= maxAttempts) {
          logger.error('[usePageNavigation] Max attempts reached');
        }
      } finally {
        navigatingRef.current = false;
        // Reset navigation state in case of error or boundary reached
        setIsNavigating(false);
      }
    },
    [
      currentIdx,
      pageOrder,
      anonHeaders,
      requestedRef,
      setAudioUrl,
      setIsChunked,
      setChunkTotal,
      setChunkCurrent,
      setCurrentIdx,
      pendingAutoplayRef,
    ],
  );

  // Navigation handlers
  const goToPreviousPage = React.useCallback(() => {
    logger.log('[usePageNavigation] goToPreviousPage called', {
      currentIdx,
      canGoToPrevious: currentIdx > 0,
    });
    navigateToPage('previous');
  }, [navigateToPage, currentIdx]);

  const goToNextPage = React.useCallback(() => {
    logger.log('[usePageNavigation] goToNextPage called', {
      currentIdx,
      canGoToNext: currentIdx < pageOrder.length - 1,
    });
    navigateToPage('next');
  }, [navigateToPage, currentIdx, pageOrder.length]);

  // Can navigate flags
  const canGoToPrevious = currentIdx > 0;
  const canGoToNext = currentIdx < pageOrder.length - 1;

  return {
    goToPreviousPage,
    goToNextPage,
    canGoToPrevious,
    canGoToNext,
    isNavigating,
  };
}
