'use client';
import React from 'react';

export interface AudioControlsState {
  isPlaying: boolean;
  isAudioReady: boolean;
  duration: number;
  currentTime: number;
  audioEnded: boolean;
}

export interface AudioControlsHandlers {
  togglePlay: () => void;
  seekBy: (delta: number) => void;
  onScrubStart: () => void;
  onScrub: (val: number) => void;
  onScrubEnd: (val: number) => void;
}

/**
 * Simplified audio playback controls hook.
 * Phase 1: No usage gating, no speed control, no resume position.
 */
export function useAudioControls(
  audioRef: React.MutableRefObject<HTMLAudioElement | null>,
  audioUrl: string | null,
): AudioControlsState & AudioControlsHandlers {
  const [isPlaying, setIsPlaying] = React.useState<boolean>(false);
  const [isAudioReady, setIsAudioReady] = React.useState<boolean>(false);
  const [duration, setDuration] = React.useState<number>(0);
  const [currentTime, setCurrentTime] = React.useState<number>(0);
  const [isScrubbing, setIsScrubbing] = React.useState<boolean>(false);
  const [audioEnded, setAudioEnded] = React.useState<boolean>(false);

  // Reset audioEnded when audio URL changes (new chunk/page loaded)
  React.useEffect(() => {
    setAudioEnded(false);
  }, [audioUrl]);

  // Reset audio ready state when URL changes
  React.useEffect(() => {
    setIsAudioReady(false);
  }, [audioUrl]);

  // Audio element event listeners
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;

    const onLoaded = () => {
      setDuration(el.duration || 0);
      setCurrentTime(el.currentTime || 0);
    };

    const onCanPlay = () => {
      setIsAudioReady(true);
    };

    const onPlay = () => {
      setIsPlaying(true);
    };

    const onPause = () => {
      setIsPlaying(false);
    };

    const onEnded = () => {
      setAudioEnded(true);
    };

    const onTimeUpdate = () => {
      if (!isScrubbing) {
        setCurrentTime(el.currentTime || 0);
      }
    };

    el.addEventListener('loadedmetadata', onLoaded);
    el.addEventListener('canplay', onCanPlay);
    el.addEventListener('play', onPlay);
    el.addEventListener('pause', onPause);
    el.addEventListener('ended', onEnded);
    el.addEventListener('timeupdate', onTimeUpdate);

    return () => {
      el.removeEventListener('loadedmetadata', onLoaded);
      el.removeEventListener('canplay', onCanPlay);
      el.removeEventListener('play', onPlay);
      el.removeEventListener('pause', onPause);
      el.removeEventListener('ended', onEnded);
      el.removeEventListener('timeupdate', onTimeUpdate);
    };
  }, [audioUrl, isScrubbing]);

  const togglePlay = React.useCallback(() => {
    const el = audioRef.current;
    if (!el) return;
    if (el.paused) {
      el.play().catch(() => {});
    } else {
      el.pause();
    }
  }, [audioRef]);

  const seekBy = React.useCallback(
    (delta: number) => {
      const el = audioRef.current;
      if (!el) return;
      const d = el.duration;
      const hasFinite = isFinite(d) && d > 0;
      const nextT = el.currentTime + delta;
      el.currentTime = hasFinite
        ? Math.max(0, Math.min(nextT, d))
        : Math.max(0, nextT);
    },
    [audioRef],
  );

  const onScrubStart = React.useCallback(() => {
    setIsScrubbing(true);
  }, []);

  const onScrub = React.useCallback((val: number) => {
    setCurrentTime(val);
  }, []);

  const onScrubEnd = React.useCallback(
    (val: number) => {
      setIsScrubbing(false);
      const el = audioRef.current;
      if (!el) return;
      try {
        el.currentTime = val;
      } catch {}
    },
    [audioRef],
  );

  return {
    isPlaying,
    isAudioReady,
    duration,
    currentTime,
    audioEnded,
    togglePlay,
    seekBy,
    onScrubStart,
    onScrub,
    onScrubEnd,
  };
}
