'use client';
import React from 'react';
import { startChunkPolling } from '../utils/chunkPolling';

/**
 * Manages chunk polling lifecycle for chunked audio playback.
 * Polls for next chunks when playing or after audio ends.
 */
export function useChunkPolling(
  isChunked: boolean,
  isPlaying: boolean,
  chunkTotal: number,
  chunkCurrent: number,
  chunkNextUrl: string | null,
  currentPageId: string | undefined,
  audioEnded: boolean,
  anonHeaders: Record<string, string>,
  chunkCurrentRef: React.MutableRefObject<number>,
  chunkPollCleanupRef: React.MutableRefObject<(() => void) | null>,
  knownChunksRef: React.MutableRefObject<Map<number, string>>,
  requestedChunksRef: React.MutableRefObject<Set<number>>,
  chunkEndedNeedingNextRef: React.MutableRefObject<boolean>,
  nextPreloadAudioRef: React.MutableRefObject<HTMLAudioElement | null>,
  setChunkNextUrl: (url: string | null) => void,
): void {
  // Keep a ref in sync with latest chunkCurrent for use in polling callback closures
  React.useEffect(() => {
    chunkCurrentRef.current = chunkCurrent;
  }, [chunkCurrent, chunkCurrentRef]);

  // Manage chunk polling lifecycle: poll while playing OR after an end, as long as more chunks are needed
  React.useEffect(() => {
    // Cleanup helper
    const stop = () => {
      if (chunkPollCleanupRef.current) {
        try {
          chunkPollCleanupRef.current();
        } catch {}
        chunkPollCleanupRef.current = null;
      }
    };

    const currentChunk = chunkCurrentRef.current || 1;
    const nextNeededChunk = currentChunk + 1;

    // Check if we already have the next chunk or if it's being requested
    const hasNextChunk = knownChunksRef.current.has(nextNeededChunk);
    const isRequestingNextChunk =
      requestedChunksRef.current.has(nextNeededChunk);

    // Check if we need the next chunk (regardless of whether it's cached)
    const needNextChunk =
      isChunked && chunkTotal > 0 && currentChunk < chunkTotal && !chunkNextUrl; // We need next chunk if we don't have the URL set yet

    // Track if chunk ended and we're waiting for next chunk
    if (audioEnded && needNextChunk && !chunkNextUrl) {
      chunkEndedNeedingNextRef.current = true;
    } else if (chunkNextUrl || !needNextChunk) {
      chunkEndedNeedingNextRef.current = false;
    }

    // If we don't need next chunk, stop and exit
    if (!needNextChunk || !currentPageId) {
      stop();
      return;
    }

    // If we already have the next chunk cached, use it immediately
    if (hasNextChunk) {
      const url = knownChunksRef.current.get(nextNeededChunk);
      if (url) {
        setChunkNextUrl(url);
        try {
          if (!nextPreloadAudioRef.current)
            nextPreloadAudioRef.current = new Audio();
          nextPreloadAudioRef.current.src = url;
          nextPreloadAudioRef.current.preload = 'auto';
          nextPreloadAudioRef.current.load();
        } catch {}
      }
      stop();
      return;
    }

    // Don't poll if:
    // 1. Already requesting this chunk
    // 2. Not playing and not ended (lazy loading - only poll when needed)
    const shouldPoll =
      !isRequestingNextChunk &&
      (isPlaying || audioEnded || chunkEndedNeedingNextRef.current);

    if (!shouldPoll) {
      stop();
      return;
    }

    // Start polling for the next chunk
    if (!chunkPollCleanupRef.current) {
      const pid = currentPageId;
      // Mark this chunk as being requested
      requestedChunksRef.current.add(nextNeededChunk);
      chunkPollCleanupRef.current = startChunkPolling(
        pid,
        anonHeaders,
        () => nextNeededChunk,
        (url, idx) => {
          // Store the discovered chunk and remove from requested set
          if (idx) {
            knownChunksRef.current.set(idx, url);
            requestedChunksRef.current.delete(idx);
          }
          setChunkNextUrl(url);
          try {
            if (!nextPreloadAudioRef.current)
              nextPreloadAudioRef.current = new Audio();
            nextPreloadAudioRef.current.src = url;
            nextPreloadAudioRef.current.preload = 'auto';
            nextPreloadAudioRef.current.load();
          } catch {}
        },
        () => {
          // Cleanup callback: remove from requested set if polling stopped/failed
          requestedChunksRef.current.delete(nextNeededChunk);
        },
      );
    }
    return () => {
      stop();
    };
  }, [
    isChunked,
    isPlaying,
    chunkTotal,
    chunkCurrent,
    chunkNextUrl,
    currentPageId,
    anonHeaders,
    audioEnded,
    chunkCurrentRef,
    chunkPollCleanupRef,
    knownChunksRef,
    requestedChunksRef,
    chunkEndedNeedingNextRef,
    nextPreloadAudioRef,
    setChunkNextUrl,
  ]);
}
