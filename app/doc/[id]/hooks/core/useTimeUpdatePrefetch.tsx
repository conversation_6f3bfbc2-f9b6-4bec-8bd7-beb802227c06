import React from 'react';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';
import {
  ELAPSED_FALLBACK_SEC,
  MIN_REMAIN_SEC,
  PREFETCH_PERCENT,
} from '../utils/constants';

type PageRef = { id: string; number: number; status: string };

export function useTimeUpdatePrefetch(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  isChunked: boolean,
  next: PageRef | undefined,
  nextUrl: string | null,
  requestedRef: React.MutableRefObject<Set<string>>,
  anonHeaders: Record<string, string>,
  setNextUrl: (u: string) => void,
) {
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    const triggerPrefetch = () => {
      if (isChunked) return;
      if (!next || nextUrl) return;
      const start = next; // start from next page only here
      const tryEnsure = async (p?: PageRef) => {
        if (!p) return;
        // Guard against duplicate requests - critical to prevent request flood
        if (requestedRef.current.has(p.id)) return;
        requestedRef.current.add(p.id);
        try {
          const headers = getAuthHeaders({ ...anonHeaders }) as Record<
            string,
            string
          >;
          const sid = headers['x-anon-id'];
          let sids: string[] = [];
          try {
            const raw = headers['x-anon-alt-ids'];
            if (raw) {
              if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
              else sids = raw.split(',').map((x) => x.trim());
            }
          } catch {}
          if (sid) sids = [sid, ...sids];
          if (sids.length === 0) {
            try {
              const cand2 = await getAnonCandidates();
              if (cand2) {
                sids = Array.from(
                  new Set(
                    [cand2.stableId, cand2.fpjsId, cand2.coarseId].filter(
                      Boolean,
                    ) as string[],
                  ),
                );
              }
            } catch {}
          }
          const qs = sids.length
            ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
            : '';
          const jr = await fetch(`/api/page/${p.id}/ensure-tts${qs}`, {
            method: 'POST',
            headers,
          });
          const j = await jr.json();
          // Handle multiple response formats:
          // - Streaming mode: j.streamUrl
          // - Complete audio: j.url
          // - Chunked audio: j.urls[0] (first chunk URL)
          if (j?.ok) {
            if (j.skip || j.skippable) {
              // no recursion chain here; prefetch will handle in onPlay again
            } else {
              const url = j.streamUrl || j.url || (j.urls && j.urls[0]);
              if (url) setNextUrl(url);
            }
          }
        } catch {}
      };
      tryEnsure(start);
    };
    const onTimeUpdate = () => {
      if (!next || nextUrl) return;
      const d = el.duration;
      if (!isFinite(d) || d <= 0) {
        if (el.currentTime >= ELAPSED_FALLBACK_SEC) triggerPrefetch();
        return;
      }
      const t = el.currentTime;
      const percent = t / d;
      const remaining = d - t;
      if (percent >= PREFETCH_PERCENT || remaining <= MIN_REMAIN_SEC) {
        triggerPrefetch();
      }
    };
    let timer: ReturnType<typeof setTimeout> | null = null;
    if (isFinite(el.duration) && el.duration > 0 && next && !nextUrl) {
      const ms = Math.max(
        0,
        (el.duration * PREFETCH_PERCENT - el.currentTime) * 1000,
      );
      timer = setTimeout(triggerPrefetch, ms);
    }
    el.addEventListener('timeupdate', onTimeUpdate);
    return () => {
      el.removeEventListener('timeupdate', onTimeUpdate);
      if (timer) clearTimeout(timer);
    };
  }, [
    audioRef,
    isChunked,
    next,
    nextUrl,
    anonHeaders,
    requestedRef,
    setNextUrl,
  ]);
}
