'use client';
import React from 'react';

/**
 * Progress bar hook that manages processing progress animation.
 * Auto-increments from 0-95%, then jumps to 100% when ready.
 *
 * @param processing - Whether still processing
 * @param audioUrl - Audio URL (triggers completion when available)
 * @param setProcessing - Setter for processing state
 * @returns processingProgress - Current progress (0-100)
 */
export function useProgressBar(
  processing: boolean,
  audioUrl: string | null,
  setProcessing: (val: boolean) => void,
): number {
  const [processingProgress, setProcessingProgress] = React.useState<number>(0);
  const completingRef = React.useRef<boolean>(false);

  // Auto-increment progress bar smoothly from 0-95%
  // Don't reach 100% until audio is actually ready
  React.useEffect(() => {
    if (!processing) return;

    const interval = setInterval(() => {
      setProcessingProgress((prev) => {
        // Cap at 95% while still processing
        // Will jump to 100% when audio loads (handled separately)
        if (prev >= 95) return 95;

        // Increment slower as we get closer to 95%
        const increment = prev < 60 ? 1 : prev < 80 ? 0.5 : 0.2;
        return Math.min(prev + increment, 95);
      });
    }, 200);

    return () => clearInterval(interval);
  }, [processing]);

  // Jump to 100% when audio is ready, then hide progress bar
  React.useEffect(() => {
    if (audioUrl && processing && !completingRef.current) {
      completingRef.current = true;

      // Jump progress to 100%
      setProcessingProgress(100);

      // Wait a moment then show player
      const timer = setTimeout(() => {
        setProcessing(false);
        completingRef.current = false; // Reset for next page
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [audioUrl, processing, setProcessing]);

  return processingProgress;
}
