import React from 'react';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';
import { appConfig } from '@/lib/config';
import { logger } from '@/lib/logger';

type PageRef = { id: string; number: number; status: string };

/**
 * Proactively triggers translation + TTS processing for the next page
 * when current page audio remaining duration falls below a threshold.
 *
 * This is different from prefetching - it triggers actual processing
 * (translation + TTS) rather than just fetching already-processed audio.
 *
 * Works for both chunked and non-chunked audio modes.
 */
export function useProactiveNextPageProcessing(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  isChunked: boolean,
  next: PageRef | undefined,
  anonHeaders: Record<string, string>,
  processingTriggeredRef: React.MutableRefObject<Set<string>>,
) {
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;

    const triggerProcessing = async () => {
      if (!next) return;

      // Guard against duplicate processing requests
      if (processingTriggeredRef.current.has(next.id)) {
        return;
      }
      processingTriggeredRef.current.add(next.id);

      logger.log('[proactive-processing] Triggering next page processing', {
        pageId: next.id,
        pageNumber: next.number,
      });

      try {
        const headers = getAuthHeaders({ ...anonHeaders }) as Record<
          string,
          string
        >;
        const sid = headers['x-anon-id'];
        let sids: string[] = [];
        try {
          const raw = headers['x-anon-alt-ids'];
          if (raw) {
            if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
            else sids = raw.split(',').map((x) => x.trim());
          }
        } catch {}
        if (sid) sids = [sid, ...sids];
        if (sids.length === 0) {
          try {
            const cand2 = await getAnonCandidates();
            if (cand2) {
              sids = Array.from(
                new Set(
                  [cand2.stableId, cand2.fpjsId, cand2.coarseId].filter(
                    Boolean,
                  ) as string[],
                ),
              );
            }
          } catch {}
        }
        const qs = sids.length
          ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
          : '';

        // Trigger ensure-tts which will start translation + TTS if not already processed
        const response = await fetch(`/api/page/${next.id}/ensure-tts${qs}`, {
          method: 'POST',
          headers,
        });
        const result = await response.json();

        if (result?.ok) {
          logger.log(
            '[proactive-processing] Next page processing triggered successfully',
            {
              pageId: next.id,
              pageNumber: next.number,
            },
          );
        } else if (result?.retry) {
          logger.log(
            '[proactive-processing] Next page processing in progress',
            {
              pageId: next.id,
              pageNumber: next.number,
              stage: result.processingStage,
            },
          );
        }
      } catch (error) {
        logger.error('[proactive-processing] Failed to trigger processing', {
          pageId: next.id,
          pageNumber: next.number,
          error,
        });
        // Remove from triggered set so we can retry
        processingTriggeredRef.current.delete(next.id);
      }
    };

    const onTimeUpdate = () => {
      if (!next) return;
      if (processingTriggeredRef.current.has(next.id)) return;

      const d = el.duration;
      if (!isFinite(d) || d <= 0) return;

      const t = el.currentTime;
      const remaining = d - t;

      // Trigger processing when remaining time falls below threshold
      if (
        remaining <= appConfig.nextPageProcessThresholdSeconds &&
        remaining > 0
      ) {
        triggerProcessing();
      }
    };

    el.addEventListener('timeupdate', onTimeUpdate);
    return () => {
      el.removeEventListener('timeupdate', onTimeUpdate);
    };
  }, [audioRef, isChunked, next, anonHeaders, processingTriggeredRef]);
}
