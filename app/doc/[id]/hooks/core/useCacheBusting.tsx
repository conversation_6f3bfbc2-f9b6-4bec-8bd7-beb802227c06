'use client';
import React from 'react';

/**
 * Busts audio cache after sign-in to avoid anonymous partial cache.
 * Reloads audio with cache-busting query params and restores playback state.
 */
export function useCacheBusting(
  isSignedIn: boolean,
  audioUrl: string | null,
  nextUrl: string | null,
  audioRef: React.MutableRefObject<HTMLAudioElement | null>,
  nextPreloadAudioRef: React.MutableRefObject<HTMLAudioElement | null>,
  resumeLocalSecRef: React.MutableRefObject<number | null>,
  setAudioUrl: (url: string) => void,
  setNextUrl: (url: string) => void,
): void {
  React.useEffect(() => {
    if (!isSignedIn) return;
    const el = audioRef.current;
    const url = audioUrl || '';
    if (!el || !url) return;
    if (url.startsWith('blob:')) return;
    const needsBust = url.includes('/api/audio/');
    if (!needsBust) return;
    const wasPlaying = !el.paused && !el.ended;
    const t = isFinite(el.currentTime) ? el.currentTime : 0;
    const busted = url + (url.includes('?') ? '&' : '?') + 'rt=' + Date.now();
    if (nextUrl && nextUrl.includes('/api/audio/')) {
      const bustedNext =
        nextUrl + (nextUrl.includes('?') ? '&' : '?') + 'rt=' + Date.now();
      setNextUrl(bustedNext);
      try {
        if (!nextPreloadAudioRef.current)
          nextPreloadAudioRef.current = new Audio();
        nextPreloadAudioRef.current.src = bustedNext;
        nextPreloadAudioRef.current.preload = 'auto';
        nextPreloadAudioRef.current.load();
      } catch {}
    }
    setAudioUrl(busted);
    const onLoaded = () => {
      // If we have a pending resume position, prefer that over the pre-bust time
      const resumeSec = resumeLocalSecRef.current;
      try {
        el.currentTime =
          resumeSec != null && isFinite(resumeSec) ? resumeSec : t;
      } catch {}
      if (wasPlaying) {
        try {
          el.play();
        } catch {}
      }
    };
    el.addEventListener('loadedmetadata', onLoaded, { once: true });
    return () => {
      el.removeEventListener('loadedmetadata', onLoaded);
    };
  }, [
    isSignedIn,
    audioUrl,
    nextUrl,
    audioRef,
    nextPreloadAudioRef,
    resumeLocalSecRef,
    setAudioUrl,
    setNextUrl,
  ]);
}
