'use client';
import React from 'react';
import {
  FREE_TRIAL_SECONDS,
  SIGNED_IN_FREE_SECONDS,
  ENABLE_SIGNED_IN_GATE,
} from '../utils/constants';

export interface AudioControlsState {
  isPlaying: boolean;
  isAudioReady: boolean;
  duration: number;
  currentTime: number;
  playbackRate: number;
  isScrubbing: boolean;
  audioEnded: boolean;
}

export interface AudioControlsHandlers {
  togglePlay: () => void;
  seekBy: (delta: number) => void;
  onScrubStart: () => void;
  onScrub: (val: number) => void;
  onScrubEnd: (val: number) => void;
  changeSpeed: () => void;
}

/**
 * Manages audio playback controls and state.
 * Handles play/pause, seeking, scrubbing, and playback speed.
 */
export function useAudioControls(
  audioRef: React.MutableRefObject<HTMLAudioElement | null>,
  audioUrl: string | null,
  isSignedIn: boolean,
  requireLogin: boolean,
  enforceGate: (el: HTMLAudioElement) => boolean,
  enforceSignedGate: (el: HTMLAudioElement) => boolean,
  resetUpgradeDismissal: () => void,
  trialBaseOffsetRef: React.MutableRefObject<number>,
  signedBaseOffsetRef: React.MutableRefObject<number>,
  resumeLocalSecRef: React.MutableRefObject<number | null>,
): AudioControlsState & AudioControlsHandlers {
  const [isPlaying, setIsPlaying] = React.useState<boolean>(false);
  const [isAudioReady, setIsAudioReady] = React.useState<boolean>(false);
  const [duration, setDuration] = React.useState<number>(0);
  const [currentTime, setCurrentTime] = React.useState<number>(0);
  const [playbackRate, setPlaybackRate] = React.useState<number>(1.0);
  const [isScrubbing, setIsScrubbing] = React.useState<boolean>(false);
  const [audioEnded, setAudioEnded] = React.useState<boolean>(false);

  // Reset audioEnded when audio URL changes (new chunk/page loaded)
  React.useEffect(() => {
    setAudioEnded(false);
  }, [audioUrl]);

  // Reset audio ready state when URL changes
  React.useEffect(() => {
    setIsAudioReady(false);
  }, [audioUrl]);

  // Audio element event listeners
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;

    const onLoaded = () => {
      try {
        el.playbackRate = playbackRate;
      } catch {}
      setDuration(el.duration || 0);
      setCurrentTime(el.currentTime || 0);
      const sec = resumeLocalSecRef.current;
      if (sec != null && isFinite(sec)) {
        try {
          el.currentTime = sec;
        } catch {}
        resumeLocalSecRef.current = null;
      }
      if (!isSignedIn) enforceGate(el);
      else if (ENABLE_SIGNED_IN_GATE) enforceSignedGate(el);
    };
    const onCanPlay = () => {
      setIsAudioReady(true);
    };
    const onPlay = () => {
      setIsPlaying(true);
      if (!isSignedIn) enforceGate(el);
      else if (ENABLE_SIGNED_IN_GATE) enforceSignedGate(el);
    };
    const onPause = () => setIsPlaying(false);
    const onEnded = () => setAudioEnded(true);
    const onTimeUpdate = () => {
      if (!isScrubbing) setCurrentTime(el.currentTime || 0);
      if (!isSignedIn) enforceGate(el);
      else if (ENABLE_SIGNED_IN_GATE) enforceSignedGate(el);
    };

    el.addEventListener('loadedmetadata', onLoaded);
    el.addEventListener('canplay', onCanPlay);
    el.addEventListener('play', onPlay);
    el.addEventListener('pause', onPause);
    el.addEventListener('ended', onEnded);
    el.addEventListener('timeupdate', onTimeUpdate);

    return () => {
      el.removeEventListener('loadedmetadata', onLoaded);
      el.removeEventListener('canplay', onCanPlay);
      el.removeEventListener('play', onPlay);
      el.removeEventListener('pause', onPause);
      el.removeEventListener('ended', onEnded);
      el.removeEventListener('timeupdate', onTimeUpdate);
    };
  }, [
    audioUrl,
    playbackRate,
    isScrubbing,
    enforceGate,
    enforceSignedGate,
    isSignedIn,
    resumeLocalSecRef,
  ]);

  const togglePlay = React.useCallback(() => {
    const el = audioRef.current;
    if (!el) return;
    if (!isSignedIn && (requireLogin || enforceGate(el))) return;
    // For signed-in gating, ensure dismissal is cleared before next play
    if (isSignedIn) resetUpgradeDismissal();
    if (el.paused) {
      el.play().catch(() => {});
    } else {
      el.pause();
    }
  }, [isSignedIn, requireLogin, enforceGate, resetUpgradeDismissal]);

  const seekBy = React.useCallback(
    (delta: number) => {
      const el = audioRef.current;
      if (!el) return;
      const d = el.duration;
      const hasFinite = isFinite(d) && d > 0;
      let nextT = el.currentTime + delta;
      const allowedLocalMax = !isSignedIn
        ? Math.max(0, FREE_TRIAL_SECONDS - trialBaseOffsetRef.current)
        : ENABLE_SIGNED_IN_GATE
          ? Math.max(0, SIGNED_IN_FREE_SECONDS - signedBaseOffsetRef.current)
          : Number.POSITIVE_INFINITY;
      if (isFinite(allowedLocalMax)) nextT = Math.min(nextT, allowedLocalMax);
      el.currentTime = hasFinite
        ? Math.max(0, Math.min(nextT, d))
        : Math.max(0, nextT);
      if (!isSignedIn) enforceGate(el);
      else enforceSignedGate(el);
    },
    [
      isSignedIn,
      enforceGate,
      enforceSignedGate,
      trialBaseOffsetRef,
      signedBaseOffsetRef,
    ],
  );

  const onScrubStart = React.useCallback(() => setIsScrubbing(true), []);

  const onScrub = React.useCallback(
    (val: number) => {
      let v = val;
      const allowedLocalMax = !isSignedIn
        ? Math.max(0, FREE_TRIAL_SECONDS - trialBaseOffsetRef.current)
        : ENABLE_SIGNED_IN_GATE
          ? Math.max(0, SIGNED_IN_FREE_SECONDS - signedBaseOffsetRef.current)
          : Number.POSITIVE_INFINITY;
      if (isFinite(allowedLocalMax)) v = Math.min(v, allowedLocalMax);
      setCurrentTime(v);
    },
    [isSignedIn, trialBaseOffsetRef, signedBaseOffsetRef],
  );

  const onScrubEnd = React.useCallback(
    (val: number) => {
      setIsScrubbing(false);
      const el = audioRef.current;
      if (!el) return;
      let target = val;
      const allowedLocalMax = !isSignedIn
        ? Math.max(0, FREE_TRIAL_SECONDS - trialBaseOffsetRef.current)
        : ENABLE_SIGNED_IN_GATE
          ? Math.max(0, SIGNED_IN_FREE_SECONDS - signedBaseOffsetRef.current)
          : Number.POSITIVE_INFINITY;
      if (isFinite(allowedLocalMax)) target = Math.min(target, allowedLocalMax);
      try {
        el.currentTime = target;
      } catch {}
      if (!isSignedIn) enforceGate(el);
      else enforceSignedGate(el);
    },
    [
      isSignedIn,
      enforceGate,
      enforceSignedGate,
      trialBaseOffsetRef,
      signedBaseOffsetRef,
    ],
  );

  const changeSpeed = React.useCallback(() => {
    const speeds = [0.75, 1.0, 1.25, 1.5, 1.75, 2.0];
    const idx = speeds.indexOf(playbackRate);
    const nextSp = speeds[(idx + 1) % speeds.length];
    setPlaybackRate(nextSp);
    const el = audioRef.current;
    if (el) {
      try {
        el.playbackRate = nextSp;
      } catch {}
    }
  }, [playbackRate]);

  return {
    isPlaying,
    isAudioReady,
    duration,
    currentTime,
    playbackRate,
    isScrubbing,
    audioEnded,
    togglePlay,
    seekBy,
    onScrubStart,
    onScrub,
    onScrubEnd,
    changeSpeed,
  };
}
