import React from 'react';
import { FREE_TRIAL_SECONDS } from '../utils/constants';

export function useAnonFreeTrialGate(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  isSignedIn: boolean,
  trialBaseOffsetRef: React.MutableRefObject<number>,
  audioUrl?: string | null,
) {
  const [requireLogin, setRequireLogin] = React.useState(false);
  const [trialDisplay, setTrialDisplay] = React.useState(0);

  // We gate by absolute content position: baseOffset (prior pages) + currentTime of current audio.
  const rafGateRef = React.useRef<number | null>(null);
  const gateTimeoutRef = React.useRef<number | null>(null);

  const updateDisplayAndGate = React.useCallback(
    (el: HTMLAudioElement | null) => {
      if (!el) return false;
      if (isSignedIn) return false;
      const used = Math.max(
        0,
        trialBaseOffsetRef.current + (el.currentTime || 0),
      );
      const clamped = Math.min(used, FREE_TRIAL_SECONDS);
      setTrialDisplay(clamped);
      try {
        sessionStorage.setItem(
          'transread_free_trial_elapsed_sec',
          String(Math.floor(clamped)),
        );
      } catch {}
      if (used >= FREE_TRIAL_SECONDS) {
        setRequireLogin(true);
        try {
          el.pause();
        } catch {}
        return true;
      }
      return false;
    },
    [isSignedIn, trialBaseOffsetRef],
  );

  const enforceGate = React.useCallback(
    (el: HTMLAudioElement | null) => {
      if (!el) return false;
      if (isSignedIn) return false;
      const allowedLocalMax = Math.max(
        0,
        FREE_TRIAL_SECONDS - trialBaseOffsetRef.current,
      );
      const eps = 0.02;
      if (el.currentTime >= allowedLocalMax - eps) {
        try {
          el.currentTime = allowedLocalMax;
        } catch {}
        setRequireLogin(true);
        try {
          el.pause();
        } catch {}
        return true;
      }
      return false;
    },
    [isSignedIn, trialBaseOffsetRef],
  );

  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    const onPlay = () => {
      if (isSignedIn) return;
      if (updateDisplayAndGate(el)) return;
      if (gateTimeoutRef.current != null) {
        try {
          clearTimeout(gateTimeoutRef.current);
        } catch {}
        gateTimeoutRef.current = null;
      }
      const allowedLocalMax = Math.max(
        0,
        FREE_TRIAL_SECONDS - trialBaseOffsetRef.current,
      );
      const msLeft = Math.max(0, (allowedLocalMax - el.currentTime) * 1000);
      if (msLeft <= 0) {
        enforceGate(el);
      } else {
        gateTimeoutRef.current = window.setTimeout(
          () => enforceGate(audioRef.current),
          Math.min(msLeft, 60_000),
        );
      }
      if (rafGateRef.current == null) {
        const tick = () => {
          const node = audioRef.current;
          if (!node) return;
          if (isSignedIn) return;
          updateDisplayAndGate(node);
          if (enforceGate(node)) {
            if (rafGateRef.current) cancelAnimationFrame(rafGateRef.current);
            rafGateRef.current = null;
            return;
          }
          rafGateRef.current = requestAnimationFrame(tick);
        };
        rafGateRef.current = requestAnimationFrame(tick);
      }
    };
    const onTimeUpdate = () => {
      if (isSignedIn) return;
      const el2 = audioRef.current;
      if (updateDisplayAndGate(el2 || null)) return;
      enforceGate(el2 || null);
    };
    const onPauseOrEnded = () => {
      if (isSignedIn) return;
      updateDisplayAndGate(audioRef.current);
      if (gateTimeoutRef.current != null) {
        try {
          clearTimeout(gateTimeoutRef.current);
        } catch {}
        gateTimeoutRef.current = null;
      }
      if (rafGateRef.current != null) {
        try {
          cancelAnimationFrame(rafGateRef.current);
        } catch {}
        rafGateRef.current = null;
      }
    };
    el.addEventListener('play', onPlay);
    el.addEventListener('timeupdate', onTimeUpdate);
    el.addEventListener('pause', onPauseOrEnded);
    el.addEventListener('ended', onPauseOrEnded);
    return () => {
      el.removeEventListener('play', onPlay);
      el.removeEventListener('timeupdate', onTimeUpdate);
      el.removeEventListener('pause', onPauseOrEnded);
      el.removeEventListener('ended', onPauseOrEnded);
      if (gateTimeoutRef.current != null) {
        try {
          clearTimeout(gateTimeoutRef.current);
        } catch {}
        gateTimeoutRef.current = null;
      }
      if (rafGateRef.current != null) {
        try {
          cancelAnimationFrame(rafGateRef.current);
        } catch {}
        rafGateRef.current = null;
      }
    };
    // trialBaseOffsetRef is a ref and doesn't need to be in the dependency array
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioRef, isSignedIn, enforceGate, updateDisplayAndGate, audioUrl]);

  React.useEffect(() => {
    if (isSignedIn) return;
    try {
      localStorage.removeItem('transread_free_trial_elapsed_sec');
    } catch {}
    try {
      sessionStorage.removeItem('transread_free_trial_elapsed_sec');
    } catch {}
    setTrialDisplay(0);
    setRequireLogin(false);
  }, [isSignedIn]);

  const triggerGate = React.useCallback(() => {
    setRequireLogin(true);
    const el = audioRef.current;
    if (el) {
      try {
        el.pause();
      } catch {}
    }
  }, [audioRef]);

  return {
    requireLogin,
    setRequireLogin,
    trialDisplay,
    enforceGate,
    triggerGate,
  } as const;
}
