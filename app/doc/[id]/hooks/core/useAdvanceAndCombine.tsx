import React from 'react';
import { combineTwoUrlsToWav } from '../utils/audioCombine';
import { logger } from '@/lib/logger';

type PageRef = { id: string; number: number; status: string };

export function useAdvanceAndCombine(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  audioUrl: string | null,
  next: PageRef | undefined,
  nextUrl: string | null,
  isChunked: boolean,
  chunkNextUrl: string | null,
  audioEnded: boolean,
  setAudioUrl: (u: string) => void,
  setNextUrl: (u: string | null) => void,
  setCurrentIdx: (fn: (i: number) => number) => void,
  setIsCombined: (b: boolean) => void,
  setSegmentBoundarySec: (n: number | null) => void,
  combinedBaseIdxRef: React.MutableRefObject<number | null>,
  lastObjectUrlRef: React.MutableRefObject<string | null>,
  currentIdx: number,
  isSignedIn: boolean,
  enforceGate: (el: HTMLAudioElement | null) => boolean,
  enforceSignedGate: (el: HTMLAudioElement | null) => boolean,
  trialBaseOffsetRef: React.MutableRefObject<number>,
  signedBaseOffsetRef: React.MutableRefObject<number>,
  setChunkCurrent: (fn: (i: number) => number) => void,
  setChunkNextUrl: (u: string | null) => void,
  pendingAutoplayRef: React.MutableRefObject<boolean>,
) {
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    const onEnded = () => {
      if (isChunked) {
        // Check if we have a pre-combined audio ready (from background combination)
        const combinedUrl = combinedAudioRef.current;
        const boundary = combinedBoundaryRef.current;
        if (combinedUrl) {
          // Combined audio is ready! Switch to it for accumulated seeking
          logger.log('[audio] Using pre-combined audio, boundary:', boundary);
          try {
            if (lastObjectUrlRef.current) {
              URL.revokeObjectURL(lastObjectUrlRef.current);
            }
          } catch {}

          lastObjectUrlRef.current = combinedUrl;
          accumulatedSrcRef.current = combinedUrl;
          setAudioUrl(combinedUrl);
          setChunkCurrent((c) => c + 1);
          setChunkNextUrl(null);
          combinedAudioRef.current = null;

          // Resume from the boundary where chunk 2 starts
          pendingAutoplayRef.current = true;
          const onLoadedMetadata = () => {
            try {
              el.currentTime = boundary;
              if (!isSignedIn) enforceGate(el);
              else enforceSignedGate(el);
            } catch {}
          };
          el.addEventListener('loadedmetadata', onLoadedMetadata, {
            once: true,
          });
          return;
        } else if (chunkNextUrl) {
          // Combination not ready yet, fall back to simple chunk switching
          logger.log(
            '[audio] Combined audio not ready, switching to next chunk directly',
          );
          setAudioUrl(chunkNextUrl);
          setChunkCurrent((c) => c + 1);
          setChunkNextUrl(null);

          // Auto-play when ready
          pendingAutoplayRef.current = true;
          return;
        }
      }
      if (next) {
        if (nextUrl) {
          logger.log('[audio] Page ended, switching to next page immediately', {
            nextPage: next?.number,
          });

          if (isFinite(el.duration) && el.duration > 0) {
            if (!isSignedIn) trialBaseOffsetRef.current += el.duration;
            else signedBaseOffsetRef.current += el.duration;
          }

          // Immediately switch to next page and play
          setAudioUrl(nextUrl);
          setNextUrl(null);
          setCurrentIdx((i) => i + 1);

          // Auto-play when ready
          pendingAutoplayRef.current = true;

          // Start background combination for seeking (non-blocking)
          const prevSrc = el.currentSrc || audioUrl || '';
          if (prevSrc) {
            combineTwoUrlsToWav(prevSrc, nextUrl)
              .then((combo) => {
                if (combo && !el.paused) {
                  logger.log(
                    '[audio] Background combination ready, will use for seeking',
                  );
                  // Store for future use, but don't interrupt playback
                  try {
                    if (lastObjectUrlRef.current)
                      URL.revokeObjectURL(lastObjectUrlRef.current);
                  } catch {}
                  lastObjectUrlRef.current = combo.url;
                  // Don't switch to combined audio mid-playback
                }
              })
              .catch((err) => {
                logger.error('[audio] Background combination failed:', err);
              });
          }
        }
      }
    };
    el.addEventListener('ended', onEnded);
    return () => {
      el.removeEventListener('ended', onEnded);
    };
    // Note: combinedAudioRef and combinedBoundaryRef are refs and don't need to be in deps
  }, [
    audioRef,
    audioUrl,
    next,
    nextUrl,
    isChunked,
    chunkNextUrl,
    audioEnded,
    setAudioUrl,
    setNextUrl,
    setCurrentIdx,
    setIsCombined,
    setSegmentBoundarySec,
    combinedBaseIdxRef,
    lastObjectUrlRef,
    currentIdx,
    isSignedIn,
    enforceGate,
    enforceSignedGate,
    trialBaseOffsetRef,
    signedBaseOffsetRef,
    setChunkCurrent,
    setChunkNextUrl,
  ]);

  // Watch for nextUrl becoming available after audio ended
  // This handles the case where audio ends before prefetch completes
  React.useEffect(() => {
    if (!audioEnded) return; // Audio hasn't ended
    if (isChunked) return; // Chunked audio has separate logic
    if (!next) return; // No next page
    if (!nextUrl) return; // Next URL not ready yet

    const el = audioRef.current;
    if (!el) return;

    // Audio ended, next page exists, and nextUrl just became available
    logger.log('[audio] Next page audio arrived after ending, advancing now', {
      nextPage: next.number,
    });

    // Update duration offsets for usage tracking
    if (isFinite(el.duration) && el.duration > 0) {
      if (!isSignedIn) trialBaseOffsetRef.current += el.duration;
      else signedBaseOffsetRef.current += el.duration;
    }

    // Advance to next page
    setAudioUrl(nextUrl);
    setNextUrl(null);
    setCurrentIdx((i) => i + 1);

    // Auto-play when ready
    pendingAutoplayRef.current = true;

    // Start background combination for seeking (non-blocking)
    const prevSrc = el.currentSrc || audioUrl || '';
    if (prevSrc) {
      combineTwoUrlsToWav(prevSrc, nextUrl)
        .then((combo) => {
          if (combo && !el.paused) {
            logger.log(
              '[audio] Background combination ready, will use for seeking',
            );
            try {
              if (lastObjectUrlRef.current)
                URL.revokeObjectURL(lastObjectUrlRef.current);
            } catch {}
            lastObjectUrlRef.current = combo.url;
          }
        })
        .catch((err) => {
          logger.error('[audio] Background combination failed:', err);
        });
    }
  }, [
    audioEnded,
    isChunked,
    next,
    nextUrl,
    audioRef,
    audioUrl,
    setAudioUrl,
    setNextUrl,
    setCurrentIdx,
    pendingAutoplayRef,
    lastObjectUrlRef,
    isSignedIn,
    trialBaseOffsetRef,
    signedBaseOffsetRef,
  ]);

  // Track the current accumulated source synchronously to avoid race conditions
  const accumulatedSrcRef = React.useRef<string | null>(null);
  const combinedAudioRef = React.useRef<string | null>(null);
  const combinedBoundaryRef = React.useRef<number>(0);
  const isCombiningRef = React.useRef<boolean>(false);

  // Update accumulated source ref when audioUrl changes
  React.useEffect(() => {
    if (audioUrl) {
      accumulatedSrcRef.current = audioUrl;
    }
  }, [audioUrl]);

  // Progressive chunk combination: Combine in background, switch on chunk end
  // This maintains accumulated seek capability while avoiding mid-playback interruption
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    if (!isChunked) return;
    if (!chunkNextUrl) return;
    if (isCombiningRef.current) return; // Already combining

    const prevSrc =
      accumulatedSrcRef.current || el.currentSrc || audioUrl || '';
    if (!prevSrc) return;

    // Start background combination immediately
    logger.log('[audio] Starting background chunk combination');
    isCombiningRef.current = true;
    combinedAudioRef.current = null;
    combinedBoundaryRef.current = 0;

    let cancelled = false;

    combineTwoUrlsToWav(prevSrc, chunkNextUrl)
      .then((combo) => {
        if (cancelled || !combo) {
          isCombiningRef.current = false;
          return;
        }

        // Store combined audio and boundary for use when current chunk ends
        combinedAudioRef.current = combo.url;
        combinedBoundaryRef.current = combo.boundarySec;
        isCombiningRef.current = false;

        logger.log('[audio] Background combination ready', {
          boundarySec: combo.boundarySec,
        });

        // If audio has already ended while we were combining, switch now
        if (el.ended) {
          switchToCombinedAudio(combo);
        }
      })
      .catch((err) => {
        if (!cancelled) {
          logger.error('[audio] Background combination failed', err);
          isCombiningRef.current = false;
          combinedAudioRef.current = null;
          combinedBoundaryRef.current = 0;
        }
      });

    // Helper to switch to combined audio
    function switchToCombinedAudio(combo: {
      url: string;
      boundarySec: number;
    }) {
      logger.log('[audio] Switching to combined audio after background merge');
      try {
        if (lastObjectUrlRef.current) {
          URL.revokeObjectURL(lastObjectUrlRef.current);
        }
      } catch {}

      lastObjectUrlRef.current = combo.url;
      accumulatedSrcRef.current = combo.url;
      setAudioUrl(combo.url);
      setChunkCurrent((c) => c + 1);
      setChunkNextUrl(null);

      // Start playback from the boundary (where chunk2 begins)
      pendingAutoplayRef.current = true;
      const onLoadedMetadata = () => {
        logger.log('[audio] Combined audio metadata loaded, seeking');
        try {
          el.currentTime = combo.boundarySec;
        } catch {}
        if (!isSignedIn) enforceGate(el);
        else enforceSignedGate(el);
      };
      el.addEventListener('loadedmetadata', onLoadedMetadata, { once: true });
    }

    return () => {
      cancelled = true;
    };
  }, [
    audioRef,
    isChunked,
    chunkNextUrl,
    audioUrl,
    setAudioUrl,
    setChunkCurrent,
    setChunkNextUrl,
    lastObjectUrlRef,
    isSignedIn,
    enforceGate,
    enforceSignedGate,
  ]);
}
