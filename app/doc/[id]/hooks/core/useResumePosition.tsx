'use client';
import React from 'react';
import { logger } from '@/lib/logger';
import { getAuthHeaders } from '@/lib/access/client';
import { buildAnonQueryString } from '../utils/buildAnonQueryString';
import { handleAudioResponse } from '../utils/handleAudioResponse';

type PageRef = { id: string; number: number; status: string };

export interface ResumePositionHandlers {
  setProcessing: (val: boolean) => void;
  setIsChunked: (val: boolean) => void;
  setAudioUrl: (url: string) => void;
  setChunkTotal: (total: number) => void;
  setChunkCurrent: (chunk: number) => void;
  setChunkNextUrl: (url: string | null) => void;
  setCurrentIdx: (idx: number) => void;
}

/**
 * Manages save/restore of playback position across sign-in.
 * Handles resume target with retry logic and position restoration.
 */
export function useResumePosition(
  docId: string,
  isSignedIn: boolean,
  requireLogin: boolean,
  currentIdx: number,
  isCombined: boolean,
  segmentBoundarySec: number | null,
  pageOrder: PageRef[],
  current: PageRef | undefined,
  anonHeaders: Record<string, string>,
  isPlaying: boolean,
  audioRef: React.MutableRefObject<HTMLAudioElement | null>,
  combinedBaseIdxRef: React.MutableRefObject<number | null>,
  resumeLocalSecRef: React.MutableRefObject<number | null>,
  pendingAutoplayRef: React.MutableRefObject<boolean>,
  knownChunksRef: React.MutableRefObject<Map<number, string>>,
  nextPreloadAudioRef: React.MutableRefObject<HTMLAudioElement | null>,
  handlers: ResumePositionHandlers,
): {
  saveResumePosition: (reason: 'gate' | 'signup') => void;
} {
  const {
    setProcessing,
    setIsChunked,
    setAudioUrl,
    setChunkTotal,
    setChunkCurrent,
    setChunkNextUrl,
    setCurrentIdx,
  } = handlers;

  const resumeTargetRef = React.useRef<{
    pageId: string;
    localSec: number;
  } | null>(null);

  const getCurrentLocalPosition = React.useCallback(() => {
    const el = audioRef.current;
    let idx = currentIdx;
    let localSec = 0;
    try {
      localSec = Math.max(0, el?.currentTime || 0);
    } catch {
      localSec = 0;
    }
    if (
      isCombined &&
      segmentBoundarySec != null &&
      combinedBaseIdxRef.current != null
    ) {
      const base = combinedBaseIdxRef.current;
      if (localSec < segmentBoundarySec) {
        idx = base;
      } else {
        idx = base + 1;
        localSec = Math.max(0, localSec - segmentBoundarySec);
      }
    }
    const pageId = pageOrder[idx]?.id || current?.id;
    return { pageId, localSec };
  }, [
    currentIdx,
    isCombined,
    segmentBoundarySec,
    pageOrder,
    current,
    audioRef,
    combinedBaseIdxRef,
  ]);

  const saveResumePosition = React.useCallback(
    (reason: 'gate' | 'signup') => {
      try {
        const { pageId, localSec } = getCurrentLocalPosition();
        const payload = {
          docId,
          pageId,
          localSec: Math.max(0, Math.floor(localSec * 1000)) / 1000,
          reason,
          path:
            typeof window !== 'undefined'
              ? window.location.pathname + window.location.search
              : undefined,
          ts: Date.now(),
        };
        sessionStorage.setItem(
          `transread_resume_${docId}`,
          JSON.stringify(payload),
        );
      } catch {}
    },
    [docId, getCurrentLocalPosition],
  );

  // Save resume position when gate is triggered
  React.useEffect(() => {
    if (!isSignedIn && requireLogin) saveResumePosition('gate');
  }, [isSignedIn, requireLogin, saveResumePosition]);

  // Restore resume position after sign-in
  React.useEffect(() => {
    if (!isSignedIn) return;
    try {
      const raw = sessionStorage.getItem(`transread_resume_${docId}`);
      if (!raw) return;
      const data = JSON.parse(raw || 'null');
      if (!data || data.docId !== docId || !data.pageId) return;
      resumeTargetRef.current = {
        pageId: data.pageId,
        localSec: Number(data.localSec) || 0,
      };
      resumeLocalSecRef.current = Number(data.localSec) || 0;
      const idx = Math.max(
        0,
        pageOrder.findIndex((p) => p.id === data.pageId),
      );
      if (idx >= 0) setCurrentIdx(idx);
      pendingAutoplayRef.current = true;
      sessionStorage.removeItem(`transread_resume_${docId}`);
    } catch {}
  }, [
    isSignedIn,
    docId,
    pageOrder,
    setCurrentIdx,
    pendingAutoplayRef,
    resumeLocalSecRef,
  ]);

  // After sign-in, if we have a saved resume target, proactively ensure that page's audio
  // and set it as the current source so we actually resume from the correct position.
  const resumeEnsuredRef = React.useRef<boolean>(false);
  const resumeRetryCountRef = React.useRef<number>(0);
  React.useEffect(() => {
    const doEnsure = async () => {
      const tgt = resumeTargetRef.current;
      if (!isSignedIn || !tgt) return;
      if (resumeEnsuredRef.current && resumeRetryCountRef.current >= 3) return;
      if (!resumeEnsuredRef.current) {
        resumeEnsuredRef.current = true;
      }
      try {
        const qs = await buildAnonQueryString(anonHeaders);
        const r = await fetch(`/api/page/${tgt.pageId}/audio${qs}`, {
          method: 'GET',
          headers: getAuthHeaders(anonHeaders),
          cache: 'no-store',
        });
        const j = (await r.json().catch(() => ({}))) as {
          ok?: boolean;
          [key: string]: unknown;
        };
        if (j?.ok) {
          handleAudioResponse(j, true, {
            setIsChunked,
            setAudioUrl,
            setChunkTotal,
            setChunkCurrent,
            setChunkNextUrl,
            knownChunksRef,
            nextPreloadAudioRef,
            pendingAutoplayRef,
            isPlaying,
          });
          setProcessing(false);
        }
      } catch (e) {
        logger.error('[resume] Failed to ensure audio for resume', e);
        // Retry with exponential backoff if we haven't exceeded retry limit
        if (resumeRetryCountRef.current < 3) {
          resumeRetryCountRef.current++;
          const retryDelay =
            1000 * Math.pow(2, resumeRetryCountRef.current - 1); // 1s, 2s, 4s
          logger.log(
            '[resume] Retrying in',
            retryDelay,
            'ms. Attempt',
            resumeRetryCountRef.current,
          );
          setTimeout(() => {
            // Trigger re-run by not marking as ensured
            resumeEnsuredRef.current = false;
            doEnsure();
          }, retryDelay);
        } else {
          logger.error('[resume] Max retries exceeded, giving up');
        }
      }
    };
    doEnsure();
  }, [
    isSignedIn,
    anonHeaders,
    isPlaying,
    setProcessing,
    setIsChunked,
    setAudioUrl,
    setChunkTotal,
    setChunkCurrent,
    setChunkNextUrl,
    knownChunksRef,
    nextPreloadAudioRef,
    pendingAutoplayRef,
  ]);

  return { saveResumePosition };
}
