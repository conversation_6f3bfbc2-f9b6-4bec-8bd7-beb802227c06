import React from 'react';
import { getAnonCandidates } from '@/lib/anon';
import { getAuthHeaders } from '@/lib/access/client';

type PageRef = { id: string; number: number; status: string };

export function usePrefetchOnPlay(
  audioRef: React.RefObject<HTMLAudioElement | null>,
  isChunked: boolean,
  next: PageRef | undefined,
  nextUrl: string | null,
  requestedRef: React.MutableRefObject<Set<string>>,
  anonHeaders: Record<string, string>,
  setNextUrl: (u: string) => void,
) {
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;
    const onPlay = () => {
      if (isChunked) return;
      if (!next || nextUrl) return;
      if (!requestedRef.current.has(next.id)) {
        requestedRef.current.add(next.id);
        (async () => {
          try {
            const headers = getAuthHeaders({ ...anonHeaders }) as Record<
              string,
              string
            >;
            const sid = headers['x-anon-id'];
            let sids: string[] = [];
            try {
              const raw = headers['x-anon-alt-ids'];
              if (raw) {
                if (raw.trim().startsWith('[')) sids = JSON.parse(raw);
                else sids = raw.split(',').map((x) => x.trim());
              }
            } catch {}
            if (sid) sids = [sid, ...sids];
            if (sids.length === 0) {
              try {
                const cand2 = await getAnonCandidates();
                if (cand2) {
                  sids = Array.from(
                    new Set(
                      [cand2.stableId, cand2.fpjsId, cand2.coarseId].filter(
                        Boolean,
                      ) as string[],
                    ),
                  );
                }
              } catch {}
            }
            const qs = sids.length
              ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(sids.join(','))}`
              : '';
            const r = await fetch(`/api/page/${next.id}/ensure-tts${qs}`, {
              method: 'POST',
              headers,
            });
            const j = await r.json();
            // Handle multiple response formats:
            // - Streaming mode: j.streamUrl
            // - Complete audio: j.url
            // - Chunked audio: j.urls[0] (first chunk URL)
            if (j?.ok) {
              const url = j.streamUrl || j.url || (j.urls && j.urls[0]);
              if (url) setNextUrl(url);
            }
          } catch {}
        })();
      }
    };
    el.addEventListener('play', onPlay);
    return () => {
      el.removeEventListener('play', onPlay);
    };
  }, [
    audioRef,
    isChunked,
    next,
    nextUrl,
    anonHeaders,
    requestedRef,
    setNextUrl,
  ]);
}
