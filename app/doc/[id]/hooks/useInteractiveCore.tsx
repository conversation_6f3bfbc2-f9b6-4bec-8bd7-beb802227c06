'use client';
import React from 'react';
import { useUser } from '@clerk/nextjs';
import {
  FREE_TRIAL_SECONDS,
  SIGNED_IN_FREE_SECONDS,
  ENABLE_SIGNED_IN_GATE,
} from './utils/constants';
import { useAnonHeaders } from './utils/anonHeaders';
import { getProcessingMessage } from './utils/processingStage';
import { useDocumentPolling } from './core/useDocumentPolling';
import { useAudioControls } from './core/useAudioControls.simple';
import { useChunkPolling } from './core/useChunkPolling';
import { usePrefetch } from './core/usePrefetch';
import { useProactiveNextPageProcessing } from './core/useProactiveNextPageProcessing';
import { useAutoChainProcessing } from './core/useAutoChainProcessing';
import { usePageAdvance } from './core/usePageAdvance';
import { useProgressBar } from './core/useProgressBar';
import { usePageNavigation } from './core/usePageNavigation';
import { logger } from '@/lib/logger';

type PageRef = { id: string; number: number; status: string };

/**
 * Main audio player hook for TransReed PDF audio player.
 *
 * Phase 1 implementation includes:
 * - Document status polling and first page loading
 * - Basic playback controls (play/pause, seek, scrub)
 * - Chunked audio support with progressive loading
 * - Prefetch next page at 55% or ≤10s remaining
 * - Auto-advance to next page/chunk on end
 * - Processing progress bar
 *
 * Phase 2 features (deferred):
 * - Usage gating (free trial/tier limits)
 * - Resume position across sessions
 * - Combined audio seeking (WAV merging)
 * - Playback speed control
 * - Cache busting
 * - Proactive processing
 */
export function useInteractiveCore({
  docId,
  firstPageId,
  pageOrder,
}: {
  docId: string;
  firstPageId?: string;
  pageOrder: PageRef[];
}) {
  const { isSignedIn } = useUser();

  // ============ State Variables ============
  // Audio state
  const [audioUrl, setAudioUrl] = React.useState<string | null>(null);
  const [nextUrl, setNextUrl] = React.useState<string | null>(null);
  const [currentIdx, setCurrentIdx] = React.useState<number>(
    Math.max(
      0,
      pageOrder.findIndex((p) => p.id === firstPageId),
    ),
  );

  // Processing state
  const [processing, setProcessing] = React.useState<boolean>(true);
  const [processingStage, setProcessingStage] =
    React.useState<string>('processing_file');

  // Next page processing state (for overlay during page transitions)
  const [isProcessingNextPage, setIsProcessingNextPage] =
    React.useState<boolean>(false);
  const [nextPageProcessingStage, setNextPageProcessingStage] =
    React.useState<string>('');

  // Chunked audio state
  const [isChunked, setIsChunked] = React.useState<boolean>(false);
  const [chunkTotal, setChunkTotal] = React.useState<number>(0);
  const [chunkCurrent, setChunkCurrent] = React.useState<number>(0);
  const [chunkNextUrl, setChunkNextUrl] = React.useState<string | null>(null);

  // ============ Refs ============
  // Audio element
  const audioRef = React.useRef<HTMLAudioElement | null>(null);

  // Request deduplication
  const requestedRef = React.useRef<Set<string>>(new Set());
  const requestedChunksRef = React.useRef<Set<number>>(new Set());

  // Chunk management
  const knownChunksRef = React.useRef<Map<number, string>>(new Map());
  const nextPreloadAudioRef = React.useRef<HTMLAudioElement | null>(null);

  // Flags
  const initialEnsuredRef = React.useRef<boolean>(false);
  const pendingAutoplayRef = React.useRef<boolean>(false);

  // Chunk polling refs
  const chunkCurrentRef = React.useRef<number>(0);
  const chunkPollCleanupRef = React.useRef<(() => void) | null>(null);
  const chunkEndedNeedingNextRef = React.useRef<boolean>(false);

  // Proactive processing ref
  const processingTriggeredRef = React.useRef<Set<string>>(new Set());

  // ============ Derived Values ============
  const current = pageOrder[currentIdx];
  const next = pageOrder[currentIdx + 1];

  // Anonymous session headers
  const anonHeaders = useAnonHeaders();

  // ============ Sub-Hooks ============
  // Audio controls - basic playback without gating
  const {
    isPlaying,
    isAudioReady,
    duration,
    currentTime,
    audioEnded,
    togglePlay,
    seekBy,
    onScrubStart,
    onScrub,
    onScrubEnd,
  } = useAudioControls(audioRef, audioUrl);

  // Document polling - loads first page audio
  useDocumentPolling(
    docId,
    firstPageId,
    pageOrder,
    audioUrl,
    anonHeaders,
    requestedRef,
    initialEnsuredRef,
    knownChunksRef,
    nextPreloadAudioRef,
    pendingAutoplayRef,
    isPlaying,
    {
      setProcessing,
      setProcessingStage,
      setAudioUrl,
      setNextUrl,
      setIsChunked,
      setChunkTotal,
      setChunkCurrent,
      setChunkNextUrl,
      setCurrentIdx,
    },
  );

  // Chunk polling - progressive loading for chunked audio
  useChunkPolling(
    isChunked,
    isPlaying,
    chunkTotal,
    chunkCurrent,
    chunkNextUrl,
    current?.id,
    audioEnded,
    anonHeaders,
    chunkCurrentRef,
    chunkPollCleanupRef,
    knownChunksRef,
    requestedChunksRef,
    chunkEndedNeedingNextRef,
    nextPreloadAudioRef,
    setChunkNextUrl,
  );

  // Prefetch - fetch next page at 55% or ≤10s remaining
  usePrefetch(
    audioRef,
    isChunked,
    next,
    nextUrl,
    anonHeaders,
    requestedRef,
    setNextUrl,
  );

  // Proactive processing - trigger translation + TTS when ≤ 120s remaining
  useProactiveNextPageProcessing(
    audioRef,
    isChunked,
    next,
    anonHeaders,
    processingTriggeredRef,
  );

  // Auto-chain processing - trigger next page if current duration is short
  useAutoChainProcessing(
    audioRef,
    nextPreloadAudioRef,
    current?.id,
    next,
    pageOrder,
    currentIdx,
    anonHeaders,
    processingTriggeredRef,
  );

  // Page advance - auto-advance on page/chunk end
  usePageAdvance(
    audioRef,
    audioUrl,
    next,
    nextUrl,
    isChunked,
    chunkNextUrl,
    anonHeaders,
    requestedRef,
    setAudioUrl,
    setNextUrl,
    setCurrentIdx,
    setChunkCurrent,
    setChunkNextUrl,
    pendingAutoplayRef,
    setIsProcessingNextPage,
    setNextPageProcessingStage,
  );

  // Page navigation - manual previous/next page controls
  const {
    goToPreviousPage,
    goToNextPage,
    canGoToPrevious,
    canGoToNext,
    isNavigating,
  } = usePageNavigation({
    currentIdx,
    setCurrentIdx,
    pageOrder,
    anonHeaders,
    requestedRef,
    setAudioUrl,
    setIsChunked,
    setChunkTotal,
    setChunkCurrent,
    pendingAutoplayRef,
  });

  // Progress bar - animate processing progress
  const processingProgress = useProgressBar(
    processing,
    audioUrl,
    setProcessing,
  );

  // ============ Effects ============
  // Chunk cache cleanup - clear on page change
  React.useEffect(() => {
    knownChunksRef.current.clear();
    requestedChunksRef.current.clear();
    requestedRef.current.clear();
    setNextUrl(null);
  }, [current?.id]);

  // Autoplay on URL change - auto-play after page/chunk advance
  React.useEffect(() => {
    const el = audioRef.current;
    if (!el) return;

    let attempted = false;
    let timeoutId: ReturnType<typeof setTimeout> | null = null;

    const maybePlay = () => {
      if (!pendingAutoplayRef.current || attempted) return;
      attempted = true;
      pendingAutoplayRef.current = false;

      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }

      el.play().catch(() => {});
    };

    // Primary: trigger on loadedmetadata
    el.addEventListener('loadedmetadata', maybePlay);

    // Fallback: if loadedmetadata doesn't fire within 500ms, try anyway
    timeoutId = setTimeout(() => {
      if (pendingAutoplayRef.current && !attempted) {
        logger.log(
          '[autoplay] loadedmetadata timeout, attempting fallback play',
        );
        maybePlay();
      }
    }, 500);

    return () => {
      el.removeEventListener('loadedmetadata', maybePlay);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [audioUrl]);

  // Resume position tracking - save current page for resume functionality
  React.useEffect(() => {
    if (!current?.id) return;

    // Debounce to avoid excessive API calls
    const timeoutId = setTimeout(() => {
      fetch(`/api/doc/${docId}/resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...anonHeaders,
        },
        body: JSON.stringify({ pageId: current.id }),
      }).catch((err) => {
        logger.error('[resume] Failed to save resume position', err);
      });
    }, 2000);

    return () => clearTimeout(timeoutId);
  }, [current?.id, docId, anonHeaders]);

  // ============ Stub Values (Phase 1 - no gating) ============
  const requireLogin = false;
  const requireUpgrade = false;
  const trialDisplay = 0;
  const signedDisplay = 0;
  const saveResumePosition = () => {};
  const dismissUpgrade = () => {};

  // ============ Return Interface ============
  return {
    // Core refs and state
    audioRef,
    audioUrl,
    current,
    next,
    nextUrl,

    // Playback state
    isPlaying,
    isAudioReady,
    currentTime,
    duration,

    // Playback controls
    togglePlay,
    seekBy,
    onScrubStart,
    onScrub,
    onScrubEnd,

    // Page navigation controls
    goToPreviousPage,
    goToNextPage,
    canGoToPrevious,
    canGoToNext,
    isNavigating,

    // Processing state
    processingProgress,
    processingMessage: getProcessingMessage(processingStage, !!audioUrl),

    // Next page processing (for overlay during page transitions)
    isProcessingNextPage,
    nextPageProcessingMessage: getProcessingMessage(nextPageProcessingStage),

    // Usage gating (stubs for Phase 1)
    isSignedIn,
    FREE_TRIAL_SECONDS,
    SIGNED_IN_FREE_SECONDS,
    ENABLE_SIGNED_IN_GATE,
    requireLogin,
    requireUpgrade,
    trialDisplay,
    signedDisplay,
    allowedMax: duration || Number.MAX_SAFE_INTEGER,
    saveResumePosition,
    dismissUpgrade,
  } as const;
}
