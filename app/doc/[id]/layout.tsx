import { ReactNode } from 'react';

import { Navbar } from '@/app/components/Navbar';
import Providers from 'app/(public)/providers';

type DocumentLayoutProps = {
  children: ReactNode;
};

export default function DocumentLayout({ children }: DocumentLayoutProps) {
  const navbarHeight = 65;

  return (
    <Providers>
      <div className="flex h-screen flex-col">
        <Navbar />
        <main
          className="flex flex-1 flex-col"
          style={{ height: `calc(100vh - ${navbarHeight}px)` }}
        >
          {children}
        </main>
      </div>
    </Providers>
  );
}
