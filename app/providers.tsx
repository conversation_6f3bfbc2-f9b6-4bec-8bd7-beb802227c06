'use client';

import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { Hero<PERSON>Provider } from '@heroui/system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { ReactNode, Suspense, useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const createQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  });

type ProvidersProps = {
  children: ReactNode;
};

export default function Providers({ children }: ProvidersProps) {
  const router = useRouter();
  const [queryClient] = useState(createQueryClient);

  return (
    <ClerkProvider>
      <QueryClientProvider client={queryClient}>
        <Suspense>
          <ToastContainer
            position="bottom-center"
            autoClose={8000}
            newestOnTop
            closeOnClick
            pauseOnHover
          />
        </Suspense>
        <HeroUIProvider navigate={router.push} locale="en">
          {children}
        </HeroUIProvider>
      </QueryClientProvider>
    </ClerkProvider>
  );
}
