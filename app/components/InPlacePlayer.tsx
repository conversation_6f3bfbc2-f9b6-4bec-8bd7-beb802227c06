'use client';

import { useEffect, useState } from 'react';
import { getAnonCandidates } from '@/lib/anon';
import Interactive from '@/app/doc/[id]/Interactive';
import { Card, CardBody, Button } from '@heroui/react';

type DocData = {
  document: {
    id: string;
    originalFilename: string;
    userId?: string | null;
  };
  pages: Array<{ id: string; pageNumber: number; status: string }>;
  chapters: unknown[];
};

export default function InPlacePlayer({ docId }: { docId: string }) {
  const [data, setData] = useState<DocData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        // Include anonymous session headers so server can authorize
        const cand = await getAnonCandidates().catch(() => null);
        const headers: Record<string, string> = {};
        let sids: string[] = [];
        if (cand) {
          sids = Array.from(
            new Set(
              [cand.stableId, cand.fpjsId, cand.coarseId].filter(
                Boolean,
              ) as string[],
            ),
          );
          if (sids[0]) headers['x-anon-id'] = sids[0];
          if (sids.length > 1)
            headers['x-anon-alt-ids'] = JSON.stringify(sids.slice(1));
        }
        const qs = sids.length
          ? `?sid=${encodeURIComponent(sids[0])}&sids=${encodeURIComponent(
              sids.join(','),
            )}`
          : '';
        const res = await fetch(`/api/doc/${docId}${qs}`, {
          cache: 'no-store',
          headers,
        });
        if (!res.ok) throw new Error(`Failed to load document: ${res.status}`);
        const j = (await res.json()) as DocData;
        if (mounted) {
          setData(j);
          setLoading(false);
        }
      } catch (err) {
        if (mounted) {
          setError((err as Error).message || 'Load failed');
          setLoading(false);
        }
      }
    })();
    return () => {
      mounted = false;
    };
  }, [docId]);

  if (loading) {
    return (
      <Card className="shadow-sm">
        <CardBody className="flex min-h-[400px] items-center justify-center">
          <div className="flex items-center gap-2 text-gray-600">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
            <span>Loading document...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className="shadow-sm">
        <CardBody className="flex min-h-[400px] flex-col items-center justify-center gap-4">
          <p className="text-sm text-red-600">
            {error || 'Failed to load document'}
          </p>
          <Button
            color="primary"
            onClick={() => window.location.reload()}
            size="sm"
          >
            Retry
          </Button>
        </CardBody>
      </Card>
    );
  }

  const pageOrder = data.pages.map((p) => ({
    id: p.id,
    number: p.pageNumber,
    status: p.status,
  }));

  const firstPageId = pageOrder[0]?.id;

  return (
    <Card className="shadow-sm">
      <CardBody className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {data.document.originalFilename}
            </h3>
            <p className="text-sm text-gray-600">
              {data.pages.length} {data.pages.length === 1 ? 'page' : 'pages'}
            </p>
          </div>
          <Button
            as="a"
            href={`/doc/${docId}`}
            color="primary"
            variant="bordered"
            size="sm"
          >
            Full View
          </Button>
        </div>

        <div
          className="relative overflow-hidden rounded-lg bg-gradient-to-br from-blue-50 to-green-50"
          style={{ height: '400px' }}
        >
          <Interactive
            docId={docId}
            firstPageId={firstPageId}
            pageOrder={pageOrder}
            documentUserId={data.document.userId}
          />
        </div>
      </CardBody>
    </Card>
  );
}
