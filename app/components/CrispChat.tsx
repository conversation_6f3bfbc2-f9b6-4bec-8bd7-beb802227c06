'use client';

import { useEffect } from 'react';

type CrispChatProps = {
  websiteId: string;
};

declare global {
  interface Window {
    $crisp?: unknown[];
    CRISP_WEBSITE_ID?: string;
  }
}

export default function CrispChat({ websiteId }: CrispChatProps) {
  useEffect(() => {
    if (!websiteId) return;

    // Initialize Crisp
    window.$crisp = [];
    window.CRISP_WEBSITE_ID = websiteId;

    // Load Crisp script
    const script = document.createElement('script');
    script.src = 'https://client.crisp.chat/l.js';
    script.async = true;
    document.head.appendChild(script);

    // Cleanup on unmount
    return () => {
      // Remove Crisp script and widget if component unmounts
      const crispScript = document.querySelector(
        'script[src="https://client.crisp.chat/l.js"]',
      );
      if (crispScript) {
        crispScript.remove();
      }
      delete window.$crisp;
      delete window.CRISP_WEBSITE_ID;
    };
  }, [websiteId]);

  return null;
}
