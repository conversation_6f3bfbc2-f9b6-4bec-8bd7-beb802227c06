'use client';

import {
  Navbar as <PERSON><PERSON><PERSON><PERSON>,
  Nav<PERSON><PERSON><PERSON>,
  NavbarContent,
  Navbar<PERSON><PERSON>,
  Button,
} from '@heroui/react';
import {
  SignedIn,
  SignedOut,
  SignInButton,
  SignUpButton,
  UserButton,
} from '@clerk/nextjs';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export function Navbar() {
  const pathname = usePathname();
  const isLandingPage = pathname === '/';

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <HeroNavbar isBordered maxWidth="full">
      <div className="mx-auto flex w-full max-w-7xl items-center">
        <NavbarBrand>
          <Link href="/" className="flex items-center gap-2">
            <span className="text-2xl font-semibold text-gray-900">
              TransReed
            </span>
          </Link>
        </NavbarBrand>

        {/* Center Navigation Links - Only on Landing Page */}
        {isLandingPage && (
          <NavbarContent className="hidden gap-8 md:flex" justify="center">
            <NavbarItem>
              <button
                onClick={() => scrollToSection('features')}
                className="text-gray-700 transition-colors hover:text-blue-600"
              >
                Features
              </button>
            </NavbarItem>
            <NavbarItem>
              <button
                onClick={() => scrollToSection('pricing')}
                className="text-gray-700 transition-colors hover:text-blue-600"
              >
                Pricing
              </button>
            </NavbarItem>
            <NavbarItem>
              <button
                onClick={() => scrollToSection('faqs')}
                className="text-gray-700 transition-colors hover:text-blue-600"
              >
                FAQs
              </button>
            </NavbarItem>
          </NavbarContent>
        )}

        <NavbarContent className="ml-auto gap-4" justify="end">
          <SignedOut>
            <NavbarItem>
              <SignInButton mode="modal">
                <Button variant="light" size="md">
                  Log in
                </Button>
              </SignInButton>
            </NavbarItem>
            <NavbarItem>
              <SignUpButton mode="modal">
                <Button color="primary" size="md">
                  Sign up
                </Button>
              </SignUpButton>
            </NavbarItem>
          </SignedOut>
          <SignedIn>
            <NavbarItem>
              <Button as={Link} href="/documents" color="primary" size="md">
                Library
              </Button>
            </NavbarItem>
            <NavbarItem>
              <UserButton
                afterSignOutUrl="/"
                signOutCallback={() => {
                  // Force full page reload to clear all cached data
                  window.location.href = '/';
                }}
              />
            </NavbarItem>
          </SignedIn>
        </NavbarContent>
      </div>
    </HeroNavbar>
  );
}
