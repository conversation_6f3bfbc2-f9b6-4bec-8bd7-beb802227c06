'use client';

import { useEffect, useState } from 'react';
import { getAnonCandidates } from '@/lib/anon';
import UploadDropzone from '@/app/(protected)/components/UploadDropzone';
import {
  Button,
  Card,
  CardBody,
  Select,
  SelectItem,
  RadioGroup,
  Radio,
} from '@heroui/react';
import { getRecaptchaToken } from '@/lib/recaptcha/client';
import { setAccessToken } from '@/lib/access/client';
import { useUser } from '@clerk/nextjs';
import { PricingModal } from '@/app/(protected)/components/PricingModal';

export default function UploadForm({
  onSuccess,
}: {
  onSuccess?: (docId: string) => void;
}) {
  const { isSignedIn } = useUser();
  const [file, setFile] = useState<File | null>(null);
  const [language, setLanguage] = useState('en');
  const [voice, setVoice] = useState('female');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [anonId, setAnonId] = useState<string | null>(null);
  const [anonAltIds, setAnonAltIds] = useState<string[]>([]);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Anonymous candidate detection (works for both signed-in and signed-out)
  useEffect(() => {
    let cancel = false;
    (async () => {
      const cand = await getAnonCandidates();
      if (cancel) return;
      if (!cand) return;
      const primary = cand.stableId || cand.coarseId;
      setAnonId(primary || null);
      const sids = Array.from(
        new Set(
          [primary, cand.stableId, cand.fpjsId, cand.coarseId].filter(
            Boolean,
          ) as string[],
        ),
      );
      setAnonAltIds(sids.filter((x) => x !== (cand.stableId || '')));
    })();
    return () => {
      cancel = true;
    };
  }, []);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    if (!file) {
      setError('Please choose a PDF file');
      return;
    }
    setLoading(true);
    const body = new FormData();
    body.append('file', file);
    body.append('targetLanguage', language);
    body.append('voice', voice);
    if (anonId) body.append('anonId', anonId);
    if (anonAltIds && anonAltIds.length > 0)
      body.append('anonAltIds', JSON.stringify(anonAltIds));
    try {
      // Only include reCAPTCHA for anonymous uploads
      if (!isSignedIn) {
        const token = await getRecaptchaToken('upload');
        if (token) body.append('recaptchaToken', token);
      }
    } catch {}
    try {
      const res = await fetch('/api/upload', { method: 'POST', body });
      if (!res.ok) {
        setLoading(false);
        const data = await res.json().catch(() => ({}));
        // Check if this is a plan limit error
        if (
          res.status === 403 &&
          (data as any)?.requireUpgrade &&
          (data as any)?.reason === 'document_limit_reached'
        ) {
          const currentCount = (data as any)?.currentCount || 0;
          const maxCount = (data as any)?.maxCount || 0;
          setError(
            `You've reached your upload limit (${currentCount}/${maxCount} documents this month). Please upgrade your plan to continue.`,
          );
          setShowUpgradeModal(true);
          return;
        }
        setError((data as any)?.error || 'Upload failed');
        return;
      }
      const data = (await res.json()) as {
        docId: string;
        existed?: boolean;
        token?: string;
        type?: 'anon_doc' | 'user';
      };
      try {
        if (data.token && data.type) setAccessToken(data.token, data.type);
      } catch {}
      // If onSuccess callback is provided, use it; otherwise redirect
      if (onSuccess) {
        setLoading(false);
        onSuccess(data.docId);
      } else {
        // Don't set loading to false here - keep button disabled during redirect
        window.location.href = `/doc/${data.docId}`;
      }
    } catch (err) {
      setLoading(false);
      setError('Upload failed');
    }
  };

  type LangOpt = { value: string; label: string; flag: string };
  const langOptions: LangOpt[] = [
    { value: 'en', label: 'English', flag: '🇺🇸' },
    { value: 'vi', label: 'Tiếng Việt', flag: '🇻🇳' },
    { value: 'es', label: 'Español', flag: '🇪🇸' },
    { value: 'fr', label: 'Français', flag: '🇫🇷' },
    { value: 'de', label: 'Deutsch', flag: '🇩🇪' },
    { value: 'ja', label: '日本語', flag: '🇯🇵' },
    { value: 'zh', label: '中文', flag: '🇨🇳' },
  ];

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      <Card className="shadow-sm">
        <CardBody className="space-y-6">
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-900">
              PDF file
            </label>
            <UploadDropzone
              onFileSelected={setFile}
              selectedFile={file}
              disabled={loading}
            />
          </div>

          <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
            <div className="w-full sm:w-56">
              <Select
                label="Target language"
                items={langOptions as any}
                selectedKeys={new Set([language])}
                onSelectionChange={(keys) => {
                  const key = Array.from(keys as Set<string>)[0];
                  if (key) setLanguage(key);
                }}
                renderValue={(items) =>
                  items.map((item) => (
                    <div
                      key={String(item.key)}
                      className="flex items-center gap-2"
                    >
                      <span className="text-base">
                        {(item.data as any)?.flag}
                      </span>
                      <span>{(item.data as any)?.label}</span>
                    </div>
                  ))
                }
                className="w-full"
                variant="bordered"
                size="lg"
                classNames={{ trigger: 'h-12' }}
              >
                {(item: any) => (
                  <SelectItem
                    key={item.value}
                    textValue={item.label}
                    startContent={
                      <span className="text-base">{item.flag}</span>
                    }
                  >
                    {item.label}
                  </SelectItem>
                )}
              </Select>
            </div>
            <div className="sm:ml-4 sm:w-64">
              <RadioGroup
                label="Voice"
                orientation="horizontal"
                value={voice}
                onValueChange={(val) => setVoice(String(val))}
                classNames={{
                  label: 'text-sm text-gray-900',
                  wrapper: 'gap-6 sm:gap-8',
                }}
              >
                <Radio value="female">Female</Radio>
                <Radio value="male">Male</Radio>
              </RadioGroup>
            </div>
            <div className="flex w-full justify-center sm:ml-auto sm:w-auto">
              <Button
                color="primary"
                type="submit"
                isDisabled={loading}
                isLoading={loading}
                size="lg"
                className="h-12 w-full sm:w-auto"
              >
                Upload & process
              </Button>
            </div>
          </div>

          {error && <p className="text-sm text-red-600">{error}</p>}
        </CardBody>
      </Card>

      <PricingModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
      />
    </form>
  );
}
