'use client';

import { Progress } from '@heroui/react';
import { useState } from 'react';
import { PricingModal } from './PricingModal';

interface PlanUsageCardProps {
  planName: string;
  planKey: string;
  usedSeconds: number;
  totalSeconds: number | null;
}

function formatDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes < 60) {
    // Show minutes and seconds (e.g., "2m 32s")
    if (remainingSeconds === 0) {
      return `${minutes}m`;
    }
    return `${minutes}m ${remainingSeconds}s`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  // For hours, show hours and minutes (don't show seconds for clarity)
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  return `${hours}h ${remainingMinutes}m`;
}

export function PlanUsageCard({
  planName,
  planKey,
  usedSeconds,
  totalSeconds,
}: PlanUsageCardProps) {
  const [showPricingModal, setShowPricingModal] = useState(false);

  const isUnlimited = totalSeconds === null || totalSeconds === 0;
  const percentage = isUnlimited
    ? 0
    : Math.min(100, (usedSeconds / totalSeconds) * 100);

  const isNearLimit = !isUnlimited && percentage >= 80;
  const isOverLimit = !isUnlimited && usedSeconds >= totalSeconds;

  const progressColor = isOverLimit
    ? 'danger'
    : isNearLimit
      ? 'warning'
      : 'primary';

  return (
    <>
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-start justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Current Plan: {planName}
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              {isUnlimited ? (
                <>
                  <span className="font-medium text-green-600">Unlimited</span>{' '}
                  listening time
                </>
              ) : (
                <>
                  {formatDuration(usedSeconds)} of{' '}
                  {formatDuration(totalSeconds)} used
                </>
              )}
            </p>
          </div>
          {planKey === 'free' && (
            <button
              onClick={() => setShowPricingModal(true)}
              className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
            >
              Upgrade
            </button>
          )}
        </div>

        {!isUnlimited && (
          <div className="space-y-2">
            <Progress
              value={percentage}
              color={progressColor}
              size="md"
              className="w-full"
              aria-label="Listening time usage"
            />
            {isOverLimit && (
              <p className="text-sm text-red-600">
                You&apos;ve exceeded your listening time limit. Upgrade to
                continue.
              </p>
            )}
            {isNearLimit && !isOverLimit && (
              <p className="text-sm text-orange-600">
                You&apos;re approaching your listening time limit.
              </p>
            )}
          </div>
        )}
      </div>

      <PricingModal
        isOpen={showPricingModal}
        onClose={() => setShowPricingModal(false)}
      />
    </>
  );
}
