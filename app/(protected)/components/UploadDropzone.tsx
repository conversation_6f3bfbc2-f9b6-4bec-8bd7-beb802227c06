'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON>, CardBody, Button } from '@heroui/react';
import { getAnonCandidates } from '@/lib/anon';
import { useUser } from '@clerk/nextjs';

type UploadDropzoneProps = {
  onFileSelected: (file: File | null) => void;
  selectedFile?: File | null;
  disabled?: boolean;
};

export default function UploadDropzone({
  onFileSelected,
  selectedFile,
  disabled,
}: UploadDropzoneProps) {
  const { isSignedIn } = useUser();
  const [redirectDocId, setRedirectDocId] = useState<string | null>(null);

  // For anonymous users, detect if they already uploaded a document.
  useEffect(() => {
    let cancelled = false;
    (async () => {
      try {
        if (isSignedIn) return; // only apply to anonymous users
        const cand = await getAnonCandidates();
        if (!cand) return;
        const sids = Array.from(
          new Set(
            [cand.stableId, cand.fpjsId, cand.coarseId].filter(
              Boolean,
            ) as string[],
          ),
        );
        if (sids.length === 0) return;
        const params = new URLSearchParams();
        for (const s of sids) params.append('sid', s);
        if (cand.stableId) params.set('normalizeTo', cand.stableId);
        const res = await fetch(`/api/doc/by-session?${params.toString()}`);
        if (!res.ok) return;
        const data = (await res.json()) as {
          ok?: boolean;
          docId?: string | null;
        };
        if (!cancelled && data?.docId) setRedirectDocId(data.docId);
      } catch {}
    })();
    return () => {
      cancelled = true;
    };
  }, [isSignedIn]);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const file = acceptedFiles?.[0] ?? null;
      onFileSelected(file);
    },
    [onFileSelected],
  );

  const accept = useMemo(
    () => ({
      'application/pdf': ['.pdf'],
    }),
    [],
  );

  const { getRootProps, getInputProps, isDragActive, fileRejections, open } =
    useDropzone({
      onDrop,
      accept,
      maxFiles: 1,
      multiple: false,
      disabled,
      noClick: true,
      // let react-dropzone handle clicks on the root element
    });

  const errorMsg = fileRejections?.[0]?.errors?.[0]?.message;

  const zoneClass = (active: boolean) =>
    [
      'flex items-center justify-center p-8 border border-dashed min-h-[180px] w-full',
      disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer',
      active
        ? 'border-blue-500 ring-2 ring-blue-200'
        : 'border-gray-300 hover:border-gray-400',
    ].join(' ');

  const prettyBytes = (bytes: number) => {
    if (!Number.isFinite(bytes)) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB'];
    let i = 0;
    let num = bytes;
    while (num >= 1024 && i < units.length - 1) {
      num /= 1024;
      i++;
    }
    return `${num.toFixed(num >= 100 ? 0 : num >= 10 ? 1 : 2)} ${units[i]}`;
  };

  return (
    <div className="space-y-1">
      <Card className="bg-white">
        <CardBody className="p-0">
          {selectedFile ? (
            <div
              {...getRootProps({
                className: zoneClass(false) + ' relative bg-gray-50',
              })}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (!disabled) open();
              }}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  if (!disabled) open();
                }
              }}
            >
              <input {...getInputProps()} />
              <div className="absolute right-2 top-2">
                <Button
                  isIconOnly
                  variant="light"
                  radius="full"
                  aria-label="Remove selected file"
                  className="h-8 w-8 text-gray-500 hover:text-gray-700"
                  onPress={(e) => {
                    // stop bubbling to dropzone
                    // @ts-ignore
                    e?.preventDefault?.();
                    // @ts-ignore
                    e?.stopPropagation?.();
                    onFileSelected(null);
                  }}
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    className="h-4 w-4"
                  >
                    <path d="M6 6l12 12M18 6L6 18" />
                  </svg>
                </Button>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-gray-900">
                  {selectedFile.name}
                </p>
                <p className="mt-1 text-xs text-gray-600">
                  {prettyBytes(selectedFile.size)}
                </p>
                <p className="mt-2 text-xs text-gray-500">
                  Click to replace file
                </p>
              </div>
            </div>
          ) : (
            <div
              {...getRootProps({ className: zoneClass(isDragActive) })}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (disabled) return;
                if (!selectedFile && redirectDocId) {
                  window.location.href = `/doc/${redirectDocId}`;
                  return;
                }
                open();
              }}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  if (disabled) return;
                  if (!selectedFile && redirectDocId) {
                    window.location.href = `/doc/${redirectDocId}`;
                    return;
                  }
                  open();
                }
              }}
            >
              <input {...getInputProps()} />
              <div className="text-center">
                <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-blue-50 text-blue-600">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.8"
                    className="h-7 w-7"
                  >
                    <path d="M12 16V4" />
                    <path d="M6 10l6-6 6 6" />
                    <rect x="3" y="16" width="18" height="4" rx="1" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-900">
                  Drag and drop your PDF here
                </p>
                <p className="mt-1 text-xs text-gray-600">
                  or click to select a single file
                </p>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
      {errorMsg && <p className="text-xs text-red-600">{errorMsg}</p>}
      <p className="text-xs text-gray-500">
        Only .pdf files are supported. Max 1 file.
      </p>
    </div>
  );
}
