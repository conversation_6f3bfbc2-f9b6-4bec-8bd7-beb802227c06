'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@heroui/react';
import { logger } from '@/lib/logger';

interface PricingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// NOTE: productId can be either a Polar Product ID or Price ID
// When passing to checkout, use the query parameter name "products" (required by Polar)
const PRICING_TIERS = [
  {
    name: 'Starter',
    price: 5,
    productId: process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_STARTER,
    description: 'Perfect for getting started',
    features: [
      '1 hour listening time',
      'All languages supported',
      'High-quality TTS',
      'Chapter detection',
    ],
  },
  {
    name: 'Pro',
    price: 19,
    productId: process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_PRO,
    description: 'Most popular choice',
    features: [
      '5 hours listening time',
      'All languages supported',
      'High-quality TTS',
      'Chapter detection',
      'Priority processing',
    ],
    popular: true,
  },
  {
    name: 'Premium',
    price: 49,
    productId: process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_PREMIUM,
    description: 'For power users',
    features: [
      '15 hours listening time',
      'All languages supported',
      'High-quality TTS',
      'Chapter detection',
      'Priority processing',
      'Email support',
    ],
  },
];

export function PricingModal({ isOpen, onClose }: PricingModalProps) {
  const handlePurchase = (productId: string | undefined) => {
    try {
      // Validate productId exists
      if (!productId || productId.trim() === '') {
        logger.error('Product ID is not configured for this tier');
        alert(
          'This plan is not yet configured. Please contact support or try another plan.',
        );
        return;
      }

      // Construct checkout URL with products parameter (required by Polar)
      const checkoutUrl = new URL(
        '/api/polar/checkout',
        window.location.origin,
      );
      checkoutUrl.searchParams.set('products', productId);

      // Open Polar checkout in new tab
      const newWindow = window.open(checkoutUrl.toString(), '_blank');

      // Check if window.open was blocked
      if (
        !newWindow ||
        newWindow.closed ||
        typeof newWindow.closed === 'undefined'
      ) {
        alert(
          'Popup was blocked. Please allow popups for this site and try again.',
        );
        return;
      }

      // Close the pricing modal after successfully opening checkout
      onClose();
    } catch (error) {
      logger.error('Failed to open checkout:', error);
      alert('Failed to open checkout. Please try again.');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: 'max-h-[90vh]',
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Choose Your Plan</h2>
          <p className="text-sm font-normal text-gray-600">
            Select the plan that works best for you
          </p>
        </ModalHeader>
        <ModalBody className="pb-6">
          <div
            className={`grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-${PRICING_TIERS.length}`}
          >
            {PRICING_TIERS.map((tier) => (
              <div
                key={tier.name}
                className={`relative flex flex-col rounded-lg border-2 p-6 ${
                  tier.popular
                    ? 'border-blue-500 shadow-lg'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {tier.popular && (
                  <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                    <span className="rounded-full bg-blue-500 px-3 py-1 text-xs font-semibold text-white">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {tier.name}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {tier.description}
                  </p>
                </div>

                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">
                    ${tier.price}
                  </span>
                  <span className="text-gray-500">/month</span>
                </div>

                <ul className="mb-6 flex-1 space-y-3">
                  {tier.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <svg
                        className="mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  color={tier.popular ? 'primary' : 'default'}
                  variant={tier.popular ? 'solid' : 'bordered'}
                  className="w-full"
                  onPress={() => handlePurchase(tier.productId)}
                  isDisabled={!tier.productId || tier.productId.trim() === ''}
                >
                  {!tier.productId || tier.productId.trim() === ''
                    ? 'Coming Soon'
                    : 'Get Started'}
                </Button>
              </div>
            ))}
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
