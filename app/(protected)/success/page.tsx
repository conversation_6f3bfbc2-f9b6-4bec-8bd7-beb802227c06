'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@heroui/react';
import { logger } from '@/lib/logger';

function SuccessContent() {
  const searchParams = useSearchParams();
  const checkoutId = searchParams.get('checkout_id');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);

  useEffect(() => {
    if (checkoutId) {
      // Verify checkout and update user subscription
      const verifyCheckout = async () => {
        try {
          setStatusMessage('Verifying your payment...');
          const response = await fetch('/api/polar/verify-checkout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ checkoutId }),
          });

          const data = await response.json();

          if (!response.ok) {
            // If checkout is still being processed, retry up to 3 times
            if (data.status === 'open' && retryCount < 3) {
              logger.log(
                `Checkout still processing, retry ${retryCount + 1}/3`,
              );
              setStatusMessage(
                data.message || 'Payment is being processed. Please wait...',
              );
              // Retry after 2 seconds
              setTimeout(() => {
                setRetryCount(retryCount + 1);
              }, 2000);
              return;
            }

            throw new Error(
              data.message || data.error || 'Failed to verify checkout',
            );
          }

          logger.log('Checkout verified:', data);
          setLoading(false);
          setStatusMessage(null);
        } catch (err) {
          logger.error('Checkout verification error:', err);
          setError(
            err instanceof Error ? err.message : 'Failed to verify checkout',
          );
          setLoading(false);
          setStatusMessage(null);
        }
      };

      verifyCheckout();
    } else {
      setError('No checkout ID provided');
      setLoading(false);
    }
  }, [checkoutId, retryCount]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
          <p className="text-gray-600">
            {statusMessage || 'Processing your payment...'}
          </p>
          {retryCount > 0 && (
            <p className="mt-2 text-sm text-gray-500">
              Attempt {retryCount + 1}/4
            </p>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="max-w-md rounded-lg bg-white p-8 text-center shadow-lg ring-1 ring-gray-200">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <h1 className="mb-2 text-2xl font-bold text-gray-900">
            Payment Error
          </h1>
          <p className="mb-6 text-gray-600">{error}</p>
          <div className="flex flex-col gap-3">
            <Button
              onPress={() => {
                setError(null);
                setLoading(true);
                setRetryCount(0);
              }}
              color="primary"
            >
              Try Again
            </Button>
            <Button as={Link} href="/app" variant="bordered">
              Return to Dashboard
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="max-w-md rounded-lg bg-white p-8 text-center shadow-lg ring-1 ring-gray-200">
        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
          <svg
            className="h-8 w-8 text-green-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h1 className="mb-2 text-2xl font-bold text-gray-900">
          Payment Successful!
        </h1>
        <p className="mb-6 text-gray-600">
          Thank you for your purchase. Your subscription has been activated and
          you can now enjoy unlimited access to all features.
        </p>
        <div className="mb-4 rounded-lg bg-gray-50 p-4">
          <p className="text-sm text-gray-500">Order ID:</p>
          <p className="font-mono text-sm text-gray-700">{checkoutId}</p>
        </div>
        <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
          <Button as={Link} href="/app" color="primary">
            Go to Dashboard
          </Button>
          <Button as={Link} href="/" variant="bordered">
            Back to Home
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function SuccessPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <div className="mb-4 inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      }
    >
      <SuccessContent />
    </Suspense>
  );
}
