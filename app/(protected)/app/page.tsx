'use client';

import { useEffect, useState } from 'react';
import { getAnonCandidates } from '@/lib/anon';
import { useUser } from '@clerk/nextjs';
import UploadForm from '@/app/components/UploadForm';
import { logger } from '@/lib/logger';

export default function AppHomePage() {
  const [anonId, setAnonId] = useState<string | null>(null);
  const [anonAltIds, setAnonAltIds] = useState<string[]>([]);
  const { isLoaded, isSignedIn } = useUser();

  // Detect identity; if signed-in, claim any anonymous docs for these session IDs.
  // If not signed-in, redirect to existing anon doc if present.
  useEffect(() => {
    let cancel = false;
    (async () => {
      if (!isLoaded) return; // wait for Clerk to hydrate to avoid false anon redirect
      const cand = await getAnonCandidates();
      if (cancel) return;
      if (!cand) return;
      // Prefer stableId (FPJS visitorId if available), fallback to coarse
      const primary = cand.stableId || cand.coarseId;
      setAnonId(primary || null);
      const sids = Array.from(
        new Set(
          [primary, cand.stableId, cand.fpjsId, cand.coarseId].filter(
            Boolean,
          ) as string[],
        ),
      );
      setAnonAltIds(sids.filter((x) => x !== (cand.stableId || '')));
      try {
        logger.info('[anon] candidates:', cand);
        logger.info(
          '[anon] chosen primary:',
          primary,
          'sids used for lookup:',
          sids,
        );
      } catch {}
      if (isSignedIn) {
        // Best-effort: claim any docs attached to these SIDs, then do not redirect by session
        try {
          await fetch('/api/doc/claim-by-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sids }),
          });
        } catch {}
        return;
      }
      // Signed-out: redirect to anon-bound document if exists
      try {
        const q = sids.map((sid) => `sid=${encodeURIComponent(sid)}`).join('&');
        const r = await fetch(
          `/api/doc/by-session?${q}&normalizeTo=${encodeURIComponent(primary || '')}`,
          { cache: 'no-store' },
        );
        if (r.ok) {
          const j = await r.json();
          try {
            logger.info('[anon] by-session response:', j);
          } catch {}
          if (j?.docId) {
            window.location.replace(`/doc/${j.docId}`);
          }
        }
      } catch {}
    })();
    return () => {
      cancel = true;
    };
  }, [isLoaded, isSignedIn]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Upload a PDF</h2>
        <p className="text-sm text-gray-600">
          Pick a target language and voice, then we’ll prepare page-by-page
          audio.
        </p>
      </div>
      <UploadForm />
    </div>
  );
}
