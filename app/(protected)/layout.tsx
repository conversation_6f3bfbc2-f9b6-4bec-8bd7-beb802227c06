import { ReactNode } from 'react';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

import { Navbar } from '@/app/components/Navbar';

import Providers from './providers';

type AppLayoutProps = {
  children: ReactNode;
};

export default async function AppLayout({ children }: AppLayoutProps) {
  const { userId } = await auth();
  if (!userId) redirect('/');

  return (
    <Providers>
      <Navbar />
      <main className="mx-auto max-w-5xl p-6">{children}</main>
    </Providers>
  );
}
