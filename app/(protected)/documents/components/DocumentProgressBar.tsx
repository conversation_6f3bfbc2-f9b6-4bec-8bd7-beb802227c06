'use client';

import { Progress } from '@heroui/react';

interface DocumentProgressBarProps {
  completed: number;
  total: number;
  targetLanguage?: string | null;
  voice?: string | null;
}

export function DocumentProgressBar({
  completed,
  total,
  targetLanguage,
  voice,
}: DocumentProgressBarProps) {
  // Hide if no target language or voice configured
  if (!targetLanguage || !voice) {
    return null;
  }

  // Hide if no pages to process
  if (total === 0) {
    return null;
  }

  const percentage = Math.min(100, Math.round((completed / total) * 100));

  // Determine color based on progress
  const progressColor =
    percentage === 100 ? 'success' : percentage === 0 ? 'default' : 'primary';

  return (
    <div className="space-y-1">
      <Progress
        value={percentage}
        color={progressColor}
        size="sm"
        className="w-full"
        aria-label="Document processing progress"
      />
      <div className="text-xs text-gray-600">
        {completed} / {total} pages ready
      </div>
    </div>
  );
}
