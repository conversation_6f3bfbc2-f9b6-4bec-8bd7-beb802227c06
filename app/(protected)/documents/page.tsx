import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { prisma } from '@/lib/prisma';
import { ensureUserByClerkId } from '@/lib/user';
import { getUserCycleListeningSeconds } from '@/lib/usage';
import { PlanUsageCard } from '../components/PlanUsageCard';
import { DocumentProgressBar } from './components/DocumentProgressBar';

export const dynamic = 'force-dynamic';

type DocumentWithProgress = {
  id: string;
  originalFilename: string;
  pageCount: number | null;
  createdAt: Date;
  targetLanguage: string | null;
  voice: string | null;
  processable_pages: bigint;
  total_pages: bigint;
  completed_pages: bigint;
};

function timeAgo(input: Date | string): string {
  const ts = typeof input === 'string' ? Date.parse(input) : input.getTime();
  const diff = Date.now() - ts;
  if (!Number.isFinite(diff) || diff < 0) return 'just now';
  const sec = Math.floor(diff / 1000);
  if (sec < 10) return 'just now';
  if (sec < 60) return `${sec}s ago`;
  const min = Math.floor(sec / 60);
  if (min < 60) return `${min}m ago`;
  const hr = Math.floor(min / 60);
  if (hr < 24) return `${hr}h ago`;
  const day = Math.floor(hr / 24);
  if (day < 30) return `${day}d ago`;
  const mo = Math.floor(day / 30);
  if (mo < 12) return `${mo}mo ago`;
  const yr = Math.floor(mo / 12);
  return `${yr}y ago`;
}

export default async function DocumentsPage() {
  const { userId: clerkUserId } = await auth();
  if (!clerkUserId) redirect('/');

  const dbUserId = await ensureUserByClerkId(clerkUserId);

  // Fetch user with plan information
  const user = await prisma.user.findUnique({
    where: { id: dbUserId },
    select: {
      planKey: true,
      billingPeriodStart: true,
      billingPeriodEnd: true,
      plan: {
        select: {
          name: true,
          includedListeningTime: true,
        },
      },
    },
  });

  // Fetch current usage cycle using the same logic as tracking
  const usedListeningTime = await getUserCycleListeningSeconds(dbUserId);

  // Fetch documents with progress information using raw SQL for efficiency
  const rawDocs = await prisma.$queryRaw<DocumentWithProgress[]>`
    SELECT
      d.id,
      d."originalFilename",
      d."pageCount",
      d."createdAt",
      d."targetLanguage",
      d.voice,
      COUNT(DISTINCT p.id) FILTER (WHERE p."isSkippableByDefault" = false) as processable_pages,
      COUNT(DISTINCT p.id) as total_pages,
      COUNT(DISTINCT CASE
        WHEN pa.id IS NOT NULL
             AND pa.language = d."targetLanguage"
             AND pa.voice = d.voice
        THEN p.id
      END) as completed_pages
    FROM documents d
    LEFT JOIN pages p ON p."documentId" = d.id
    LEFT JOIN page_audios pa ON pa."pageId" = p.id
    WHERE d."userId" = ${dbUserId}
    GROUP BY d.id, d."originalFilename", d."pageCount", d."createdAt", d."targetLanguage", d.voice
    ORDER BY d."createdAt" DESC
  `;

  // Convert bigint to number for easier handling in UI
  const docs = rawDocs.map((d) => ({
    ...d,
    processablePages: Number(d.processable_pages),
    totalPages: Number(d.total_pages),
    completedPages: Number(d.completed_pages),
  }));

  return (
    <div className="space-y-6">
      {/* Plan Usage Card */}
      {user && (
        <PlanUsageCard
          planName={user.plan.name}
          planKey={user.planKey}
          usedSeconds={usedListeningTime}
          totalSeconds={user.plan.includedListeningTime}
        />
      )}

      <div>
        <h2 className="text-xl font-semibold">My Documents</h2>
        <p className="text-sm text-gray-600">
          All PDFs you&apos;ve uploaded in this account.
        </p>
      </div>

      {docs.length === 0 ? (
        <div className="rounded border bg-white p-6 text-sm text-gray-700">
          <div>No documents yet.</div>
          <a
            href="/app"
            className="mt-3 inline-flex items-center justify-center rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700"
          >
            Upload a PDF
          </a>
        </div>
      ) : (
        <ul className="divide-y rounded border bg-white">
          {docs.map((d) => {
            const pages =
              typeof d.pageCount === 'number' && d.pageCount > 0
                ? d.pageCount
                : d.totalPages;
            // Use processable pages if available, otherwise use total pages
            const progressTotal =
              d.processablePages > 0 ? d.processablePages : d.totalPages;
            return (
              <li
                key={d.id}
                className="flex items-center justify-between p-4 hover:bg-gray-50"
              >
                <a href={`/doc/${d.id}`} className="flex-1">
                  <div className="font-medium text-gray-900">
                    {d.originalFilename}
                  </div>
                  <div className="text-xs text-gray-600">
                    {pages} {pages === 1 ? 'page' : 'pages'} · Uploaded{' '}
                    {timeAgo(d.createdAt as any)}
                  </div>
                  <div className="mt-2">
                    <DocumentProgressBar
                      completed={d.completedPages}
                      total={progressTotal}
                      targetLanguage={d.targetLanguage}
                      voice={d.voice}
                    />
                  </div>
                </a>
                <a
                  href={`/doc/${d.id}`}
                  className="ml-4 inline-flex items-center justify-center rounded border border-gray-300 px-3 py-1.5 text-sm hover:bg-gray-50"
                >
                  Open
                </a>
              </li>
            );
          })}
        </ul>
      )}
    </div>
  );
}
