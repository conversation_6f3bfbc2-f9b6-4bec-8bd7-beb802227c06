import UploadForm from '@/app/components/UploadForm';
import SmoothScrollToUpload from './components/SmoothScrollToUpload';
import { Button } from '@heroui/react';

export default function LandingPage() {
  return (
    <div className="space-y-16">
      <SmoothScrollToUpload />
      {/* Hero */}
      <section className="overflow-hidden rounded-2xl bg-white shadow-sm ring-1 ring-gray-100">
        <div className="px-6 py-10 sm:px-10 sm:py-16">
          <div className="flex flex-col gap-8">
            <div className="w-full">
              <div className="inline-flex items-center gap-2 rounded-full bg-blue-50 px-3 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-100">
                AI Translation • Page-by-Page Audio
              </div>
              <h1 className="mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Transform any PDF into your personal audiobook.
              </h1>
              <p className="mt-3 text-gray-600">
                Upload a PDF, optionally translate it, and listen within minutes
                with natural, expressive Text-to-Speech. Page-by-page playback,
                chapter jumps, and smart prefetch make listening smooth.
              </p>
              {/* Removed hero CTA buttons in favor of inline upload form */}
              <div className="mt-4 text-xs text-gray-500">
                No signup required for a short preview.
              </div>
            </div>
          </div>
          <div id="upload-demo" className="mt-6 w-full">
            <UploadForm />
          </div>
        </div>
      </section>

      {/* Differentiators (What Makes Us Different) */}
      <section id="features" className="space-y-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">
            What Makes Us Different
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Upload any PDF, translate on demand, and enjoy lifelike narration
            you control.
          </p>
        </div>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {[
            {
              title: 'Universal Upload',
              desc: 'Turn your own PDFs into audio—English, Vietnamese, and more.',
              iconColor: 'text-blue-600',
              icon: (
                <path d="M7 3h6l5 5v13a1 1 0 01-1 1H7a1 1 0 01-1-1V4a1 1 0 011-1z" />
              ),
            },
            {
              title: 'AI Translation (Optional)',
              desc: 'Translate foreign-language books so you never miss out on knowledge.',
              iconColor: 'text-green-600',
              icon: (
                <>
                  <path d="M4 4h9v6H4zM4 14h6v6H4zM15 14h6v6h-6z" />
                  <path d="M16 5h4M18 3v4" />
                </>
              ),
            },
            {
              title: 'Lifelike AI Narration',
              desc: 'Natural voices that sound close to professional narrators.',
              iconColor: 'text-blue-600',
              icon: (
                <>
                  <path d="M11 5l6 3v8l-6 3V5z" />
                  <path d="M5 8v8" />
                </>
              ),
            },
            {
              title: 'Stream or Download',
              desc: 'Listen in the app or download MP3/M4A for offline.',
              iconColor: 'text-green-600',
              icon: (
                <>
                  <path d="M12 5v10" />
                  <path d="M8 11l4 4 4-4" />
                  <path d="M4 19h16" />
                </>
              ),
            },
            {
              title: 'Affordable Pricing',
              desc: 'Pay less than traditional audiobook platforms with simple hourly rates.',
              iconColor: 'text-blue-600',
              icon: (
                <>
                  <circle cx="12" cy="12" r="9" />
                  <path d="M8 12h8M12 8v8" />
                </>
              ),
            },
            {
              title: 'Designed for Books',
              desc: 'Chapter browsing, page-by-page playback, smart prefetch, sentence boundaries.',
              iconColor: 'text-green-600',
              icon: <path d="M4 6h8M4 12h12M4 18h16" />,
            },
          ].map((f) => (
            <div
              key={f.title}
              className="rounded-xl bg-white p-5 shadow-sm ring-1 ring-gray-100"
            >
              <div className="flex items-center gap-3">
                <div className="inline-flex h-10 w-10 items-center justify-center rounded-lg bg-blue-50 ring-1 ring-inset ring-blue-100">
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1.8"
                    className={`h-6 w-6 ${f.iconColor}`}
                  >
                    {f.icon}
                  </svg>
                </div>
                <div className="font-semibold text-gray-900">{f.title}</div>
              </div>
              <p className="mt-3 text-sm text-gray-600">{f.desc}</p>
            </div>
          ))}
        </div>
      </section>

      {/* Why Readers Love It */}
      <section className="rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100">
        <h2 className="text-2xl font-bold text-gray-900">
          Why Readers Love It
        </h2>
        <ul className="mt-4 grid gap-3 sm:grid-cols-2">
          {[
            {
              k: 'Control',
              v: 'Turn any book into audio, not just publisher releases.',
            },
            {
              k: 'Convenience',
              v: 'Learn anywhere—commuting, exercising, cooking, or relaxing.',
            },
            {
              k: 'Savings',
              v: 'More books per dollar vs. Audible, Kindle, or Speechify.',
            },
            {
              k: 'Productivity Boost',
              v: 'Absorb knowledge hands-free while doing other tasks.',
            },
          ].map((i) => (
            <li key={i.k} className="flex items-start gap-3">
              <span className="mt-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-green-100 text-green-700 ring-1 ring-inset ring-green-200">
                ✓
              </span>
              <div>
                <div className="font-medium text-gray-900">{i.k}</div>
                <div className="text-sm text-gray-600">{i.v}</div>
              </div>
            </li>
          ))}
        </ul>
      </section>

      {/* Pricing Plans (expanded) */}
      <section aria-labelledby="pricing" className="space-y-6">
        <div className="text-center">
          <h2 id="pricing" className="text-2xl font-bold text-gray-900">
            Pricing Plans
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Simple and transparent. Extra time billed at $5/hour.
          </p>
        </div>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {[
            {
              name: 'Basic – Entry',
              price: '$9.9 / month',
              hours: 'Includes 2 hours (≈ 1 short book)',
              extra: '$5/hour after quota',
              blurb: 'For beginners and occasional listeners.',
            },
            {
              name: 'Pro – Most Popular',
              price: '$24.9 / month',
              hours: 'Includes 6 hours (≈ 2–3 books)',
              extra: '$5/hour after quota',
              blurb: 'For busy professionals listening weekly.',
            },
            {
              name: 'Premium – Power User',
              price: '$49.9 / month',
              hours: 'Includes 12 hours (≈ 4–6 books)',
              extra: '$5/hour after quota',
              blurb: 'Best savings for avid readers.',
            },
          ].map((p) => (
            <div
              key={p.name}
              className="rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-100"
            >
              <div className="text-sm font-semibold text-blue-700">
                {p.name}
              </div>
              <div className="mt-1 text-2xl font-bold text-gray-900">
                {p.price}
              </div>
              <div className="mt-1 text-sm text-gray-600">{p.hours}</div>
              <div className="mt-1 text-xs text-gray-500">{p.extra}</div>
              <div className="mt-2 text-sm text-gray-700">{p.blurb}</div>
              <Button
                as="a"
                href="#upload-demo"
                color="primary"
                size="sm"
                className="mt-4"
              >
                Try now
              </Button>
            </div>
          ))}
        </div>
      </section>

      {/* How It Works */}
      <section className="rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100">
        <h2 className="text-2xl font-bold text-gray-900">How It Works</h2>
        <div className="mt-6">
          <div className="flex flex-col items-stretch gap-4 lg:flex-row lg:items-stretch lg:justify-start lg:gap-6">
            {[
              {
                n: '1',
                t: 'Upload your book (PDF)',
                d: 'Drag and drop—our system handles the rest.',
              },
              {
                n: '2',
                t: 'Select language & voice',
                d: 'Translate to Vietnamese or keep original; pick male/female voice.',
              },
              {
                n: '3',
                t: 'Generate audiobook',
                d: 'In minutes, text becomes natural, flowing audio.',
              },
              {
                n: '4',
                t: 'Listen or download',
                d: 'Stream instantly or download MP3/M4A for offline listening.',
              },
            ].map((s, i, arr) => (
              <div key={s.n} className="contents">
                <div className="flex min-w-0 flex-1 basis-0 flex-col">
                  <div className="h-1 w-full rounded-t-xl bg-gradient-to-r from-blue-500/20 via-green-500/20 to-blue-500/20" />
                  <div className="flex min-h-[200px] flex-1 flex-col rounded-xl bg-white p-4 shadow-sm ring-1 ring-gray-200 sm:p-5">
                    <div className="inline-flex h-9 w-9 items-center justify-center rounded-full bg-blue-600 text-sm font-bold text-white shadow">
                      {s.n}
                    </div>
                    <div className="mt-2 font-medium text-gray-900">{s.t}</div>
                    <div className="mt-1 text-sm text-gray-600">{s.d}</div>
                  </div>
                </div>
                {i < arr.length - 1 && (
                  <>
                    {/* Arrow for desktop (horizontal) */}
                    <div className="hidden w-8 shrink-0 items-center justify-center self-center lg:flex">
                      <svg
                        viewBox="0 0 24 24"
                        className="h-6 w-6 text-gray-300"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M4 12h14" />
                        <path d="M13 5l7 7-7 7" />
                      </svg>
                    </div>
                    {/* Arrow for mobile/tablet (vertical) */}
                    <div className="flex items-center justify-center self-center lg:hidden">
                      <svg
                        viewBox="0 0 24 24"
                        className="h-6 w-6 text-gray-300"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.8"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 4v14" />
                        <path d="M5 13l7 7 7-7" />
                      </svg>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Hear the Difference (placeholder) */}
      <section className="rounded-2xl bg-gradient-to-br from-blue-50 to-green-50 p-6 ring-1 ring-inset ring-gray-100">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Hear the Difference
            </h2>
            <p className="mt-1 text-sm text-gray-700">
              Try demo voices and experience narration close to human quality.
            </p>
          </div>
          <a
            href="/app"
            className="inline-flex items-center justify-center rounded-lg bg-white px-4 py-2 text-sm font-medium text-gray-900 ring-1 ring-inset ring-gray-200 hover:bg-gray-50"
          >
            Open Demo Voices
          </a>
        </div>
      </section>

      {/* Who Is It For? */}
      <section className="rounded-2xl bg-white p-6 shadow-sm ring-1 ring-gray-100">
        <h2 className="text-2xl font-bold text-gray-900">Who Is It For?</h2>
        <div className="mt-4 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {[
            "Book lovers who can't find certain titles in audio form",
            'Language learners who want bilingual texts and translations',
            'Professionals and students using commute or downtime',
            'Busy parents who want to read more with limited quiet time',
          ].map((x) => (
            <div
              key={x}
              className="rounded-lg border border-gray-200 p-4 text-sm text-gray-700"
            >
              {x}
            </div>
          ))}
        </div>
      </section>

      {/* Final CTA */}
      <section className="rounded-2xl bg-white p-6 text-center shadow-sm ring-1 ring-gray-100">
        <h2 className="text-2xl font-bold text-gray-900">
          Start Listening Smarter
        </h2>
        <p className="mt-2 text-sm text-gray-700">
          Every book you own can become an audiobook. Don&apos;t wait for
          publishers—create your own library today.
        </p>
        <Button
          as="a"
          href="#upload-demo"
          color="primary"
          size="lg"
          className="mt-5"
        >
          Get Started Now
        </Button>
      </section>
    </div>
  );
}
