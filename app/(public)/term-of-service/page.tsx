import type { Metadata } from 'next';
import Link from 'next/link';

type Section = {
  id: string;
  title: string;
  paragraphs: string[];
  bullets?: string[];
  footerNote?: string;
};

export const metadata: Metadata = {
  title: 'Terms of Service | TransReed',
  description:
    'Review the terms that govern your access to and use of the TransReed document-to-audio platform.',
};

const lastUpdated = 'October 7, 2025';

const sections: Section[] = [
  {
    id: 'agreement',
    title: 'Agreement to Terms',
    paragraphs: [
      'These Terms of Service (“Terms”) form a binding agreement between you and TransReed. By accessing or using the TransReed website, application, or related services (“Services”), you agree to these Terms and to comply with all applicable laws. If you do not agree, you must discontinue use of the Services.',
      'We may update these Terms periodically. When changes are material we will notify you by email or in-product messaging. Continued use of the Services after an update constitutes acceptance of the revised Terms.',
    ],
  },
  {
    id: 'eligibility',
    title: 'Eligibility & Account Registration',
    paragraphs: [
      'To create an account you must be at least 18 years old (or the age of majority in your jurisdiction) and able to form a legally binding contract. Creating an account on behalf of an organization confirms you are authorized to accept these Terms for that organization.',
      'You are responsible for safeguarding your credentials and for all activity that occurs under your account. Notify us immediately of any unauthorized access.',
    ],
  },
  {
    id: 'permitted-use',
    title: 'Permitted Use of the Services',
    paragraphs: [
      'Subject to these Terms, TransReed grants you a limited, non-exclusive, non-transferable license to access and use the Services for the sole purpose of converting your permitted documents into translated audio.',
    ],
    bullets: [
      'You may upload only documents you own or otherwise have the rights to use.',
      'You agree not to reverse engineer, decompile, or tamper with the Services or any underlying technology.',
      'You may not use the Services to develop competing products or to process content that violates applicable law or third-party rights.',
    ],
  },
  {
    id: 'user-content',
    title: 'User Content',
    paragraphs: [
      'You retain ownership of the content you upload. By submitting content, you grant TransReed a limited license to process, transform, and store that content as necessary to provide the Services and to generate audio output.',
      'You are solely responsible for the legality of the content you upload. Do not submit confidential information unless you are comfortable with it being processed by AI and storage providers engaged by TransReed.',
    ],
  },
  {
    id: 'subscriptions',
    title: 'Subscriptions & Payment',
    paragraphs: [
      'Some features require a paid subscription. Billing is handled by trusted payment partners and subject to their terms. Fees are non-refundable except where required by law or expressly stated by TransReed.',
      'Subscriptions renew automatically unless you cancel prior to the renewal date. Downgrading or cancellation may limit or remove access to certain features.',
    ],
  },
  {
    id: 'acceptable-use',
    title: 'Acceptable Use & Prohibited Conduct',
    paragraphs: [
      'You agree not to misuse the Services. Prohibited behavior includes, but is not limited to:',
    ],
    bullets: [
      'Uploading malicious code or attempting to disrupt or compromise infrastructure.',
      'Using the Services to infringe intellectual property, privacy, or other rights of third parties.',
      'Harassing, threatening, or abusing others, or engaging in any fraudulent or deceptive practice.',
      'Circumventing usage limits or quota enforcement.',
    ],
  },
  {
    id: 'intellectual-property',
    title: 'TransReed Intellectual Property',
    paragraphs: [
      'All rights, title, and interest in the Services, including software, documentation, branding, templates, and compilation of user content, are owned by TransReed or its licensors. Except for the limited license granted in these Terms, no rights are transferred to you.',
    ],
  },
  {
    id: 'termination',
    title: 'Suspension & Termination',
    paragraphs: [
      'We may suspend or terminate access to the Services if we reasonably believe you have violated these Terms, pose a security risk, or create liability for TransReed or others. We will use reasonable efforts to notify you of the reason for suspension unless prohibited by law.',
      'You may stop using the Services at any time. Upon termination, your right to access the Services will cease immediately. Some obligations (such as payment liabilities or indemnification duties) continue after termination.',
    ],
  },
  {
    id: 'disclaimers',
    title: 'Disclaimers',
    paragraphs: [
      'The Services are provided on an “as is” and “as available” basis without warranties of any kind, whether express, implied, or statutory. TransReed specifically disclaims implied warranties of merchantability, fitness for a particular purpose, and non-infringement.',
      'Audio output quality may vary depending on the input material and network connectivity. We do not guarantee uninterrupted or error-free operation.',
    ],
  },
  {
    id: 'liability',
    title: 'Limitation of Liability',
    paragraphs: [
      'To the maximum extent permitted by law, neither TransReed nor its suppliers will be liable for indirect, incidental, special, consequential, or punitive damages, nor for loss of profits, revenues, data, or goodwill arising from or related to your use of the Services.',
      'TransReed’s total liability for any claim arising out of or relating to these Terms is limited to the amount you paid to TransReed for the Services in the 12 months preceding the event giving rise to the claim.',
    ],
  },
  {
    id: 'indemnification',
    title: 'Indemnification',
    paragraphs: [
      'You agree to defend, indemnify, and hold harmless TransReed, its affiliates, and their respective officers, directors, employees, and agents from any claims, damages, liabilities, and expenses (including reasonable legal fees) arising from your use of the Services, your content, or your violation of these Terms.',
    ],
  },
  {
    id: 'governing-law',
    title: 'Governing Law & Dispute Resolution',
    paragraphs: [
      'These Terms are governed by the laws of Singapore, without regard to conflict-of-law principles. The parties agree to the exclusive jurisdiction of the courts located in Singapore to resolve any dispute arising out of these Terms or the Services, except where local law requires otherwise.',
    ],
  },
  {
    id: 'general',
    title: 'Miscellaneous',
    paragraphs: [
      'If any provision of these Terms is found unenforceable, the remaining provisions remain in full force. Our failure to enforce any provision is not a waiver of our right to do so later. You may not assign these Terms without our prior written consent; we may assign them as part of a merger, acquisition, or sale of assets.',
    ],
  },
  {
    id: 'contact',
    title: 'Contact Us',
    paragraphs: [
      'Questions about these Terms or your account? We are here to help.',
    ],
    footerNote: '<EMAIL>',
  },
];

export default function TermsOfServicePage() {
  return (
    <div className="space-y-10 pb-16">
      <header className="rounded-2xl bg-gradient-to-r from-slate-800 to-slate-900 p-10 text-white shadow-lg">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-end lg:justify-between">
          <div>
            <div className="inline-flex items-center rounded-full bg-white/10 px-4 py-1 text-sm font-semibold">
              Legal • Terms of Service
            </div>
            <h1 className="mt-5 text-4xl font-bold leading-tight sm:text-5xl">
              Terms of Service
            </h1>
            <p className="mt-3 max-w-2xl text-base text-white/80">
              Understand the rules that govern your use of TransReed and how we
              handle your content and data.
            </p>
          </div>
          <div className="text-sm text-white/70">
            <div className="font-semibold text-white">Last updated</div>
            <div>{lastUpdated}</div>
          </div>
        </div>
      </header>

      <section className="rounded-2xl bg-white p-10 shadow-sm ring-1 ring-gray-100">
        <div className="grid gap-12">
          {sections.map((section) => (
            <article key={section.id} id={section.id} className="space-y-4">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">
                  {section.title}
                </h2>
              </div>
              {section.paragraphs.map((paragraph, index) => (
                <p
                  key={`${section.id}-p-${index}`}
                  className="text-base text-gray-700"
                >
                  {paragraph}
                </p>
              ))}
              {section.bullets ? (
                <ul className="list-disc space-y-2 pl-6 text-base text-gray-700">
                  {section.bullets.map((bullet, index) => (
                    <li key={`${section.id}-b-${index}`}>{bullet}</li>
                  ))}
                </ul>
              ) : null}
              {section.footerNote ? (
                <p className="text-sm text-gray-600">
                  Email:{' '}
                  <Link
                    href={`mailto:${section.footerNote}`}
                    className="text-blue-600 underline decoration-blue-200 underline-offset-4 hover:text-blue-500"
                  >
                    {section.footerNote}
                  </Link>
                </p>
              ) : null}
            </article>
          ))}
        </div>
      </section>
    </div>
  );
}
