import type { Metadata } from 'next';
import Link from 'next/link';

type Section = {
  id: string;
  title: string;
  description?: string;
  paragraphs: string[];
  bullets?: string[];
  emailCta?: string;
};

export const metadata: Metadata = {
  title: 'Privacy Policy | TransReed',
  description:
    'Understand how TransReed collects, uses, and protects the data you share while turning PDFs into audio.',
};

const lastUpdated = 'October 7, 2025';

const sections: Section[] = [
  {
    id: 'overview',
    title: 'Your Privacy Matters',
    paragraphs: [
      'TransReed (“we,” “our,” “us”) is committed to respecting and protecting your privacy. This notice explains the types of data we collect, how we use them, and the choices available to you.',
      'By continuing to use our website, application, and related services (“Services”), you agree to this Privacy Policy. If you do not agree, please discontinue using the Services.',
    ],
  },
  {
    id: 'information-we-collect',
    title: 'Information We Collect',
    description:
      'We only gather data that helps us deliver and improve TransReed.',
    paragraphs: [
      'Depending on how you interact with the Services, we may collect:',
    ],
    bullets: [
      'Account information such as your email address, preferred language, and subscription tier.',
      'Billing details processed by trusted payment providers (we never store full card numbers).',
      'Documents you upload—including PDF content and associated metadata—to enable translation and audio narration.',
      'Usage metrics like device type, IP address, browser version, and listening progress to help us troubleshoot and improve features.',
      'Cookie and analytics data that personalize experiences and highlight product adoption.',
    ],
  },
  {
    id: 'how-we-use-data',
    title: 'How We Use Information',
    paragraphs: [
      'We use the data we collect to operate, secure, and evolve TransReed. Typical uses include:',
    ],
    bullets: [
      'Translating documents and generating high-quality audio output.',
      'Managing subscription limits, billing cycles, and account lifecycle tasks.',
      'Providing customer support and sending important updates. Marketing emails are sent only when you opt in.',
      'Monitoring for misuse, fraud, or security incidents.',
    ],
  },
  {
    id: 'sharing',
    title: 'How We Share Information',
    paragraphs: [
      'We never sell your personal information. Limited sharing happens only when necessary:',
    ],
    bullets: [
      'With carefully vetted infrastructure, AI, and storage partners who process data on our behalf.',
      'To comply with applicable law, regulation, or valid legal requests.',
      'As part of a corporate transaction such as a merger or acquisition, subject to appropriate safeguards.',
    ],
  },
  {
    id: 'retention',
    title: 'Data Storage & Retention',
    paragraphs: [
      'We retain information only for as long as it remains useful and lawful to do so:',
    ],
    bullets: [
      'Uploaded documents and generated audio may be cached briefly so you can re-run or revisit a project before we purge them.',
      'Account and billing records are stored while your subscription remains active and for the period required by finance or tax regulations.',
      'You can request deletion of your account and associated data at any time. Once verified, we erase non-essential records.',
    ],
  },
  {
    id: 'security',
    title: 'How We Protect Data',
    paragraphs: [
      'We apply administrative, physical, and technical safeguards to protect your data. These include encryption in transit and at rest, access controls, vulnerability management, and routine security reviews.',
      'No platform can guarantee absolute security. You acknowledge that you use the Services at your own discretion.',
    ],
  },
  {
    id: 'rights',
    title: 'Your Privacy Rights',
    paragraphs: [
      'Depending on your location—for example, the EU (GDPR) or California (CCPA)—you may have the right to exercise specific privacy controls.',
      'Submit a privacy request via email and we will respond within legally mandated timeframes while keeping you updated on progress.',
    ],
    bullets: [
      'Access, correct, or delete personal information we maintain about you.',
      'Request a portable copy of your data.',
      'Restrict or object to certain processing activities.',
      'Withdraw consent for marketing messages.',
    ],
  },
  {
    id: 'deletion',
    title: 'Account Deletion',
    paragraphs: [
      'To delete your account, contact us using the email tied to your profile. After verifying ownership, we remove personal data except where retention is legally required (for example, audited billing records).',
    ],
  },
  {
    id: 'children',
    title: 'Children’s Privacy',
    paragraphs: [
      'TransReed is not directed to children under 13 (or under 16 in the EU). If we become aware that a child has provided personal data, we delete it promptly and close the related account.',
    ],
  },
  {
    id: 'transfers',
    title: 'International Data Transfers',
    paragraphs: [
      'We may process data in countries different from where you reside. When data crosses borders, we rely on safeguards such as Standard Contractual Clauses or similar mechanisms to maintain protection.',
    ],
  },
  {
    id: 'updates',
    title: 'Policy Updates',
    paragraphs: [
      'We periodically revise this notice to reflect product changes or new legal guidance. Major updates are shared via email or in-app notifications. The date below shows when this version took effect.',
    ],
  },
  {
    id: 'contact',
    title: 'Contact Us',
    paragraphs: [
      'Questions about this Privacy Policy or your rights? Reach out and we will be happy to help.',
    ],
    emailCta: '<EMAIL>',
  },
];

export default function PrivacyPolicyPage() {
  return (
    <div className="space-y-10 pb-16">
      <header className="rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-600 p-10 text-white shadow-lg">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-end lg:justify-between">
          <div>
            <div className="inline-flex items-center rounded-full bg-white/15 px-4 py-1 text-sm font-semibold">
              Legal • Privacy Policy
            </div>
            <h1 className="mt-5 text-4xl font-bold leading-tight sm:text-5xl">
              Privacy Policy
            </h1>
            <p className="mt-3 max-w-2xl text-base text-white/80">
              Learn how TransReed collects, uses, and safeguards the data you
              share while transforming documents into audio.
            </p>
          </div>
          <div className="text-sm text-white/70">
            <div className="font-semibold text-white">Last updated</div>
            <div>{lastUpdated}</div>
          </div>
        </div>
      </header>

      <section className="rounded-2xl bg-white p-10 shadow-sm ring-1 ring-gray-100">
        <div className="grid gap-12">
          {sections.map((section) => (
            <article key={section.id} id={section.id} className="space-y-4">
              <div>
                <h2 className="text-2xl font-semibold text-gray-900">
                  {section.title}
                </h2>
                {section.description ? (
                  <p className="mt-1 text-sm text-gray-500">
                    {section.description}
                  </p>
                ) : null}
              </div>
              {section.paragraphs.map((paragraph, index) => (
                <p
                  key={`${section.id}-p-${index}`}
                  className="text-base text-gray-700"
                >
                  {paragraph}
                </p>
              ))}
              {section.bullets ? (
                <ul className="list-disc space-y-2 pl-6 text-base text-gray-700">
                  {section.bullets.map((bullet, index) => (
                    <li key={`${section.id}-b-${index}`}>{bullet}</li>
                  ))}
                </ul>
              ) : null}
              {section.emailCta ? (
                <p className="text-sm text-gray-600">
                  Email:{' '}
                  <Link
                    href={`mailto:${section.emailCta}`}
                    className="text-blue-600 underline decoration-blue-200 underline-offset-4 hover:text-blue-500"
                  >
                    {section.emailCta}
                  </Link>
                </p>
              ) : null}
            </article>
          ))}
        </div>
      </section>
    </div>
  );
}
