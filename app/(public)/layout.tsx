import { ReactNode } from 'react';

import { Navbar } from '@/app/components/Navbar';
import Footer from './components/Footer';

import Providers from './providers';

type PublicLayoutProps = {
  children: ReactNode;
};

export default function PublicLayout({ children }: PublicLayoutProps) {
  return (
    <Providers>
      <Navbar />
      <main>{children}</main>
      <Footer />
    </Providers>
  );
}
