'use client';

import Image from 'next/image';
import { SocialProof } from '../SocialProof';

export function TestimonialsSection() {
  return (
    <section className="bg-gradient-to-b from-white to-gray-50 px-4 py-16 md:px-6">
      <div className="mx-auto max-w-7xl">
        <h2 className="mb-4 text-center text-3xl font-bold md:text-5xl">
          What Our Users Say
        </h2>
        <p className="mb-16 text-center text-lg text-gray-600">
          Join thousands of readers changing their learning experience
        </p>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {[
            {
              id: 1,
              name: '<PERSON>',
              role: 'Software Engineer',
              avatar: 'https://i.pravatar.cc/150?img=47',
              content:
                "I used to struggle finishing technical books because I never had time to sit down and read. With TransReed, I've finished 15 programming books in 6 months just during my commute. Game changer!",
            },
            {
              id: 2,
              name: '<PERSON>',
              role: 'MBA Student',
              avatar: 'https://i.pravatar.cc/150?img=12',
              content:
                'The translation feature is amazing. I can now access Japanese business books that were never translated to English. TransReed opened up a whole new world of knowledge for me.',
            },
            {
              id: 3,
              name: '<PERSON>',
              role: 'Busy Mom & Business Owner',
              avatar: 'https://i.pravatar.cc/150?img=32',
              content:
                'As a mom running a startup, I have zero time to read. TransReed lets me listen while cooking, working out, or driving. I went from 2 books a year to 40+ books. My business knowledge has gone way up!',
            },
            {
              id: 4,
              name: 'David Kim',
              role: 'Language Learner',
              avatar: 'https://i.pravatar.cc/150?img=60',
              content:
                'Learning Vietnamese through audiobooks has helped my speaking a lot. I can listen to the same book in both English and Vietnamese. Perfect for learning languages!',
            },
            {
              id: 5,
              name: 'Lisa Wang',
              role: 'Product Manager',
              avatar: 'https://i.pravatar.cc/150?img=45',
              content:
                "The quality is really good - way better than other TTS services. Sometimes I forget it's AI and not a human reading. And at $25/month for unlimited books? Great deal!",
            },
            {
              id: 6,
              name: 'James Anderson',
              role: 'Medical Student',
              avatar: 'https://i.pravatar.cc/150?img=33',
              content:
                "I turn my medical textbooks to audio and listen during my hospital rounds. I've added 3 hours of study time daily without changing my schedule. Passing exams has never been easier.",
            },
          ].map((testimonial) => (
            <div
              key={testimonial.id}
              className="flex flex-col rounded-2xl bg-white p-6 shadow-lg transition-all hover:scale-105 hover:shadow-xl"
            >
              <div className="mb-4 flex items-start gap-4">
                <div className="relative h-16 w-16 flex-shrink-0 overflow-hidden rounded-full shadow-lg">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    width={64}
                    height={64}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-900">
                    {testimonial.name}
                  </h3>
                  <p className="text-sm text-gray-600">{testimonial.role}</p>
                </div>
              </div>

              <div className="mb-4 flex-1">
                <p className="leading-relaxed text-gray-700">
                  &ldquo;{testimonial.content}&rdquo;
                </p>
              </div>

              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className="h-5 w-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
            </div>
          ))}
        </div>

        <SocialProof />
      </div>
    </section>
  );
}
