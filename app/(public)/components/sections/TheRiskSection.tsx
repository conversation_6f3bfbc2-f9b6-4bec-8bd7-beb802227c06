import { CTAButton } from '../CTAButton';

export function TheRiskSection() {
  return (
    <section className="bg-gray-50 px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-16 text-center text-3xl font-bold md:text-5xl">
          The Risk You Take
        </h2>

        <div className="grid gap-8 md:grid-cols-2">
          {/* With TransReed */}
          <div className="rounded-2xl border border-gray-200 bg-white p-8 shadow-lg">
            <div className="mb-4 flex items-start gap-4">
              <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-500">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="mb-3 text-2xl font-bold text-gray-900">
                  Choose TransReed
                </h3>
                <p className="text-lg leading-relaxed text-gray-700">
                  You&apos;re sure to get hours of knowledge that you paid for.
                  Every book becomes easy learning material.
                </p>
              </div>
            </div>
          </div>

          {/* Without TransReed */}
          <div className="rounded-2xl border border-gray-200 bg-white p-8 shadow-lg">
            <div className="mb-4 flex items-start gap-4">
              <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-gray-400">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="mb-3 text-2xl font-bold text-gray-900">
                  Stay Where You Are
                </h3>
                <p className="text-lg leading-relaxed text-gray-700">
                  Without enough knowledge, success stays out of reach. Your
                  potential stays locked away.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <CTAButton size="lg" />
        </div>
      </div>
    </section>
  );
}
