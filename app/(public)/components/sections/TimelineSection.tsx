import { CTAButton } from '../CTAButton';

export function TimelineSection() {
  return (
    <section className="bg-white px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-16 text-center text-3xl font-bold md:text-5xl">
          Your Timeline with TransReed
        </h2>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 hidden h-full w-1 -translate-x-1/2 transform bg-gradient-to-b from-blue-300 via-purple-300 to-green-300 md:block"></div>

          <div className="space-y-12">
            {/* Past */}
            <div className="relative">
              <div className="md:flex md:items-center">
                <div className="mb-6 md:mb-0 md:w-1/2 md:pr-12 md:text-right">
                  <div className="mb-4 inline-block rounded-full bg-blue-100 px-4 py-2 font-bold text-blue-800">
                    Past
                  </div>
                  <h3 className="mb-3 text-2xl font-bold">
                    What Could Have Been
                  </h3>
                  <p className="text-lg text-gray-700">
                    You could have been a team leader if you had people skills
                    and leadership abilities
                  </p>
                </div>
                <div className="absolute left-1/2 hidden h-8 w-8 -translate-x-1/2 transform rounded-full border-4 border-white bg-blue-500 shadow-lg md:block"></div>
                <div className="md:w-1/2 md:pl-12"></div>
              </div>
            </div>

            {/* Present */}
            <div className="relative">
              <div className="md:flex md:items-center">
                <div className="md:w-1/2 md:pr-12"></div>
                <div className="absolute left-1/2 hidden h-8 w-8 -translate-x-1/2 transform rounded-full border-4 border-white bg-purple-500 shadow-lg md:block"></div>
                <div className="md:w-1/2 md:pl-12">
                  <div className="mb-4 inline-block rounded-full bg-purple-100 px-4 py-2 font-bold text-purple-800">
                    Present
                  </div>
                  <h3 className="mb-3 text-2xl font-bold">
                    Feel Helpful Every Day
                  </h3>
                  <p className="text-lg text-gray-700">
                    Every day you feel truly helpful because you&apos;re always
                    learning new knowledge
                  </p>
                </div>
              </div>
            </div>

            {/* Future 1 - Get Promoted */}
            <div className="relative">
              <div className="md:flex md:items-center">
                <div className="mb-6 md:mb-0 md:w-1/2 md:pr-12 md:text-right">
                  <div className="mb-4 inline-block rounded-full bg-green-100 px-4 py-2 font-bold text-green-800">
                    Future
                  </div>
                  <h3 className="mb-3 text-2xl font-bold">Get Promoted</h3>
                  <p className="text-lg text-gray-700">
                    You get promoted because you have the needed soft skills
                  </p>
                </div>
                <div className="absolute left-1/2 hidden h-8 w-8 -translate-x-1/2 transform rounded-full border-4 border-white bg-green-500 shadow-lg md:block"></div>
                <div className="md:w-1/2 md:pl-12"></div>
              </div>
            </div>

            {/* Future 2 - Start Your Own Business */}
            <div className="relative">
              <div className="md:flex md:items-center">
                <div className="md:w-1/2 md:pr-12"></div>
                <div className="absolute left-1/2 hidden h-8 w-8 -translate-x-1/2 transform rounded-full border-4 border-white bg-emerald-500 shadow-lg md:block"></div>
                <div className="md:w-1/2 md:pl-12">
                  <div className="mb-4 inline-block rounded-full bg-emerald-100 px-4 py-2 font-bold text-emerald-800">
                    Future
                  </div>
                  <h3 className="mb-3 text-2xl font-bold">
                    Start Your Own Business
                  </h3>
                  <p className="text-lg text-gray-700">
                    You have enough skills to start your own company
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12 text-center">
          <CTAButton size="lg" />
        </div>
      </div>
    </section>
  );
}
