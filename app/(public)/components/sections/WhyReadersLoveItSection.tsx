import { CTAButton } from '../CTAButton';

export function WhyReadersLoveItSection() {
  return (
    <section className="bg-gradient-to-br from-blue-50 to-purple-50 px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-4 text-center text-3xl font-bold md:text-5xl">
          Why Readers Love It
        </h2>
        <p className="mb-16 text-center text-lg text-gray-600">
          Find out what makes <PERSON><PERSON><PERSON> the perfect audiobook friend
        </p>

        <div className="grid gap-8 md:grid-cols-2">
          {[
            {
              title: 'Control',
              desc: 'Turn any book into audio, not just publisher releases.',
              icon: '🎮',
            },
            {
              title: 'Easy to Use',
              desc: 'Learn anywhere—commuting, working out, cooking, or relaxing.',
              icon: '🚀',
            },
            {
              title: 'Savings',
              desc: 'More books per dollar vs. Audible, Kindle, or Speechify.',
              icon: '💵',
            },
            {
              title: 'Get More Done',
              desc: 'Learn knowledge hands-free while doing other tasks.',
              icon: '📈',
            },
          ].map((i) => (
            <div
              key={i.title}
              className="flex items-start gap-4 rounded-2xl bg-white p-6 shadow-lg"
            >
              <div className="flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-xl bg-gradient-to-br from-green-400 to-emerald-500 text-2xl shadow-lg">
                {i.icon}
              </div>
              <div>
                <h3 className="mb-2 text-xl font-bold text-gray-900">
                  {i.title}
                </h3>
                <p className="text-gray-600">{i.desc}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <CTAButton size="lg" />
        </div>
      </div>
    </section>
  );
}
