import { SocialProof } from '../SocialProof';

export function WhoIsItForSection() {
  return (
    <section className="bg-gradient-to-br from-green-50 to-blue-50 px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-4 text-center text-3xl font-bold md:text-5xl">
          Who Is It For?
        </h2>
        <p className="mb-16 text-center text-lg text-gray-600">
          TransReed is perfect for everyone who loves to learn
        </p>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[
            {
              title: 'Book Lovers',
              desc: "Who can't find certain titles in audio form",
              icon: '📖',
              gradient: 'from-red-500 to-pink-500',
            },
            {
              title: 'Language Learners',
              desc: 'Who want bilingual texts and translations',
              icon: '🌍',
              gradient: 'from-blue-500 to-cyan-500',
            },
            {
              title: 'Workers & Students',
              desc: 'Using commute or free time to learn',
              icon: '💼',
              gradient: 'from-purple-500 to-indigo-500',
            },
            {
              title: 'Busy Parents',
              desc: 'Who want to read more with limited quiet time',
              icon: '👨‍👩‍👧‍👦',
              gradient: 'from-green-500 to-emerald-500',
            },
          ].map((x) => (
            <div
              key={x.title}
              className="rounded-2xl border-2 border-white bg-white/80 p-6 shadow-lg backdrop-blur-sm transition-all hover:scale-105 hover:shadow-xl"
            >
              <div
                className={`mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br ${x.gradient} text-3xl shadow-lg`}
              >
                {x.icon}
              </div>
              <h3 className="mb-2 text-xl font-bold text-gray-900">
                {x.title}
              </h3>
              <p className="text-gray-600">{x.desc}</p>
            </div>
          ))}
        </div>

        <SocialProof />
      </div>
    </section>
  );
}
