'use client';

import { CTAButton } from '../CTAButton';

export function DearBookLoverSection() {
  const content = [
    'Dear book lover,',
    'If you want to listen to <strong>your favorite books</strong>,',
    '<u>Anytime, anywhere</u>, in your native language,',
    'With a voice that sounds <span class="highlight">like a real person</span>',
    'Then this will be <u>the most important software</u> for your personal development,',
    "Here's why,",
    'All you need to do is <strong>upload your book</strong>,',
    'Then choose <u>the language you want to listen in</u>,',
    'AI will automatically create a perfect audiobook for you,',
    "So you can listen while you're <strong>driving</strong>,",
    'Or when your eyes are too tired from looking at screens, but you still want to listen to audiobooks to <u>keep learning</u>',
    "Or listen while riding the train, or waiting at the doctor's office,...",
    '<span class="highlight">Improve by 1% each day</span>, and after 1 year, you\'ll be <strong>37 times better</strong>',
    'How much would you invest to become <u>37 times better</u> in just one year?',
    '<span class="highlight">Get this software today</span>, so procrastination can\'t hold you back',
  ];

  return (
    <section className="bg-white px-4 py-16 md:py-24">
      <style jsx>{`
        .content-responsive {
          font-size: 22px;
          margin-bottom: 26px;
          line-height: 33px;
        }
        @media (min-width: 768px) {
          .content-responsive {
            font-size: 32px;
            margin-bottom: 43px;
            line-height: 48px;
          }
        }
        :global(.highlight) {
          background: linear-gradient(to bottom, #93c5fd, #dbeafe);
          padding: 2px 4px;
          border-radius: 3px;
        }
      `}</style>
      <div className="mx-auto" style={{ maxWidth: '800px' }}>
        {content.map((item, index) => (
          <p key={index} className="content-responsive text-gray-800">
            <span dangerouslySetInnerHTML={{ __html: item }} />
          </p>
        ))}

        <div className="mt-12 text-center">
          <CTAButton size="lg" />
        </div>
      </div>
    </section>
  );
}
