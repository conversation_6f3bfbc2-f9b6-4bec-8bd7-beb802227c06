import { SocialProof } from '../SocialProof';

export function WhatMakesDifferentSection() {
  return (
    <section id="features" className="bg-white px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-4 text-center text-3xl font-bold md:text-5xl">
          What Makes Us Different
        </h2>
        <p className="mb-16 text-center text-lg text-gray-600">
          Upload any PDF, translate when you want, and enjoy lifelike reading
          you control.
        </p>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {[
            {
              title: 'Upload Any Book',
              desc: 'Turn your own PDFs into audio—English, Vietnamese, and more.',
              icon: '📄',
              gradient: 'from-blue-500 to-cyan-500',
            },
            {
              title: 'AI Translation (Optional)',
              desc: 'Translate books in other languages so you never miss out on knowledge.',
              icon: '🌐',
              gradient: 'from-green-500 to-emerald-500',
            },
            {
              title: 'Lifelike AI Voice',
              desc: 'Natural voices that sound like real people reading.',
              icon: '🎙️',
              gradient: 'from-purple-500 to-pink-500',
            },
            {
              title: 'Stream or Download',
              desc: 'Listen in the app or download MP3/M4A for offline.',
              icon: '⬇️',
              gradient: 'from-orange-500 to-red-500',
            },
            {
              title: 'Low Prices',
              desc: 'Pay less than regular audiobook sites with simple hourly rates.',
              icon: '💰',
              gradient: 'from-yellow-500 to-orange-500',
            },
            {
              title: 'Built for Books',
              desc: 'Chapter browsing, page-by-page playback, smart loading, sentence breaks.',
              icon: '📚',
              gradient: 'from-indigo-500 to-blue-500',
            },
          ].map((f) => (
            <div
              key={f.title}
              className="group rounded-2xl border-2 border-gray-200 bg-white p-6 shadow-lg transition-all hover:scale-105 hover:shadow-xl"
            >
              <div
                className={`mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br ${f.gradient} text-3xl shadow-lg`}
              >
                {f.icon}
              </div>
              <h3 className="mb-2 text-xl font-bold text-gray-900">
                {f.title}
              </h3>
              <p className="text-gray-600">{f.desc}</p>
            </div>
          ))}
        </div>

        <SocialProof />
      </div>
    </section>
  );
}
