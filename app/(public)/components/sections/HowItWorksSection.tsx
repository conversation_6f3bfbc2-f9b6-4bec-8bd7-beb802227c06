import { CTAButton } from '../CTAButton';

export function HowItWorksSection() {
  const steps = [
    {
      step: '1',
      title: 'Upload your book (PDF)',
      desc: 'Drag and drop—our system handles the rest.',
      icon: '📤',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      step: '2',
      title: 'Select language & voice',
      desc: 'Translate to Vietnamese or keep original; pick male/female voice.',
      icon: '🗣️',
      color: 'from-purple-500 to-pink-500',
    },
    {
      step: '3',
      title: 'Create audiobook',
      desc: 'In minutes, text becomes natural audio.',
      icon: '⚡',
      color: 'from-orange-500 to-red-500',
    },
    {
      step: '4',
      title: 'Listen or download',
      desc: 'Stream instantly or download MP3/M4A for offline listening.',
      icon: '🎧',
      color: 'from-green-500 to-emerald-500',
    },
  ];

  return (
    <section className="bg-white px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-4 text-center text-3xl font-bold md:text-5xl">
          How It Works
        </h2>
        <p className="mb-16 text-center text-lg text-gray-600">
          From PDF to audiobook in 4 simple steps
        </p>

        <div className="relative">
          {/* Connection line for desktop */}
          <div className="absolute left-0 right-0 top-8 hidden h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 md:block"></div>

          <div className="grid gap-8 md:grid-cols-4">
            {steps.map((s) => (
              <div key={s.step} className="relative">
                <div className="relative z-10 flex flex-col items-center text-center">
                  <div
                    className={`mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br ${s.color} text-2xl font-bold text-white shadow-xl`}
                  >
                    {s.step}
                  </div>
                  <div
                    className={`mb-4 flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br ${s.color} text-4xl shadow-lg`}
                  >
                    {s.icon}
                  </div>
                  <h3 className="mb-2 text-lg font-bold text-gray-900">
                    {s.title}
                  </h3>
                  <p className="text-sm text-gray-600">{s.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-12 text-center">
          <CTAButton size="lg" />
        </div>
      </div>
    </section>
  );
}
