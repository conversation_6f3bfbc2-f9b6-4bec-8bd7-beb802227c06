import { SocialProof } from '../SocialProof';

export function WhatHappensNextSection() {
  return (
    <section className="bg-white px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-16 text-center text-3xl font-bold md:text-5xl">
          What Happens Next?
        </h2>

        <div className="grid gap-8 md:grid-cols-2">
          {/* Yes - With TransReed */}
          <div className="rounded-2xl border-2 border-green-200 bg-gradient-to-br from-green-50 to-emerald-50 p-8">
            <div className="mb-4 flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-500">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-green-900">
                With TransReed
              </h3>
            </div>
            <p className="text-lg leading-relaxed text-gray-700">
              Listen to your favorite books in your language, anywhere you go.
              Turn your commute, workout, or daily routine into learning
              opportunities.
            </p>
          </div>

          {/* No - Without TransReed */}
          <div className="rounded-2xl border-2 border-red-200 bg-gradient-to-br from-red-50 to-rose-50 p-8">
            <div className="mb-4 flex items-center gap-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-500">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-red-900">
                Without TransReed
              </h3>
            </div>
            <p className="text-lg leading-relaxed text-gray-700">
              Never find time to learn new knowledge. Your books gather dust
              while opportunities pass you by.
            </p>
          </div>
        </div>

        <SocialProof />
      </div>
    </section>
  );
}
