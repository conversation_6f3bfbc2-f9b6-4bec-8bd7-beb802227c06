'use client';

import { useState } from 'react';
import UploadForm from '@/app/components/UploadForm';
import InPlacePlayer from '@/app/components/InPlacePlayer';

export function HeroSection() {
  const [uploadedDocId, setUploadedDocId] = useState<string | null>(null);
  return (
    <section className="relative flex min-h-[90vh] items-center overflow-hidden bg-gradient-to-b from-blue-600 via-blue-500 to-green-500">
      {/* Decorative stripes */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-transparent">
          <span className="absolute left-0 top-0 h-px w-full rotate-12 transform bg-white opacity-10"></span>
          <span className="absolute left-0 top-8 h-1 w-full rotate-12 transform bg-cyan-400 opacity-80"></span>
          <span className="absolute left-0 top-16 h-px w-full rotate-12 transform bg-purple-400 opacity-60"></span>
          <span className="absolute left-0 top-32 h-1 w-full rotate-12 transform bg-green-400 opacity-70"></span>
        </div>
      </div>

      <div className="relative z-10 w-full py-16">
        {/* Headline Section */}
        <div className="mb-12 w-full px-4">
          <div className="mx-auto text-center" style={{ maxWidth: '1200px' }}>
            {/* Badge */}
            <div className="mb-8">
              <div className="inline-flex items-center gap-2 rounded-full bg-white/20 px-4 py-2 text-sm font-medium text-white backdrop-blur-sm">
                <span className="relative flex h-2 w-2">
                  <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-white opacity-75"></span>
                  <span className="relative inline-flex h-2 w-2 rounded-full bg-white"></span>
                </span>
                AI-Powered Translation • Natural Voice
              </div>
            </div>

            {/* Main Headline */}
            <h1
              className="mb-8 text-3xl font-black uppercase leading-relaxed text-white md:text-6xl md:tracking-tighter lg:text-7xl"
              style={{
                lineHeight: '1.2',
                textShadow: '2px 2px 8px rgba(0, 0, 0, 0.5)',
              }}
            >
              How to turn <br /> any book into audiobook, <br /> in your native
              language, <br /> in few clicks
            </h1>

            <p className="mx-auto mb-8 max-w-3xl text-xl text-white/90 md:text-2xl">
              Turn any PDF into an audiobook with AI translation and natural
              voice
            </p>
          </div>
        </div>

        {/* Upload Form / Player - Full width with padding */}
        <div className="mb-12 w-full px-4">
          <div
            id="upload-demo"
            className="mx-auto"
            style={{ maxWidth: '1200px' }}
          >
            {uploadedDocId ? (
              <InPlacePlayer docId={uploadedDocId} />
            ) : (
              <UploadForm onSuccess={(docId) => setUploadedDocId(docId)} />
            )}
          </div>
        </div>

        {/* Note */}
        {!uploadedDocId && (
          <div className="w-full px-4">
            <div
              className="mx-auto text-center text-sm text-white/70"
              style={{ maxWidth: '1200px' }}
            >
              No signup required for a short preview.
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
