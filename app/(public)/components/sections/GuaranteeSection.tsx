export function GuaranteeSection() {
  return (
    <section className="bg-gradient-to-br from-green-50 to-emerald-50 px-4 py-16 md:px-6">
      <div className="mx-auto max-w-4xl">
        <div className="rounded-3xl border-2 border-green-200 bg-white p-8 shadow-2xl md:p-12">
          <div className="text-center">
            {/* Badge */}
            <div className="mb-6 flex justify-center">
              <div className="inline-flex items-center gap-2 rounded-full bg-green-100 px-6 py-3">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
                <span className="text-lg font-bold text-green-800">
                  100% Money-Back Guarantee
                </span>
              </div>
            </div>

            {/* Main Content */}
            <h2 className="mb-6 text-3xl font-bold text-gray-900 md:text-4xl">
              Try TransReed Risk-Free for 30 Days
            </h2>
            <p className="mb-4 text-xl leading-relaxed text-gray-700">
              We&apos;re so sure you&apos;ll love TransReed that we offer a{' '}
              <strong className="text-green-600">
                30-day money-back guarantee
              </strong>
              .
            </p>
            <p className="mb-8 text-lg leading-relaxed text-gray-600">
              If you&apos;re not happy with your plan for any reason, just let
              us know within 30 days and we&apos;ll give you a full refund.{' '}
              <strong>No questions asked.</strong>
            </p>

            {/* Features Grid */}
            <div className="mt-10 grid gap-6 md:grid-cols-3">
              <div className="rounded-xl bg-green-50 p-6">
                <div className="mb-3 text-4xl">✓</div>
                <h3 className="mb-2 text-lg font-bold text-gray-900">
                  Full Refund
                </h3>
                <p className="text-sm text-gray-600">
                  Get 100% of your money back within 30 days
                </p>
              </div>
              <div className="rounded-xl bg-green-50 p-6">
                <div className="mb-3 text-4xl">🚫</div>
                <h3 className="mb-2 text-lg font-bold text-gray-900">
                  No Questions Asked
                </h3>
                <p className="text-sm text-gray-600">
                  We respect your choice, no need to explain
                </p>
              </div>
              <div className="rounded-xl bg-green-50 p-6">
                <div className="mb-3 text-4xl">⚡</div>
                <h3 className="mb-2 text-lg font-bold text-gray-900">
                  Quick Process
                </h3>
                <p className="text-sm text-gray-600">
                  Simple refund process, money back in 5-7 days
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
