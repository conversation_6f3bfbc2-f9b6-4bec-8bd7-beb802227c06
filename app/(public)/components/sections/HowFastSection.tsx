import { CTAButton } from '../CTAButton';

export function HowFastSection() {
  return (
    <section className="bg-white px-4 py-16 md:px-6">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-16 text-center text-3xl font-bold md:text-5xl">
          How Fast Can You Learn?
        </h2>

        <div className="grid gap-8 md:grid-cols-2">
          {/* With TransReed */}
          <div className="rounded-2xl border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50 p-8">
            <div className="mb-6">
              <div className="mb-2 text-6xl font-black text-blue-600">52</div>
              <div className="text-xl font-semibold text-gray-700">
                books per year
              </div>
            </div>
            <h3 className="mb-3 text-2xl font-bold text-gray-900">
              With TransReed
            </h3>
            <p className="text-lg leading-relaxed text-gray-700">
              Finish one book every week. That&apos;s 52 books in a year.
              Imagine the knowledge you&apos;ll gain, the skills you&apos;ll
              learn, and the person you&apos;ll become.
            </p>
          </div>

          {/* Without TransReed */}
          <div className="rounded-2xl border-2 border-gray-300 bg-gradient-to-br from-gray-50 to-slate-50 p-8">
            <div className="mb-6">
              <div className="mb-2 text-6xl font-black text-gray-400">0-3</div>
              <div className="text-xl font-semibold text-gray-600">
                books per year
              </div>
            </div>
            <h3 className="mb-3 text-2xl font-bold text-gray-900">
              Without TransReed
            </h3>
            <p className="text-lg leading-relaxed text-gray-700">
              Months to finish one book, or too busy to finish any. An entire
              year passes with zero books done.
            </p>
          </div>
        </div>

        <div className="mt-12 text-center">
          <CTAButton size="lg" />
        </div>
      </div>
    </section>
  );
}
