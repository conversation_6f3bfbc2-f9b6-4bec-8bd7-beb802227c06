'use client';

import { Accordion, AccordionItem } from '@heroui/react';

export function FAQsSection() {
  const faqs = [
    {
      question: 'How does TransReed work?',
      answer:
        'Simply upload your PDF book, choose your target language (or keep the original), select a voice, and our AI will create a natural-sounding audiobook for you in minutes. You can listen online or download the audio files.',
    },
    {
      question: 'What languages do you support?',
      answer:
        'We support Vietnamese, English, and many other major languages. You can keep the original language or translate to your preferred language for listening.',
    },
    {
      question: 'How good is the AI voice quality?',
      answer:
        'Our AI voices sound very natural and lifelike, similar to real people reading. Many users forget they are listening to AI-generated audio. You can choose between male and female voices.',
    },
    {
      question: 'Can I try it for free?',
      answer:
        'Yes! You get 5 minutes of free listening without signing up. After creating a free account, you get another 5 minutes (10 minutes total). This lets you test the quality before buying.',
    },
    {
      question: 'What file formats can I upload?',
      answer:
        'Currently, we support PDF files. We are working on adding support for other formats like EPUB and MOBI in the future.',
    },
    {
      question: 'How long does it take to create an audiobook?',
      answer:
        'Most books are ready in just a few minutes, depending on the length. A typical 200-page book takes about 5-10 minutes to process.',
    },
    {
      question: 'Can I download the audiobooks?',
      answer:
        'Yes! You can stream online or download MP3/M4A files for offline listening on any device. Perfect for commutes or when you have no internet.',
    },
    {
      question: 'What if I am not happy with my plan?',
      answer:
        'We offer a 30-day money-back guarantee. If you are not satisfied for any reason, just let us know within 30 days and we will give you a full refund. No questions asked.',
    },
    {
      question:
        'How is this different from Audible or other audiobook services?',
      answer:
        'Unlike Audible, you can turn ANY book into an audiobook - not just what publishers offer. You can also translate books to your language. Plus, our pricing is much lower: pay for listening time instead of per book.',
    },
    {
      question: 'Is there a limit on book length or number of books?',
      answer:
        'With paid plans, you get listening hours per month (1-15 hours depending on your plan). There is no limit on how many books you can create - only on total listening time. Unused hours do not roll over.',
    },
  ];

  return (
    <section id="faqs" className="bg-white px-4 py-16 md:px-6">
      <div className="mx-auto max-w-4xl">
        <h2 className="mb-4 text-center text-3xl font-bold md:text-5xl">
          Frequently Asked Questions
        </h2>
        <p className="mb-12 text-center text-lg text-gray-600">
          Got questions? We have got answers.
        </p>

        <Accordion
          variant="splitted"
          defaultExpandedKeys={['0']}
          className="gap-4"
        >
          {faqs.map((faq, index) => (
            <AccordionItem
              key={index}
              aria-label={faq.question}
              title={
                <h3 className="text-lg font-bold text-gray-900">
                  {faq.question}
                </h3>
              }
              className="rounded-2xl border-2 border-gray-200 bg-white px-6 shadow-sm"
            >
              <p className="pb-4 leading-relaxed text-gray-700">{faq.answer}</p>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}
