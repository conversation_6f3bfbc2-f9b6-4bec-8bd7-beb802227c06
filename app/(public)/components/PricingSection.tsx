'use client';

import { Button } from '@heroui/react';
import { useState } from 'react';

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  hours: number;
  minutes: number;
  pricePerHour?: number;
  isPopular?: boolean;
  features: string[];
  cta: string;
}

export function PricingSection() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);

  const plans: PricingPlan[] = [
    {
      id: 'free',
      name: 'Free',
      price: 0,
      hours: 0,
      minutes: 5,
      features: [
        '5 minutes listening per month',
        'AI translation to any language',
        'Natural voice synthesis',
        'Page-by-page playback',
        'No credit card required',
      ],
      cta: 'Try Free',
    },
    {
      id: 'starter',
      name: 'Starter',
      price: 5,
      hours: 1,
      minutes: 60,
      pricePerHour: 5,
      features: [
        '1 hour listening per month',
        'Everything in Free',
        'Chapter navigation',
        'Playback speed control',
        'Buy more: $5 per hour',
      ],
      cta: 'Get Started',
    },
    {
      id: 'pro',
      name: 'Pro',
      price: 19,
      hours: 5,
      minutes: 300,
      pricePerHour: 5,
      isPopular: true,
      features: [
        '5 hours listening per month',
        'Everything in Starter',
        'Priority processing',
        'Download audio files',
        'Buy more: $5 per hour',
      ],
      cta: 'Go Pro',
    },
    {
      id: 'premium',
      name: 'Premium',
      price: 49,
      hours: 15,
      minutes: 900,
      pricePerHour: 5,
      features: [
        '15 hours listening per month',
        'Everything in Pro',
        'Fastest processing',
        'Premium voice options',
        'Buy more: $5 per hour',
      ],
      cta: 'Get Premium',
    },
  ];

  return (
    <section id="pricing" className="bg-gray-50 px-4 py-16 md:px-6">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-12 text-center">
          <p className="mb-2 text-sm font-semibold uppercase tracking-wide text-blue-600">
            Pricing
          </p>
          <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-5xl">
            Choose Your Perfect Plan
          </h2>
          <p className="mx-auto max-w-2xl text-lg text-gray-600">
            Start free, upgrade anytime. All paid plans include the option to
            buy additional hours at $5/hour.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 lg:gap-8">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative rounded-2xl ${
                plan.isPopular
                  ? 'transform bg-gradient-to-br from-blue-600 to-green-600 text-white shadow-2xl lg:scale-105'
                  : 'border-2 border-gray-200 bg-white shadow-lg'
              } overflow-hidden transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl`}
            >
              {/* Popular Badge */}
              {plan.isPopular && (
                <div className="absolute right-0 top-0 rounded-bl-lg bg-yellow-400 px-4 py-1 text-xs font-bold text-gray-900">
                  MOST POPULAR
                </div>
              )}

              <div className="p-6 lg:p-8">
                {/* Plan Name */}
                <h3
                  className={`mb-2 text-2xl font-black uppercase ${
                    plan.isPopular ? 'text-white' : 'text-gray-900'
                  }`}
                >
                  {plan.name}
                </h3>

                {/* Price */}
                <div className="mb-6">
                  <div className="flex items-baseline gap-2">
                    <span
                      className={`text-5xl font-black ${
                        plan.isPopular ? 'text-white' : 'text-gray-900'
                      }`}
                    >
                      ${plan.price}
                    </span>
                    {plan.price > 0 && (
                      <span
                        className={`text-lg ${
                          plan.isPopular ? 'text-white/80' : 'text-gray-500'
                        }`}
                      >
                        /month
                      </span>
                    )}
                  </div>
                  <div
                    className={`mt-1 text-sm font-semibold ${
                      plan.isPopular ? 'text-white/90' : 'text-gray-600'
                    }`}
                  >
                    {plan.hours > 0
                      ? `${plan.hours} hour${plan.hours > 1 ? 's' : ''} listening`
                      : `${plan.minutes} minutes listening`}
                  </div>
                </div>

                {/* Features */}
                <ul className="mb-8 space-y-3">
                  {plan.features.map((feature, idx) => (
                    <li
                      key={idx}
                      className={`flex items-start gap-2 text-sm ${
                        plan.isPopular ? 'text-white' : 'text-gray-700'
                      }`}
                    >
                      <svg
                        className={`mt-0.5 h-5 w-5 flex-shrink-0 ${
                          plan.isPopular ? 'text-white' : 'text-green-500'
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={3}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="leading-tight">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <Button
                  fullWidth
                  size="lg"
                  className={`text-lg font-bold ${
                    plan.isPopular
                      ? 'bg-white text-blue-600 hover:bg-gray-100'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                  onPress={() => setSelectedPlan(plan.id)}
                >
                  {plan.cta}
                </Button>

                {/* Add-on Note */}
                {plan.pricePerHour && (
                  <div
                    className={`mt-4 text-center text-xs font-medium ${
                      plan.isPopular ? 'text-white/80' : 'text-gray-500'
                    }`}
                  >
                    + Buy more hours anytime
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <p className="mb-6 text-lg text-gray-600">
            Not sure which plan is right for you?
          </p>
          <Button
            as="a"
            href="#upload-demo"
            size="lg"
            color="primary"
            className="px-8 text-lg font-bold"
          >
            Start with Free Trial
          </Button>
        </div>
      </div>
    </section>
  );
}
