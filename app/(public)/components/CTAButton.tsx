'use client';

import { But<PERSON> } from '@heroui/react';
import { SignUpButton } from '@clerk/nextjs';

interface CTAButtonProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function CTAButton({ size = 'lg', className = '' }: CTAButtonProps) {
  return (
    <SignUpButton mode="modal">
      <Button
        color="primary"
        size={size}
        className={`px-12 py-8 text-xl font-bold ${className}`}
      >
        Get Started Free Now
      </Button>
    </SignUpButton>
  );
}
