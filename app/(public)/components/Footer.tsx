import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-black text-white">
      <div className="mx-auto max-w-7xl px-6 py-16">
        <div className="grid grid-cols-1 gap-12 md:grid-cols-3">
          {/* Company Info with Map below */}
          <div className="md:col-span-1">
            <div className="text-2xl font-bold text-white">TransReed</div>
            <p className="mt-3 text-base text-gray-300">
              Turn your PDFs into audiobooks with AI
            </p>

            {/* Google Map */}
            <div className="mt-8">
              <div className="mb-4 text-base font-semibold text-white">
                Our Location
              </div>
              <div className="overflow-hidden rounded-2xl">
                <iframe
                  title="TransReed Office Location"
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3724.1453459962418!2d105.81800897419555!3d21.026869580622012!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab71774a9775%3A0x693540b959c36b2b!2zRDEwIFAuIEdp4bqjbmcgVsO1LCBHaeG6o25nIFbDtSwgQmEgxJDDrG5oLCBIw6AgTuG7mWksIFZpZXRuYW0!5e0!3m2!1sen!2s!4v1761295912232!5m2!1sen!2s"
                  width="100%"
                  height="250"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <div className="mb-4 text-base font-semibold text-white">
              Contact Us
            </div>
            <div className="space-y-4 text-base text-gray-300">
              <div className="flex items-start gap-3">
                <svg
                  className="mt-1 h-5 w-5 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  />
                </svg>
                <span>0968 081 105</span>
              </div>
              <div className="flex items-start gap-3">
                <svg
                  className="mt-1 h-5 w-5 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
                <span><EMAIL></span>
              </div>
              <div className="flex items-start gap-3">
                <svg
                  className="mt-1 h-5 w-5 flex-shrink-0"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span>D10 Giảng Võ, Ba Đình, Hà Nội, Việt Nam</span>
              </div>
            </div>
          </div>

          {/* Links */}
          <div>
            <div className="mb-4 text-base font-semibold text-white">
              Quick Links
            </div>
            <div className="flex flex-col gap-3 text-base">
              <Link
                href="/privacy-policy"
                className="text-gray-300 transition-colors hover:text-white"
              >
                Privacy Policy
              </Link>
              <Link
                href="/term-of-service"
                className="text-gray-300 transition-colors hover:text-white"
              >
                Terms of Service
              </Link>
              <Link
                href="/docs"
                className="text-gray-300 transition-colors hover:text-white"
              >
                Documentation
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400">
          © {new Date().getFullYear()} TransReed. All rights reserved.
        </div>
      </div>
    </footer>
  );
}
