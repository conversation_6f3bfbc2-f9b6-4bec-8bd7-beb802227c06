'use client';

import { useEffect } from 'react';

export default function SmoothScrollToUpload() {
  useEffect(() => {
    const handler = (e: Event) => {
      const el = document.getElementById('upload-demo');
      if (!el) return;
      e.preventDefault();
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
      try {
        window.history.replaceState(null, '', '#upload-demo');
      } catch {}
    };

    const links = Array.from(
      document.querySelectorAll('a[href="#upload-demo"]'),
    ) as HTMLAnchorElement[];
    links.forEach((link) => link.addEventListener('click', handler));

    if (
      typeof window !== 'undefined' &&
      window.location.hash === '#upload-demo'
    ) {
      const el = document.getElementById('upload-demo');
      if (el)
        setTimeout(
          () => el.scrollIntoView({ behavior: 'smooth', block: 'center' }),
          0,
        );
    }

    return () => {
      links.forEach((link) => link.removeEventListener('click', handler));
    };
  }, []);

  return null;
}
