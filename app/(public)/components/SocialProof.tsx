'use client';

import Image from 'next/image';

export function SocialProof() {
  return (
    <div className="mt-8 flex flex-col items-center justify-center gap-3 md:flex-row">
      {/* Avatars */}
      <div className="flex -space-x-3">
        <div className="h-12 w-12 overflow-hidden rounded-full border-2 border-white">
          <Image
            src="https://i.pravatar.cc/150?img=47"
            alt="User avatar"
            width={48}
            height={48}
            className="h-full w-full object-cover"
          />
        </div>
        <div className="h-12 w-12 overflow-hidden rounded-full border-2 border-white">
          <Image
            src="https://i.pravatar.cc/150?img=12"
            alt="User avatar"
            width={48}
            height={48}
            className="h-full w-full object-cover"
          />
        </div>
        <div className="h-12 w-12 overflow-hidden rounded-full border-2 border-white">
          <Image
            src="https://i.pravatar.cc/150?img=32"
            alt="User avatar"
            width={48}
            height={48}
            className="h-full w-full object-cover"
          />
        </div>
        <div className="h-12 w-12 overflow-hidden rounded-full border-2 border-white">
          <Image
            src="https://i.pravatar.cc/150?img=60"
            alt="User avatar"
            width={48}
            height={48}
            className="h-full w-full object-cover"
          />
        </div>
        <div className="h-12 w-12 overflow-hidden rounded-full border-2 border-white">
          <Image
            src="https://i.pravatar.cc/150?img=45"
            alt="User avatar"
            width={48}
            height={48}
            className="h-full w-full object-cover"
          />
        </div>
      </div>

      {/* Rating and Text */}
      <div className="flex flex-col items-center md:items-start">
        {/* 5 Stars */}
        <div className="mb-1 flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <svg
              key={star}
              className="h-5 w-5 text-yellow-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
        </div>

        <p className="text-sm text-gray-600">
          Trusted by <span className="font-bold">158</span> users
        </p>
      </div>
    </div>
  );
}
