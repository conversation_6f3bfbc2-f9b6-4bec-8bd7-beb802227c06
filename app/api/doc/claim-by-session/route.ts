import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { ensureUserByClerkId } from '@/lib/user';
import { addListeningSecondsForUser } from '@/lib/usage';
import { logger } from '@/lib/logger';

// Claim all anonymous documents bound to any of the provided session IDs.
// POST { sids: string[] }
export async function POST(req: Request) {
  const { userId: clerkUserId } = await auth();
  if (!clerkUserId)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

  let sids: string[] = [];
  try {
    const body = await req.json().catch(() => ({}));
    const arr = (body?.sids ?? []) as unknown;
    if (Array.isArray(arr))
      sids = arr.filter((x) => typeof x === 'string' && x.length > 0);
  } catch {}
  if (sids.length === 0) return NextResponse.json({ ok: true, claimed: [] });

  const dbUserId = await ensureUserByClerkId(clerkUserId);
  const docs = await prisma.document.findMany({
    where: { userId: null, sessionId: { in: sids } },
  });
  const claimed: string[] = [];
  let totalListeningTimeSec = 0;

  for (const d of docs) {
    try {
      // Calculate total duration of generated audio for this document
      // Only count PageAudio matching the document's language and voice
      try {
        const whereClause: any = {
          page: { documentId: d.id },
          s3KeyAudioMp3: { not: null },
        };

        // Filter by language and voice if set
        if (d.targetLanguage) {
          whereClause.language = d.targetLanguage;
        }
        if (d.voice) {
          whereClause.voice = d.voice;
        }

        const audioRecords = await prisma.pageAudio.findMany({
          where: whereClause,
          select: { duration: true },
        });

        const docDurationSec = audioRecords.reduce(
          (sum, record) => sum + (record.duration || 0),
          0,
        );
        totalListeningTimeSec += docDurationSec;

        logger.log('[claim-by-session] Calculated duration for', d.id, {
          duration: docDurationSec,
          language: d.targetLanguage,
          voice: d.voice,
          audioRecords: audioRecords.length,
        });
      } catch (error) {
        logger.error(
          '[claim-by-session] Failed to calculate duration for',
          d.id,
          error,
        );
      }

      await prisma.document.update({
        where: { id: d.id },
        data: { userId: dbUserId, sessionId: null },
      });
      claimed.push(d.id);
    } catch {}
  }

  // Add total listening time across all claimed documents
  if (totalListeningTimeSec > 0) {
    try {
      await addListeningSecondsForUser(dbUserId, totalListeningTimeSec);
      logger.log('[claim-by-session] Added listening time', {
        userId: dbUserId,
        documentsClaimed: claimed.length,
        totalDurationSec: totalListeningTimeSec,
      });
    } catch (error) {
      logger.error('[claim-by-session] Failed to add listening time', error);
      // Don't fail the claim if usage tracking fails
    }
  }

  return NextResponse.json({ ok: true, claimed });
}

export const runtime = 'nodejs';
