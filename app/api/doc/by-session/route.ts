import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@clerk/nextjs/server';

export async function GET(req: Request) {
  // If the caller is signed in, never redirect by anonymous session
  try {
    const { userId } = await auth();
    if (userId) return NextResponse.json({ ok: true, docId: null });
  } catch {}
  const { searchParams } = new URL(req.url);
  // Support multiple sid params or comma-separated values; check most recent doc among them
  const sidsRaw = searchParams.getAll('sid');
  const sids: string[] = [];
  for (const s of sidsRaw) {
    if (!s) continue;
    for (const part of s
      .split(',')
      .map((x) => x.trim())
      .filter(Boolean))
      sids.push(part);
  }
  const unique = Array.from(new Set(sids));
  if (unique.length === 0)
    return NextResponse.json({ error: 'Missing sid' }, { status: 400 });
  const doc = await prisma.document.findFirst({
    where: { sessionId: { in: unique } },
    orderBy: { createdAt: 'desc' },
  });
  if (!doc) return NextResponse.json({ ok: true, docId: null });

  // Optional normalization: allow caller to ask us to rebind the sessionId
  const normalizeTo = searchParams.get('normalizeTo');
  if (
    normalizeTo &&
    normalizeTo !== doc.sessionId &&
    (doc as any).userId == null
  ) {
    try {
      await prisma.document.update({
        where: { id: doc.id },
        data: { sessionId: normalizeTo },
      });
    } catch {}
  }
  return NextResponse.json({
    ok: true,
    docId: doc.id,
    sessionId: doc.sessionId,
  });
}

export const runtime = 'nodejs';
