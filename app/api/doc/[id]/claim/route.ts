import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { ensureUserByClerkId } from '@/lib/user';
import { addListeningSecondsForUser } from '@/lib/usage';
import { logger } from '@/lib/logger';

// Claim ownership of an anonymously uploaded document.
// POST body: { sids?: string[] } where sids include anon candidates (stableId/fpjs/coarse)
export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { userId: clerkUserId } = await auth();
  if (!clerkUserId)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

  const { id } = await params;
  let sids: string[] = [];
  try {
    const body = await req.json().catch(() => ({}));
    const arr = (body?.sids ?? []) as unknown;
    if (Array.isArray(arr))
      sids = arr.filter((x) => typeof x === 'string' && x.length > 0);
  } catch {}

  const doc = await prisma.document.findUnique({ where: { id } });
  if (!doc) return NextResponse.json({ error: 'Not found' }, { status: 404 });

  // Already owned — nothing to do; but only allow if it's already owned by this Clerk user
  if (doc.userId) {
    // Optionally check if owned by the same person; otherwise deny
    const owner = await prisma.user.findUnique({ where: { id: doc.userId } });
    if (owner?.clerkUserId === clerkUserId) {
      return NextResponse.json({
        ok: true,
        claimed: false,
        alreadyOwned: true,
      });
    }
    return NextResponse.json({ error: 'Already owned' }, { status: 403 });
  }

  // If sessionId is present, require it to match one of provided sids
  if (doc.sessionId) {
    const match = sids.length > 0 && sids.includes(doc.sessionId);
    if (!match) {
      return NextResponse.json({ error: 'Session mismatch' }, { status: 403 });
    }
  }

  // Ensure DB user exists and assign ownership; release from sessionId
  const dbUserId = await ensureUserByClerkId(clerkUserId);

  // Calculate total duration of generated audio for this document
  // and add it to the user's listening time
  // Only count PageAudio matching the document's language and voice
  try {
    // Get document's target language and voice
    const document = await prisma.document.findUnique({
      where: { id },
      select: { targetLanguage: true, voice: true },
    });

    if (document) {
      const whereClause: any = {
        page: { documentId: id },
        s3KeyAudioMp3: { not: null },
      };

      // Filter by language and voice if set
      if (document.targetLanguage) {
        whereClause.language = document.targetLanguage;
      }
      if (document.voice) {
        whereClause.voice = document.voice;
      }

      const audioRecords = await prisma.pageAudio.findMany({
        where: whereClause,
        select: { duration: true, language: true, voice: true },
      });

      const totalDurationSec = audioRecords.reduce(
        (sum, record) => sum + (record.duration || 0),
        0,
      );

      if (totalDurationSec > 0) {
        await addListeningSecondsForUser(dbUserId, totalDurationSec);
        logger.log('[claim] Added listening time', {
          documentId: id,
          userId: dbUserId,
          durationSec: totalDurationSec,
          language: document.targetLanguage,
          voice: document.voice,
          audioRecords: audioRecords.length,
        });
      }
    }
  } catch (error) {
    logger.error('[claim] Failed to add listening time', error);
    // Don't fail the claim if usage tracking fails
  }

  await prisma.document.update({
    where: { id },
    data: { userId: dbUserId, sessionId: null },
  });
  return NextResponse.json({ ok: true, claimed: true });
}

export const runtime = 'nodejs';
