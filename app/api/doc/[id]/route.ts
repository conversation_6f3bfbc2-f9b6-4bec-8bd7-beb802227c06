import { NextResponse } from 'next/server';
import { getDocument, getPages, getChapters } from '@/lib/db';
import { authorizeDocumentAccess } from '@/lib/authz';
import { fileExistsFlexible } from '@/lib/file-service';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
  const doc = await getDocument(id);
  if (!doc) return NextResponse.json({ error: 'Not found' }, { status: 404 });
  // Enforce: must be owner (signed-in) or anonymous session matches doc.sessionId
  const allowed = await authorizeDocumentAccess(req, {
    id: doc.id,
    userId: doc.userId,
    sessionId: doc.sessionId,
  });
  if (!allowed)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  const pages = await getPages(id);
  const chapters = await getChapters(id);

  // Check if first page audio is ready (has at least chunk-1.mp3)
  let firstPageAudioReady = false;
  if (doc.suggestedStartPageId && doc.targetLanguage && doc.voice) {
    const startPage = pages.find((p) => p.id === doc.suggestedStartPageId);
    if (startPage) {
      const chunkPath = [
        'documents',
        doc.id,
        'pages',
        `page-${startPage.pageNumber}`,
        'audio',
        doc.targetLanguage,
        doc.voice,
        'chunks',
        'chunk-1.mp3',
      ].join('/');
      firstPageAudioReady = await fileExistsFlexible(chunkPath);
    }
  }

  // Note: translations removed - not used by frontend and causes unnecessary S3 reads
  return NextResponse.json({
    document: doc,
    pages,
    chapters,
    firstPageAudioReady,
    lastListenedPageId: doc.lastListenedPageId,
  });
}
