import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authorizeDocumentAccess } from '@/lib/authz';
import { logger } from '@/lib/logger';

/**
 * POST /api/doc/[id]/resume
 *
 * Save the last listened page for resume position tracking
 */
export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id: docId } = await params;
  const body = await req.json();
  const { pageId } = body;

  if (!pageId) {
    return NextResponse.json({ error: 'pageId required' }, { status: 400 });
  }

  // Get document
  const doc = await prisma.document.findUnique({
    where: { id: docId },
  });

  if (!doc) {
    return NextResponse.json({ error: 'Not found' }, { status: 404 });
  }

  // Authorization check
  const allowed = await authorizeDocumentAccess(req, {
    id: doc.id,
    userId: doc.userId,
    sessionId: doc.sessionId,
  });

  if (!allowed) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  // Verify page belongs to this document
  const page = await prisma.page.findUnique({
    where: { id: pageId },
  });

  if (!page || page.documentId !== docId) {
    return NextResponse.json(
      { error: 'Page not found or does not belong to document' },
      { status: 400 },
    );
  }

  // Update last listened page
  await prisma.document.update({
    where: { id: docId },
    data: {
      lastListenedPageId: pageId,
      lastListenedAt: new Date(),
    },
  });

  logger.log('[resume] Saved resume position', { docId, pageId });

  return NextResponse.json({ ok: true });
}
