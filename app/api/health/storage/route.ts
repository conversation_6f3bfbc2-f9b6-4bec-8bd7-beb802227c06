import { NextResponse } from 'next/server';
import { ListObjectsV2Command, S3Client } from '@aws-sdk/client-s3';
import { redisConnection } from '@/lib/queue/config';
import { logger } from '@/lib/logger';

/**
 * Health check endpoint for storage infrastructure
 *
 * Checks:
 * - S3 configuration and connectivity
 * - Redis connection
 * - Environment variables
 *
 * Usage: GET /api/health/storage
 */
export async function GET() {
  const checks = {
    timestamp: new Date().toISOString(),
    environment: {
      aws_region: !!process.env.AWS_REGION,
      s3_bucket_name: !!process.env.S3_BUCKET_NAME,
      aws_access_key_id: !!process.env.AWS_ACCESS_KEY_ID,
      aws_secret_access_key: !!process.env.AWS_SECRET_ACCESS_KEY,
      redis_host: process.env.REDIS_HOST || 'localhost',
      redis_port: process.env.REDIS_PORT || '6379',
    },
    s3: {
      status: 'unknown' as 'ok' | 'error' | 'unknown',
      message: '',
      bucket: process.env.S3_BUCKET_NAME || 'not-configured',
      region: process.env.AWS_REGION || 'not-configured',
    },
    redis: {
      status: 'unknown' as 'ok' | 'error' | 'unknown',
      message: '',
    },
    overall: 'unknown' as 'ok' | 'degraded' | 'error',
  };

  // Check S3
  try {
    if (!process.env.AWS_REGION || !process.env.S3_BUCKET_NAME) {
      checks.s3.status = 'error';
      checks.s3.message = 'S3 environment variables not configured';
    } else {
      const creds =
        process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY
          ? {
              accessKeyId: process.env.AWS_ACCESS_KEY_ID,
              secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            }
          : undefined;

      const s3Client = new S3Client({
        region: process.env.AWS_REGION,
        credentials: creds,
      });

      // Try to list objects in bucket (lightweight check)
      await s3Client.send(
        new ListObjectsV2Command({
          Bucket: process.env.S3_BUCKET_NAME,
          MaxKeys: 1, // Just check accessibility, don't list everything
        }),
      );

      checks.s3.status = 'ok';
      checks.s3.message = 'S3 bucket accessible';
    }
  } catch (error: any) {
    checks.s3.status = 'error';
    checks.s3.message = `S3 error: ${error.name || 'Unknown'} - ${error.message?.substring(0, 100) || 'Unknown error'}`;
    logger.error('[HealthCheck] S3 check failed', {
      error: error.message,
      name: error.name,
    });
  }

  // Check Redis
  try {
    // Test basic Redis operations
    const testKey = 'health:check:' + Date.now();
    await redisConnection.set(testKey, '1', 'EX', 10); // 10 second TTL
    const value = await redisConnection.get(testKey);
    await redisConnection.del(testKey);

    if (value === '1') {
      checks.redis.status = 'ok';
      checks.redis.message = 'Redis connection working';
    } else {
      checks.redis.status = 'error';
      checks.redis.message = 'Redis read/write verification failed';
    }
  } catch (error: any) {
    checks.redis.status = 'error';
    checks.redis.message = `Redis error: ${error.message?.substring(0, 100) || 'Connection failed'}`;
    logger.error('[HealthCheck] Redis check failed', {
      error: error.message,
    });
  }

  // Determine overall status
  if (checks.s3.status === 'ok' && checks.redis.status === 'ok') {
    checks.overall = 'ok';
  } else if (checks.s3.status === 'error' && checks.redis.status === 'error') {
    checks.overall = 'error';
  } else {
    checks.overall = 'degraded';
  }

  // Return appropriate HTTP status code
  const httpStatus =
    checks.overall === 'ok' ? 200 : checks.overall === 'degraded' ? 207 : 503;

  return NextResponse.json(checks, { status: httpStatus });
}
