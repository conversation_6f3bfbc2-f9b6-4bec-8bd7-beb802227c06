import { Checkout } from '@polar-sh/nextjs';
import { NextResponse, NextRequest } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { logger } from '@/lib/logger';

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

const checkoutHandler = Checkout({
  accessToken: process.env.POLAR_ACCESS_TOKEN!,
  successUrl: `${baseUrl}/success?checkout_id={CHECKOUT_ID}`,
  server: process.env.POLAR_SERVER as 'sandbox' | 'production' | undefined, // Use env var for flexibility
});

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user details from Clerk to pre-fill customer info
    const client = await clerkClient();
    const user = await client.users.getUser(userId);

    // Clone the request URL to add customer information
    const url = new URL(req.url);

    // Pre-fill customer email if available
    if (
      user.emailAddresses?.[0]?.emailAddress &&
      !url.searchParams.has('customerEmail')
    ) {
      url.searchParams.set(
        'customerEmail',
        user.emailAddresses[0].emailAddress,
      );
    }

    // Set customer external ID to link Polar customer with our user
    if (!url.searchParams.has('customerExternalId')) {
      url.searchParams.set('customerExternalId', userId);
    }

    // Add Clerk user ID to metadata for webhook processing
    if (!url.searchParams.has('metadata[clerk_user_id]')) {
      url.searchParams.set('metadata[clerk_user_id]', userId);
    }

    // Create a new request with updated URL
    const modifiedRequest = new NextRequest(url.toString(), req);

    return checkoutHandler(modifiedRequest);
  } catch (error) {
    logger.error('Checkout error:', error);
    return NextResponse.json(
      { error: 'Failed to initiate checkout' },
      { status: 500 },
    );
  }
}

export const runtime = 'nodejs';
