import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { prisma } from '@/lib/prisma';
import {
  validateEvent,
  WebhookVerificationError,
} from '@polar-sh/sdk/webhooks';
import { logger } from '@/lib/logger';

const POLAR_WEBHOOK_SECRET = process.env.POLAR_WEBHOOK_SECRET;

// Map Polar product/price IDs to plan keys
const PRODUCT_TO_PLAN: Record<string, string> = {
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_STARTER || '']: 'starter',
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_PRO || '']: 'pro',
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_PREMIUM || '']: 'premium',
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_ENTERPRISE || '']: 'enterprise',
};

// Type definitions for Polar webhook events
interface WebhookEvent {
  type: string;
  data: any;
}

interface SubscriptionData {
  id: string;
  status: string;
  created_at?: string;
  current_period_start?: string;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  ended_at?: string;
  product: {
    id: string;
    name?: string;
  };
  price?: {
    id: string;
    amount_type?: string;
    price_amount?: number;
    price_currency?: string;
  };
  customer?: {
    id: string;
    email?: string;
    metadata?: Record<string, any>;
  };
  metadata?: Record<string, any>;
}

interface OrderData {
  id: string;
  amount: number;
  tax_amount: number;
  currency: string;
  billing_reason:
    | 'purchase'
    | 'subscription_create'
    | 'subscription_cycle'
    | 'subscription_update';
  subscription?: {
    id: string;
  };
  product: {
    id: string;
  };
  customer?: {
    id: string;
    email?: string;
    metadata?: Record<string, any>;
  };
}

interface CheckoutData {
  id: string;
  status: string;
  customer_id?: string;
  customer_email?: string;
  product_id?: string;
  subscription_id?: string;
  amount?: number;
  tax_amount?: number;
  currency?: string;
  metadata?: Record<string, any>;
}

export async function POST(req: NextRequest) {
  try {
    // Get raw body and headers for signature verification
    const body = await req.text();
    const headersList = await headers();

    // Convert headers to Record<string, string> for validation
    const headersRecord: Record<string, string> = {};
    headersList.forEach((value, key) => {
      headersRecord[key] = value;
    });

    let event: any;

    // Verify webhook signature if secret is configured
    if (POLAR_WEBHOOK_SECRET) {
      try {
        event = validateEvent(body, headersRecord, POLAR_WEBHOOK_SECRET);
        logger.log('✓ Webhook signature verified');
      } catch (error) {
        if (error instanceof WebhookVerificationError) {
          logger.error('Invalid webhook signature:', error.message);
          return NextResponse.json(
            { error: 'Invalid signature' },
            { status: 401 },
          );
        }
        throw error;
      }
    } else {
      // Parse without verification (not recommended for production)
      logger.warn(
        '⚠️ POLAR_WEBHOOK_SECRET not set - webhook signatures not verified',
      );
      event = JSON.parse(body);
    }

    logger.log('Received Polar webhook:', event.type);

    // Route event to appropriate handler
    switch (event.type) {
      // Subscription events
      case 'subscription.created':
        await handleSubscriptionCreated(event.data);
        break;
      case 'subscription.updated':
        await handleSubscriptionUpdated(event.data);
        break;
      case 'subscription.active':
        await handleSubscriptionActive(event.data);
        break;
      case 'subscription.canceled':
        await handleSubscriptionCanceled(event.data);
        break;
      case 'subscription.uncanceled':
        await handleSubscriptionUncanceled(event.data);
        break;
      case 'subscription.revoked':
        await handleSubscriptionRevoked(event.data);
        break;

      // Order events
      case 'order.created':
        await handleOrderCreated(event.data);
        break;
      case 'order.paid':
        await handleOrderPaid(event.data);
        break;

      // Checkout events
      case 'checkout.created':
        await handleCheckoutCreated(event.data);
        break;
      case 'checkout.updated':
        await handleCheckoutUpdated(event.data);
        break;

      default:
        logger.log(`Unhandled webhook event type: ${event.type}`);
    }

    return NextResponse.json({ received: true }, { status: 200 });
  } catch (error) {
    logger.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 },
    );
  }
}

// Helper function to get user from subscription/order data
async function getUserFromPolarData(
  data: SubscriptionData | OrderData | CheckoutData,
) {
  // Try to get Clerk user ID from metadata
  const clerkUserId =
    data.metadata?.['clerk_user_id'] ||
    data.metadata?.['external_id'] ||
    data.customer?.metadata?.['clerk_user_id'] ||
    data.customer?.metadata?.['external_id'];

  if (!clerkUserId) {
    logger.error('No Clerk user ID found in Polar data');
    return null;
  }

  const user = await prisma.user.findUnique({
    where: { clerkUserId },
  });

  if (!user) {
    logger.error(`User not found for Clerk ID: ${clerkUserId}`);
    return null;
  }

  return user;
}

// Helper function to get plan key from product ID
function getPlanKeyFromProductId(productId: string): string | null {
  const planKey = PRODUCT_TO_PLAN[productId];
  if (!planKey) {
    logger.warn(`Unknown product ID: ${productId}`);
    return null;
  }
  return planKey;
}

// Helper function to log payment event
async function logPaymentEvent(
  userId: string,
  type: string,
  externalId: string,
  data: any,
  amount?: number,
  currency?: string,
) {
  await prisma.paymentEvent.create({
    data: {
      userId,
      provider: 'polar',
      type,
      status: 'completed',
      externalId,
      amount: amount || null,
      currency: currency || null,
      raw: data,
    },
  });
}

// 1. Handle subscription.created
async function handleSubscriptionCreated(data: SubscriptionData) {
  logger.log(`Processing subscription.created: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  const productId = data.product.id;
  const planKey = getPlanKeyFromProductId(productId);
  if (!planKey) return;

  // Verify plan exists
  const plan = await prisma.plan.findUnique({ where: { key: planKey } });
  if (!plan) {
    logger.error(`Plan not found in database: ${planKey}`);
    return;
  }

  // Calculate billing period
  const currentPeriodStart = data.current_period_start
    ? new Date(data.current_period_start)
    : new Date();
  const currentPeriodEnd = data.current_period_end
    ? new Date(data.current_period_end)
    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

  // Update user
  await prisma.user.update({
    where: { id: user.id },
    data: {
      planKey,
      subscriptionStatus: data.status,
      billingPeriodStart: currentPeriodStart,
      billingPeriodEnd: currentPeriodEnd,
    },
  });

  // Create or update usage record
  await prisma.usage.upsert({
    where: {
      userId_cycleStart: {
        userId: user.id,
        cycleStart: currentPeriodStart,
      },
    },
    create: {
      userId: user.id,
      cycleStart: currentPeriodStart,
      cycleEnd: currentPeriodEnd,
      listeningTime: 0,
      documentsProcessed: 0,
      pagesProcessed: 0,
    },
    update: {
      cycleEnd: currentPeriodEnd,
    },
  });

  // Log event
  await logPaymentEvent(user.id, 'subscription_created', data.id, data);

  logger.log(`✓ User ${user.clerkUserId} subscribed to ${planKey}`);
}

// 2. Handle subscription.updated
async function handleSubscriptionUpdated(data: SubscriptionData) {
  logger.log(`Processing subscription.updated: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  const productId = data.product.id;
  const planKey = getPlanKeyFromProductId(productId);
  if (!planKey) return;

  // Update subscription details
  await prisma.user.update({
    where: { id: user.id },
    data: {
      planKey,
      subscriptionStatus: data.status,
      billingPeriodStart: data.current_period_start
        ? new Date(data.current_period_start)
        : undefined,
      billingPeriodEnd: data.current_period_end
        ? new Date(data.current_period_end)
        : undefined,
    },
  });

  await logPaymentEvent(user.id, 'subscription_updated', data.id, data);

  logger.log(`✓ Subscription updated for user ${user.clerkUserId}`);
}

// 3. Handle subscription.active
async function handleSubscriptionActive(data: SubscriptionData) {
  logger.log(`Processing subscription.active: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  const productId = data.product.id;
  const planKey = getPlanKeyFromProductId(productId);
  if (!planKey) return;

  // Activate subscription
  await prisma.user.update({
    where: { id: user.id },
    data: {
      planKey,
      subscriptionStatus: 'active',
      billingPeriodStart: data.current_period_start
        ? new Date(data.current_period_start)
        : undefined,
      billingPeriodEnd: data.current_period_end
        ? new Date(data.current_period_end)
        : undefined,
    },
  });

  await logPaymentEvent(user.id, 'subscription_active', data.id, data);

  logger.log(`✓ Subscription activated for user ${user.clerkUserId}`);
}

// 4. Handle subscription.canceled
async function handleSubscriptionCanceled(data: SubscriptionData) {
  logger.log(`Processing subscription.canceled: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  // Update status - keep access until period end if cancel_at_period_end is true
  const newStatus = data.cancel_at_period_end ? 'active' : 'canceled';

  await prisma.user.update({
    where: { id: user.id },
    data: {
      subscriptionStatus: newStatus,
    },
  });

  await logPaymentEvent(user.id, 'subscription_canceled', data.id, data);

  logger.log(
    `✓ Subscription canceled for user ${user.clerkUserId} (status: ${newStatus})`,
  );
}

// 5. Handle subscription.uncanceled
async function handleSubscriptionUncanceled(data: SubscriptionData) {
  logger.log(`Processing subscription.uncanceled: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  // Reactivate subscription
  await prisma.user.update({
    where: { id: user.id },
    data: {
      subscriptionStatus: 'active',
    },
  });

  await logPaymentEvent(user.id, 'subscription_uncanceled', data.id, data);

  logger.log(`✓ Subscription uncanceled for user ${user.clerkUserId}`);
}

// 6. Handle subscription.revoked
async function handleSubscriptionRevoked(data: SubscriptionData) {
  logger.log(`Processing subscription.revoked: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  // Revoke access immediately and downgrade to free plan
  await prisma.user.update({
    where: { id: user.id },
    data: {
      planKey: 'free',
      subscriptionStatus: 'inactive',
      billingPeriodStart: null,
      billingPeriodEnd: null,
    },
  });

  await logPaymentEvent(user.id, 'subscription_revoked', data.id, data);

  logger.log(
    `✓ Subscription revoked for user ${user.clerkUserId} - downgraded to free`,
  );
}

// 7. Handle order.created (for renewals and one-time payments)
async function handleOrderCreated(data: OrderData) {
  logger.log(`Processing order.created: ${data.id} (${data.billing_reason})`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  // Log the order
  await logPaymentEvent(
    user.id,
    `order_${data.billing_reason}`,
    data.id,
    data,
    data.amount,
    data.currency,
  );

  // Handle subscription renewal
  if (data.billing_reason === 'subscription_cycle' && data.subscription) {
    logger.log(`  → Subscription renewal for ${user.clerkUserId}`);

    // Create new usage cycle for the renewal period
    const now = new Date();
    const cycleEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    await prisma.usage.create({
      data: {
        userId: user.id,
        cycleStart: now,
        cycleEnd: cycleEnd,
        listeningTime: 0,
        documentsProcessed: 0,
        pagesProcessed: 0,
      },
    });

    logger.log(`  ✓ Created new usage cycle for renewal`);
  }
}

// 8. Handle order.paid
async function handleOrderPaid(data: OrderData) {
  logger.log(`Processing order.paid: ${data.id}`);

  const user = await getUserFromPolarData(data);
  if (!user) return;

  await logPaymentEvent(
    user.id,
    'order_paid',
    data.id,
    data,
    data.amount,
    data.currency,
  );

  logger.log(`✓ Order paid for user ${user.clerkUserId}`);
}

// 9. Handle checkout.created
async function handleCheckoutCreated(data: CheckoutData) {
  logger.log(`Processing checkout.created: ${data.id}`);
  // Just log for tracking - main processing happens in checkout.updated
  await logPaymentEvent(
    data.metadata?.['clerk_user_id'] || 'unknown',
    'checkout_created',
    data.id,
    data,
  );
}

// 10. Handle checkout.updated
async function handleCheckoutUpdated(data: CheckoutData) {
  logger.log(
    `Processing checkout.updated: ${data.id} (status: ${data.status})`,
  );

  // Only process confirmed/succeeded checkouts
  if (!['confirmed', 'succeeded'].includes(data.status)) {
    logger.log(`  → Skipping checkout with status: ${data.status}`);
    return;
  }

  const user = await getUserFromPolarData(data);
  if (!user) return;

  await logPaymentEvent(
    user.id,
    'checkout_completed',
    data.id,
    data,
    data.amount,
    data.currency,
  );

  logger.log(`✓ Checkout completed for user ${user.clerkUserId}`);
}
