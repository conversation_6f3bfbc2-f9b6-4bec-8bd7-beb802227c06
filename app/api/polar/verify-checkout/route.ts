import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';

const POLAR_ACCESS_TOKEN = process.env.POLAR_ACCESS_TOKEN;
const POLAR_API_URL =
  process.env.POLAR_SERVER === 'sandbox'
    ? 'https://sandbox-api.polar.sh'
    : 'https://api.polar.sh';

// Map Polar product/price IDs to plan keys
const PRODUCT_TO_PLAN: Record<string, string> = {
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_STARTER || '']: 'starter',
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_PRO || '']: 'pro',
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_PREMIUM || '']: 'premium',
  [process.env.NEXT_PUBLIC_POLAR_PRODUCT_ID_ENTERPRISE || '']: 'enterprise',
};

export async function POST(req: NextRequest) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { checkoutId } = await req.json();

    if (!checkoutId) {
      return NextResponse.json(
        { error: 'Checkout ID required' },
        { status: 400 },
      );
    }

    // Fetch checkout details from Polar API
    const checkoutResponse = await fetch(
      `${POLAR_API_URL}/v1/checkouts/${checkoutId}`,
      {
        headers: {
          Authorization: `Bearer ${POLAR_ACCESS_TOKEN}`,
          'Content-Type': 'application/json',
        },
      },
    );

    if (!checkoutResponse.ok) {
      logger.error('Failed to fetch checkout:', await checkoutResponse.text());
      return NextResponse.json(
        { error: 'Failed to verify checkout' },
        { status: 500 },
      );
    }

    const checkout = await checkoutResponse.json();

    // Log checkout details for debugging
    logger.log('Checkout details:', {
      id: checkout.id,
      status: checkout.status,
      customer_external_id: checkout.customer_external_id,
      product_id: checkout.product_id,
    });

    // Verify the checkout belongs to this user
    if (checkout.customer_external_id !== clerkUserId) {
      logger.error('Checkout user mismatch:', {
        expected: clerkUserId,
        actual: checkout.customer_external_id,
      });
      return NextResponse.json(
        { error: 'Checkout does not belong to this user' },
        { status: 403 },
      );
    }

    // Check if checkout is successful
    // Polar checkout statuses: open, confirmed, succeeded, failed
    const validStatuses = ['confirmed', 'succeeded'];
    if (!validStatuses.includes(checkout.status)) {
      logger.warn('Checkout not completed:', {
        checkoutId,
        status: checkout.status,
        userId: clerkUserId,
      });
      return NextResponse.json(
        {
          error: 'Checkout not completed',
          status: checkout.status,
          message:
            checkout.status === 'open'
              ? 'Payment is still being processed. Please refresh the page in a few moments.'
              : 'Payment was not successful. Please try again.',
        },
        { status: 400 },
      );
    }

    // Get the product ID from checkout
    const productId = checkout.product_id || checkout.product?.id;
    if (!productId) {
      return NextResponse.json(
        { error: 'No product found in checkout' },
        { status: 400 },
      );
    }

    // Map to internal plan key
    let planKey = PRODUCT_TO_PLAN[productId];
    if (!planKey) {
      logger.warn(`Unknown product ID: ${productId}, defaulting to pro`);
      planKey = 'pro'; // Default to pro if mapping not found
    }

    // Verify plan exists in database
    const plan = await prisma.plan.findUnique({
      where: { key: planKey },
    });

    if (!plan) {
      logger.error(`Plan not found in database: ${planKey}`);
      return NextResponse.json(
        {
          error: 'Plan configuration error',
          message: `The plan "${planKey}" does not exist. Please contact support.`,
        },
        { status: 500 },
      );
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { clerkUserId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user already has this plan (avoid duplicate updates)
    if (user.planKey === planKey && user.subscriptionStatus === 'active') {
      return NextResponse.json({
        success: true,
        message: 'Plan already active',
        plan: planKey,
      });
    }

    // Calculate billing period (default 30 days if not provided)
    const now = new Date();
    const billingPeriodStart = checkout.subscription?.current_period_start
      ? new Date(checkout.subscription.current_period_start)
      : now;
    const billingPeriodEnd = checkout.subscription?.current_period_end
      ? new Date(checkout.subscription.current_period_end)
      : new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    // Update user plan
    await prisma.user.update({
      where: { id: user.id },
      data: {
        planKey,
        subscriptionStatus: 'active',
        billingPeriodStart,
        billingPeriodEnd,
      },
    });

    // Create usage record for new billing cycle
    await prisma.usage.upsert({
      where: {
        userId_cycleStart: {
          userId: user.id,
          cycleStart: billingPeriodStart,
        },
      },
      create: {
        userId: user.id,
        cycleStart: billingPeriodStart,
        cycleEnd: billingPeriodEnd,
        listeningTime: 0,
        documentsProcessed: 0,
        pagesProcessed: 0,
      },
      update: {
        cycleEnd: billingPeriodEnd,
      },
    });

    // Log payment event
    await prisma.paymentEvent.create({
      data: {
        userId: user.id,
        provider: 'polar',
        type: 'checkout_verified',
        status: 'completed',
        externalId: checkoutId,
        amount: checkout.amount || 0,
        currency: checkout.currency || 'USD',
        raw: checkout,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Plan updated successfully',
      plan: planKey || 'pro',
    });
  } catch (error) {
    logger.error('Verify checkout error:', error);
    return NextResponse.json(
      { error: 'Failed to verify checkout' },
      { status: 500 },
    );
  }
}
