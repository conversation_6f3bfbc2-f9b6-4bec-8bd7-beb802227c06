import { NextResponse } from 'next/server';
import { downloadVbeeAudio } from '@/lib/ai/providers/vbee';
import { saveFile } from '@/lib/file-service';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';
import { addListeningSecondsForUser } from '@/lib/usage';
import { getAudioDuration } from '@/lib/ai/audio-utils';

/**
 * Vbee Webhook Handler
 *
 * Vbee calls this endpoint when TTS audio is ready.
 * Expected payload:
 * {
 *   audio_link: "https://...",
 *   request_id: "...",
 *   status: "success",
 *   ...
 * }
 */
export async function POST(req: Request) {
  try {
    // Get chunkId from query params
    const url = new URL(req.url);
    const chunkId = url.searchParams.get('chunkId');

    if (!chunkId) {
      logger.error('[vbee-webhook] Missing chunkId in query params');
      return NextResponse.json({ error: 'Missing chunkId' }, { status: 400 });
    }

    // Parse webhook payload
    const payload = await req.json();
    logger.log('[vbee-webhook] Received callback:', {
      chunkId,
      status: payload.status,
      hasAudioLink: !!payload.audio_link,
    });

    // Extract audio_link from payload
    const audioLink = payload.audio_link || payload.url || payload.audioUrl;

    if (!audioLink) {
      logger.error('[vbee-webhook] No audio_link in payload:', payload);
      return NextResponse.json(
        { error: 'No audio_link in payload' },
        { status: 400 },
      );
    }

    // Download audio from Vbee
    const audioResult = await downloadVbeeAudio(audioLink);

    if (!audioResult) {
      logger.error('[vbee-webhook] Failed to download audio from:', audioLink);
      return NextResponse.json(
        { error: 'Failed to download audio' },
        { status: 500 },
      );
    }

    // Parse chunkId to get page info
    // Expected format: "pageId-chunkIndex" or "documentId-pageNumber-chunkIndex"
    const parts = chunkId.split('-');
    if (parts.length < 2) {
      logger.error('[vbee-webhook] Invalid chunkId format:', chunkId);
      return NextResponse.json(
        { error: 'Invalid chunkId format' },
        { status: 400 },
      );
    }

    // Save audio to storage
    // The chunk will be saved and the frontend will poll for it
    const chunkPath = chunkId; // Use chunkId as path
    if (audioResult.audioBytes) {
      await saveFile(chunkPath, audioResult.audioBytes, 'audio/mpeg');
    } else {
      logger.error('[vbee-webhook] Audio bytes is null');
      return NextResponse.json(
        { error: 'Audio bytes is null' },
        { status: 500 },
      );
    }

    // Get actual duration from the audio file
    let actualDuration = audioResult.durationSec;
    try {
      const realDuration = audioResult.audioBytes
        ? await getAudioDuration(audioResult.audioBytes)
        : null;
      if (realDuration !== null) {
        actualDuration = realDuration;
      }
    } catch (e) {
      logger.warn(
        '[vbee-webhook] Failed to get real duration, using estimated:',
        e,
      );
    }

    logger.log('[vbee-webhook] Audio saved successfully:', {
      chunkId,
      bytes: audioResult.audioBytes?.length || 0,
      duration: actualDuration,
    });

    // Update VbeeTtsRequest status
    try {
      const requestId = payload.request_id || payload.id;
      if (requestId) {
        await prisma.vbeeTtsRequest.updateMany({
          where: { requestId },
          data: {
            status: 'completed',
            audioLink,
          },
        });
        logger.log('[vbee-webhook] Updated request status', { requestId });
      }
    } catch (error) {
      logger.warn('[vbee-webhook] Failed to update request status:', error);
    }

    // Track usage for logged-in users (not anonymous)
    // Extract documentId from chunkPath to find the user
    try {
      const pathParts = chunkPath.split('/');
      const docIdIndex = pathParts.indexOf('documents');
      if (docIdIndex >= 0 && pathParts.length > docIdIndex + 1) {
        const documentId = pathParts[docIdIndex + 1];

        // Get document to find userId
        const document = await prisma.document.findUnique({
          where: { id: documentId },
          select: { userId: true, sessionId: true },
        });

        // Only track usage for logged-in users (not anonymous sessions)
        if (document?.userId && !document.sessionId) {
          const durationSec = Math.max(0, Math.floor(actualDuration));
          if (durationSec > 0) {
            await addListeningSecondsForUser(document.userId, durationSec);
            logger.log('[vbee-webhook] Usage tracked:', {
              userId: document.userId,
              documentId,
              durationSec,
              chunkId,
            });
          }
        }
      }
    } catch (error) {
      logger.error('[vbee-webhook] Failed to track usage:', error);
      // Don't fail the webhook if usage tracking fails
    }

    return NextResponse.json({
      ok: true,
      chunkId,
      bytes: audioResult.audioBytes.length,
      duration: actualDuration,
    });
  } catch (error) {
    logger.error(
      '[vbee-webhook] Error processing webhook:',
      (error as Error)?.message || error,
    );
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export const runtime = 'nodejs';
