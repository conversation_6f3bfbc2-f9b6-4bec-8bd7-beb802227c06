import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { getClientIp } from '@/lib/recaptcha/server';
import {
  hashIpUa,
  issueAnonDocToken,
  issueUserToken,
} from '@/lib/access-token';

export async function POST(req: Request) {
  let body: any = null;
  try {
    body = await req.json();
  } catch {}
  // Strategy update: No reCAPTCHA required for token refresh.

  const { userId: clerkUserId } = await auth();
  const ua = req.headers.get('user-agent') || undefined;
  const ip = getClientIp(req) || undefined;
  const { ipHash, userAgentHash } = hashIpUa(ip, ua);

  if (clerkUserId) {
    const user = await prisma.user.findUnique({
      where: { clerkUserId: clerkUserId },
    });
    if (!user)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    const { token, exp } = await issueUserToken(user.id, {
      ipHash,
      userAgentHash,
    });
    return NextResponse.json({ ok: true, type: 'user', token, exp });
  }

  // Anonymous refresh: require docId + anonId, ensure ownership
  const docId = String(body?.docId || '').trim();
  const anonId = String(body?.anonId || '').trim();
  if (!docId || !anonId)
    return NextResponse.json(
      { error: 'Missing docId/anonId' },
      { status: 400 },
    );
  const doc = await prisma.document.findUnique({ where: { id: docId } });
  if (!doc || !doc.sessionId || doc.sessionId !== anonId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }
  const { token, exp } = await issueAnonDocToken(docId, anonId, {
    ipHash,
    userAgentHash,
  });
  return NextResponse.json({ ok: true, type: 'anon_doc', token, exp });
}

export const runtime = 'nodejs';
