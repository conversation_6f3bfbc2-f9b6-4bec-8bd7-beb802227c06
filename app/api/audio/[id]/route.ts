import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { readBufferFlexible } from '@/lib/file-service';
import { auth } from '@clerk/nextjs/server';
import { appConfig } from '@/lib/config';
import { authorizeDocumentAccess } from '@/lib/authz';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id } = await params;
  const audio = await prisma.pageAudio.findUnique({
    where: { id },
    include: { page: { include: { document: true } } },
  });
  if (!audio || !audio.s3KeyAudioMp3)
    return NextResponse.json({ error: 'Not found' }, { status: 404 });
  // Enforce ownership or matching anonymous session
  if (audio.page?.document) {
    const ok = await authorizeDocumentAccess(req, {
      id: audio.page.document.id,
      userId: audio.page.document.userId,
      sessionId: audio.page.document.sessionId,
    });
    if (!ok)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }
  const pointer = audio.s3KeyAudioMp3;
  if (!pointer.endsWith('.mp3')) {
    return NextResponse.json(
      { error: 'Only MP3 audio is served' },
      { status: 404 },
    );
  }
  try {
    const bytes = await readBufferFlexible(pointer);

    // Anonymous users: enforce a hard 60s cap server-side by clamping the served bytes
    // This prevents playback from continuing past the threshold even if client-side JS is throttled.
    const { userId } = await auth();
    const isAnon = !userId;
    const FREE_TRIAL_SECONDS = appConfig.freeTrialSeconds;
    const total = bytes.length;

    // Helper to send a bytes slice with appropriate headers (supports Range semantics)
    const sendRange = (start: number, end: number, partial = true) => {
      const s = Math.max(0, Math.min(start || 0, total - 1));
      const e = Math.max(0, Math.min(end == null ? total - 1 : end, total - 1));
      const body = bytes.subarray(s, e + 1);
      const headers: Record<string, string> = {
        'Content-Type': 'audio/mpeg',
        'Accept-Ranges': 'bytes',
        'Content-Length': String(body.length),
        // Important: vary by cookies/session so post-login fetches bypass anon-cached ranges
        Vary: 'Cookie',
        // Default cache policy; override below depending on anon/signed-in
        'Cache-Control': 'private, max-age=0, must-revalidate',
      };
      if (partial) headers['Content-Range'] = `bytes ${s}-${e}/${total}`;
      return new NextResponse(body, { status: partial ? 206 : 200, headers });
    };

    // If not anonymous, serve normally with Range support
    if (!isAnon || !isFinite(FREE_TRIAL_SECONDS) || FREE_TRIAL_SECONDS <= 0) {
      // Basic Range support for better streaming behavior
      const range = req.headers.get('range');
      if (range && /^bytes=\d*-\d*$/.test(range)) {
        const [startStr, endStr] = range.replace(/bytes=/, '').split('-');
        const start = startStr ? parseInt(startStr, 10) : 0;
        const end = endStr ? parseInt(endStr, 10) : total - 1;
        return sendRange(start, end, true);
      }
      return sendRange(0, total - 1, false);
    }

    // Anonymous enforcement: compute byte cap corresponding to FREE_TRIAL_SECONDS
    // Prefer DB duration if present; otherwise fallback to assumed 128kbps
    const durationSec =
      audio.duration && audio.duration > 0 ? audio.duration : null;
    let allowedEnd = total - 1;
    if (durationSec && durationSec > 0) {
      const ratio = Math.max(0, Math.min(1, FREE_TRIAL_SECONDS / durationSec));
      allowedEnd = Math.max(
        0,
        Math.min(total - 1, Math.floor(total * ratio) - 1),
      );
    } else {
      const bytesPerSec = 16000; // ~128kbps
      allowedEnd = Math.max(
        0,
        Math.min(total - 1, Math.floor(FREE_TRIAL_SECONDS * bytesPerSec) - 1),
      );
    }

    const range = req.headers.get('range');
    if (range && /^bytes=\d*-\d*$/.test(range)) {
      const [startStr, endStr] = range.replace(/bytes=/, '').split('-');
      const start = startStr ? parseInt(startStr, 10) : 0;
      let end = endStr ? parseInt(endStr, 10) : allowedEnd;
      if (start > allowedEnd) {
        // Requested range starts beyond allowed window — signal unsatisfiable
        return new NextResponse(null, {
          status: 416,
          headers: {
            'Content-Range': `bytes */${total}`,
            Vary: 'Cookie',
            'Cache-Control': 'private, no-store',
          },
        });
      }
      end = Math.min(end, allowedEnd);
      const res = sendRange(start, end, true);
      // Anonymous gated responses should never be cached
      res.headers.set('Cache-Control', 'private, no-store');
      return res;
    }
    // No range header — return only allowed window as partial content
    const res = sendRange(0, allowedEnd, true);
    res.headers.set('Cache-Control', 'private, no-store');
    return res;
  } catch {
    return NextResponse.json({ error: 'Missing file' }, { status: 404 });
  }
}

export const runtime = 'nodejs';
