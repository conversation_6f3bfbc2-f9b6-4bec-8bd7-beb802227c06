import { NextResponse } from 'next/server';
import {
  createDocument,
  seedPagesForDocument,
  setChapters,
  updateDocument,
} from '@/lib/db';
import { prisma } from '@/lib/prisma';
import { extractOutline } from '@/lib/pdf';
import type { Voice } from '@/lib/types';
import { saveDocumentOriginal } from '@/lib/storage';
import { PDFDocument } from 'pdf-lib';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { auth } from '@clerk/nextjs/server';
import { ensureUserByClerkId } from '@/lib/user';
import { getClientIp, verifyRecaptcha } from '@/lib/recaptcha/server';
import { issueAnonDocToken, hashIpUa } from '@/lib/access-token';
import { canUploadDocument, canGenerateAudio } from '@/lib/plan-guard';
import { logger } from '@/lib/logger';

export async function POST(req: Request) {
  const form = await req.formData();
  const file = form.get('file') as File | null;
  const targetLanguage = (form.get('targetLanguage') as string | null) ?? 'en';
  const voice = ((form.get('voice') as string | null) ?? 'female') as Voice;
  const anonId = (form.get('anonId') as string | null) || null;
  const recaptchaToken =
    (form.get('recaptchaToken') as string | null) ||
    req.headers.get('x-recaptcha-token');
  let anonAltIds: string[] = [];
  try {
    const raw = form.get('anonAltIds') as string | null;
    if (raw) {
      const parsed = JSON.parse(raw);
      if (Array.isArray(parsed))
        anonAltIds = parsed.filter(
          (x) => typeof x === 'string' && x.length > 0,
        );
    }
  } catch {}

  if (!file) {
    return NextResponse.json({ error: 'Missing file' }, { status: 400 });
  }

  // Determine authentication before reCAPTCHA enforcement
  const { userId: clerkUserId } = await auth();
  const hasSessionCookie = req.headers.get('cookie')?.includes('__session');
  logger.log(
    '[upload] clerkUserId=',
    clerkUserId,
    'hasSession=',
    hasSessionCookie,
    'hasToken=',
    !!recaptchaToken,
  );

  // reCAPTCHA v3 validation only for anonymous uploads (no clerkUserId and no session cookie)
  if (!clerkUserId && !hasSessionCookie) {
    if (!recaptchaToken) {
      return NextResponse.json(
        { error: 'reCAPTCHA token required for anonymous uploads' },
        { status: 400 },
      );
    }
    try {
      const ip = getClientIp(req);
      const verify = await verifyRecaptcha(recaptchaToken, 'upload', ip);
      logger.log('[upload] reCAPTCHA verify=', verify);
      if (!verify.success) {
        return NextResponse.json(
          { error: 'reCAPTCHA verification failed' },
          { status: 403 },
        );
      }
    } catch (err) {
      logger.error('[upload] reCAPTCHA error:', err);
      return NextResponse.json(
        { error: 'reCAPTCHA verification error' },
        { status: 500 },
      );
    }
  }

  // AuthZ: allow only if user is logged in OR an anonymous fingerprint is provided.
  if (!clerkUserId && !anonId) {
    return NextResponse.json(
      { error: 'Unauthorized: login or provide fingerprint anonId' },
      { status: 401 },
    );
  }

  const contentType = file.type || 'application/pdf';
  const sizeBytes = file.size || 0;
  const originalFilename = (file as any).name || 'document.pdf';
  // Attempt to extract an outline from PDF bytes (best-effort)
  let detectedChapters:
    | { name: string; startPageNumber?: number; level?: number }[]
    | null = null;
  try {
    const ab = await file.arrayBuffer();
    const buf = Buffer.from(ab);
    const items = await extractOutline(buf);
    logger.log('[upload] outline items detected:', items.length);
    if (items.length > 0) detectedChapters = items;
  } catch {
    // ignore parse errors
  }

  // If anonymous (not signed in), ensure only one document per anonymous identity (consider alternates)
  if (!clerkUserId && anonId) {
    const candidates = Array.from(new Set([anonId, ...anonAltIds]));
    const existing = await prisma.document.findFirst({
      where: { sessionId: { in: candidates } },
      orderBy: { createdAt: 'desc' },
    });
    if (existing) {
      // Also issue an anon token for the existing doc
      try {
        const ua = req.headers.get('user-agent') || undefined;
        const ip = getClientIp(req) || undefined;
        const { ipHash, userAgentHash } = hashIpUa(ip, ua);
        const { token, exp } = await issueAnonDocToken(existing.id, anonId, {
          ipHash,
          userAgentHash,
        });
        return NextResponse.json({
          docId: existing.id,
          existed: true,
          token,
          type: 'anon_doc',
          exp,
        });
      } catch {
        return NextResponse.json({ docId: existing.id, existed: true });
      }
    }
  }

  // Determine owner: signed-in user gets ownership; otherwise bind to anonymous session.
  let ownerUserId: string | null = null;
  if (clerkUserId) {
    try {
      ownerUserId = await ensureUserByClerkId(clerkUserId);
    } catch {
      // If user lookup fails unexpectedly, fail closed
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
  }

  // Check plan limits for signed-in users
  if (ownerUserId) {
    // Check document count limit
    const uploadCheck = await canUploadDocument(ownerUserId);
    if (!uploadCheck.allowed) {
      logger.log('[upload] Upload blocked by document count limit:', {
        userId: ownerUserId,
        reason: uploadCheck.reason,
        currentCount: uploadCheck.currentCount,
        maxCount: uploadCheck.maxCount,
      });
      return NextResponse.json(
        {
          error: 'Upload limit reached',
          requireUpgrade: true,
          reason: uploadCheck.reason,
          currentCount: uploadCheck.currentCount,
          maxCount: uploadCheck.maxCount,
        },
        { status: 403 },
      );
    }

    // Check listening time limit to prevent wasting resources on translation
    const audioCheck = await canGenerateAudio(ownerUserId);
    if (!audioCheck.allowed) {
      logger.log('[upload] Upload blocked by listening time limit:', {
        userId: ownerUserId,
        reason: audioCheck.reason,
        usedSeconds: audioCheck.usedSeconds,
        limitSeconds: audioCheck.limitSeconds,
      });
      return NextResponse.json(
        {
          error:
            'Your listening time limit has been reached. Please upgrade your plan to continue.',
          requireUpgrade: true,
          reason: audioCheck.reason,
          usedSeconds: audioCheck.usedSeconds,
          limitSeconds: audioCheck.limitSeconds,
          remainingSeconds: audioCheck.remainingSeconds,
        },
        { status: 403 },
      );
    }
  }

  const doc = await createDocument({
    userId: ownerUserId,
    sessionId: ownerUserId ? null : anonId,
    originalFilename,
    contentType,
    sizeBytes,
    pageCount: undefined,
    targetLanguage,
    voice,
    status: 'uploaded',
    authors: null,
    hasFullAudio: false,
    tocSource: detectedChapters ? 'pdf_outline' : 'none',
  });

  // Save original and seed pages only. Translation/TTS will be triggered on-demand.
  try {
    const ab = await file.arrayBuffer();
    const buf = Buffer.from(ab);
    const origPath = await saveDocumentOriginal(doc.id, buf);
    await updateDocument(doc.id, { s3KeyOriginal: origPath });
    try {
      const pdf = await PDFDocument.load(buf);
      const pageCount = pdf.getPageCount();
      await updateDocument(doc.id, { pageCount });
      await seedPagesForDocument(doc.id, pageCount);

      // Enqueue fast-track processing job (analyze structure, translate first page, generate audio)
      // Background page splitting happens separately in low-priority job
      try {
        await documentQueue.add(
          JOB_TYPES.FAST_ANALYZE_AND_PRIME,
          {
            docId: doc.id,
            targetLanguage,
            voice,
          },
          {
            jobId: `${JOB_TYPES.FAST_ANALYZE_AND_PRIME}-${doc.id}`,
            priority: 10, // High priority - user is waiting for audio
            removeOnComplete: true,
            removeOnFail: false,
          },
        );
        logger.log('[upload] Enqueued fast-analyze-and-prime job', {
          docId: doc.id,
        });
      } catch (error) {
        logger.error('[upload] Failed to enqueue job:', error);
        // Don't fail the upload if job enqueuing fails
      }
    } catch {
      // If PDF parsing fails, UI can still operate lazily per page
    }
  } catch {}
  if (detectedChapters) {
    await setChapters(
      doc.id,
      detectedChapters.map((c) => ({
        name: c.name,
        startPageNumber: c.startPageNumber,
        level: c.level ?? 1,
      })),
    );
  }

  // If anonymous, include a short-lived document-scoped access token in response
  if (!ownerUserId && anonId) {
    try {
      const ua = req.headers.get('user-agent') || undefined;
      const ip = getClientIp(req) || undefined;
      const { ipHash, userAgentHash } = hashIpUa(ip, ua);
      const { token, exp } = await issueAnonDocToken(doc.id, anonId, {
        ipHash,
        userAgentHash,
      });
      return NextResponse.json({ docId: doc.id, token, type: 'anon_doc', exp });
    } catch {}
  }
  return NextResponse.json({ docId: doc.id });
}
export const runtime = 'nodejs';
