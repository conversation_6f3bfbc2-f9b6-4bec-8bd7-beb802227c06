import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import path from 'path';
import {
  authorizeDocumentAccess,
  appendAnonSidToUrlIfNeeded,
} from '@/lib/authz';
// reCAPTCHA no longer required; rely on Clerk/access tokens via authorizeDocumentAccess.

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id: pageId } = await params;
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: { document: true },
  });
  if (!page) return NextResponse.json({ error: 'Not found' }, { status: 404 });
  const allowed = await authorizeDocumentAccess(req, {
    id: page.documentId,
    userId: page.document?.userId || null,
    sessionId: page.document?.sessionId || null,
  });
  if (!allowed)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });

  // Strategy update: reCAPTCHA removed for chunk listings.
  const language = page.document?.targetLanguage || 'en';
  const voice = page.document?.voice || 'female';
  const chunksDir = [
    'documents',
    page.documentId,
    'pages',
    `page-${page.pageNumber}`,
    'audio',
    language,
    voice,
    'chunks',
  ].join('/');
  let total = null as number | null;
  try {
    const metaPath = path.posix.join(chunksDir, 'meta.json');
    const m = JSON.parse(await readTextFlexible(metaPath, 'utf-8'));
    if (typeof m?.total === 'number') total = m.total;
  } catch {}
  let available: number[] = [];
  if (total && total > 0) {
    const checks: number[] = [];
    for (let i = 1; i <= total; i++) checks.push(i);
    const results = await Promise.all(
      checks.map((i) =>
        fileExistsFlexible(path.posix.join(chunksDir, `chunk-${i}.mp3`)),
      ),
    );
    available = checks.filter((_, idx) => results[idx]);
  }
  const urls = await Promise.all(
    available.map(async (i) =>
      appendAnonSidToUrlIfNeeded(req, `/api/page/${pageId}/chunk/${i}`),
    ),
  );
  return NextResponse.json({ ok: true, total, available, urls });
}

import { fileExistsFlexible, readTextFlexible } from '@/lib/file-service';
export const runtime = 'nodejs';
