import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { auth } from '@clerk/nextjs/server';
import path from 'path';
import {
  saveTranslatedSentences,
  saveTranslationMeta,
  deletePagePdf,
} from '@/lib/storage';
import { PDFDocument } from 'pdf-lib';
import { translatePdfPageWithGemini } from '@/lib/ai/gemini';
import {
  fileExistsFlexible,
  readBufferFlexible,
  readTextFlexible,
  saveFile,
} from '@/lib/file-service';
import { appConfig } from '@/lib/config';
import { getRemainingListeningSeconds } from '@/lib/usage';
import {
  authorizeDocumentAccess,
  appendAnonSidToUrlIfNeeded,
} from '@/lib/authz';
import { streamTtsViaGenAiLive } from '@/lib/ai/providers/gemini'; // Barrel export
import { wrapPcmToWav, wavToMp3 } from '@/lib/ai/audio-utils';
import { synthesizeTtsGemini } from '@/lib/ai/tts';
import { ensureUserByClerkId } from '@/lib/user';
import {
  canGenerateAudio,
  checkAnonymousFreeTrialLimit,
} from '@/lib/plan-guard';
import { logger } from '@/lib/logger';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id: pageId } = await params;
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: { document: true },
  });
  if (!page) return NextResponse.json({ error: 'Not found' }, { status: 404 });

  // Authorization check
  const allowed = await authorizeDocumentAccess(req, {
    id: page.documentId,
    userId: page.document?.userId || null,
    sessionId: page.document?.sessionId || null,
  });
  if (!allowed)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });

  const language = page.document?.targetLanguage || 'en';
  const voice = page.document?.voice || 'female';
  const { userId: clerkUserId } = await auth();

  // Convert Clerk user ID to internal DB user ID for usage tracking
  let dbUserId: string | null = null;
  if (clerkUserId) {
    try {
      dbUserId = await ensureUserByClerkId(clerkUserId);
    } catch (e) {
      logger.error('[stream-tts] Failed to get DB user ID:', e);
    }
  }

  // Check if audio already exists and serve it if available
  const existingAudio = await prisma.pageAudio.findFirst({
    where: {
      pageId,
      language,
      voice,
      s3KeyAudioMp3: { not: null },
    },
  });
  if (
    existingAudio &&
    existingAudio.s3KeyAudioMp3 &&
    existingAudio.s3KeyAudioMp3.endsWith('.mp3') &&
    (await fileExistsFlexible(existingAudio.s3KeyAudioMp3))
  ) {
    // Audio is cached, serve it directly as MP3
    logger.log('[stream-tts] Audio already cached, serving file');
    try {
      const audioData = await readBufferFlexible(existingAudio.s3KeyAudioMp3);

      // Support Range requests for better seeking/streaming
      const range = req.headers.get('range');
      if (range) {
        const parts = range.replace(/bytes=/, '').split('-');
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : audioData.length - 1;
        const chunkSize = end - start + 1;
        const chunk = audioData.subarray(start, end + 1);

        return new Response(chunk, {
          status: 206,
          headers: {
            'Content-Type': 'audio/mpeg',
            'Content-Length': String(chunkSize),
            'Content-Range': `bytes ${start}-${end}/${audioData.length}`,
            'Accept-Ranges': 'bytes',
            'Cache-Control': 'private, max-age=3600',
          },
        });
      }

      return new Response(audioData, {
        headers: {
          'Content-Type': 'audio/mpeg',
          'Content-Length': String(audioData.length),
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'private, max-age=3600',
        },
      });
    } catch (error) {
      logger.error('[stream-tts] Failed to read cached audio:', error);
      // Fall through to regenerate if cached file is missing
    }
  }

  // Free trial gate for anonymous users
  try {
    if (!clerkUserId) {
      const FREE_TRIAL_SECONDS = appConfig.freeTrialSeconds;
      const anonCheck = await checkAnonymousFreeTrialLimit(
        page.documentId,
        FREE_TRIAL_SECONDS,
      );
      if (!anonCheck.allowed) {
        logger.log('[stream-tts] Anonymous free trial limit reached:', {
          documentId: page.documentId,
          usedSeconds: anonCheck.usedSeconds,
          limitSeconds: anonCheck.limitSeconds,
        });
        return NextResponse.json(
          {
            ok: false,
            requireLogin: true,
            reason: anonCheck.reason,
            usedSeconds: anonCheck.usedSeconds,
            limitSeconds: anonCheck.limitSeconds,
          },
          { status: 403 },
        );
      }
    }
  } catch {}

  // Check plan limits before doing any expensive translation or TTS work
  // This prevents wasting resources on users who can't use the audio anyway
  if (dbUserId) {
    const audioCheck = await canGenerateAudio(dbUserId);
    if (!audioCheck.allowed) {
      logger.log('[stream-tts] Plan limit reached, blocking translation:', {
        dbUserId,
        reason: audioCheck.reason,
        usedSeconds: audioCheck.usedSeconds,
        limitSeconds: audioCheck.limitSeconds,
      });
      return NextResponse.json(
        {
          ok: false,
          requireUpgrade: true,
          reason: audioCheck.reason,
          usedSeconds: audioCheck.usedSeconds,
          limitSeconds: audioCheck.limitSeconds,
          remainingSeconds: audioCheck.remainingSeconds,
        },
        { status: 403 },
      );
    }
  }

  // Ensure translated text exists
  let audio = await prisma.pageAudio.findFirst({
    where: {
      pageId,
      language,
      s3KeyTranslatedText: { not: null },
    },
  });

  if (!audio) {
    // Generate translation on-demand
    let pageBytes: Buffer | null = null;
    try {
      if (
        page.s3KeySinglePagePdf &&
        (await fileExistsFlexible(page.s3KeySinglePagePdf))
      ) {
        pageBytes = await readBufferFlexible(page.s3KeySinglePagePdf);
      }
    } catch {}

    if (!pageBytes) {
      const origPath = page.document?.s3KeyOriginal || '';
      if (!origPath) {
        return NextResponse.json(
          { error: 'Missing original' },
          { status: 400 },
        );
      }
      const origBuf = await readBufferFlexible(origPath);
      const src = await PDFDocument.load(origBuf);
      const single = await PDFDocument.create();
      const [p] = await single.copyPages(src, [page.pageNumber - 1]);
      single.addPage(p);
      const bytes = await single.save();
      pageBytes = Buffer.from(bytes);
    }

    // Determine carryover from previous page
    let carryoverText: string | undefined;
    try {
      if (page.pageNumber > 1) {
        const prev = await prisma.page.findFirst({
          where: {
            documentId: page.documentId,
            pageNumber: page.pageNumber - 1,
          },
        });
        if (prev) {
          const prevAudio = await prisma.pageAudio.findFirst({
            where: { pageId: prev.id, language },
          });
          if (prevAudio && prevAudio.s3KeyTranslatedText) {
            const metaDir = path.posix.dirname(prevAudio.s3KeyTranslatedText);
            const metaPath = path.posix.join(metaDir, 'meta.json');
            try {
              const metaRaw = await readTextFlexible(metaPath, 'utf-8');
              const meta = JSON.parse(metaRaw);
              if (
                meta &&
                meta.endsWithCompleteSentence === false &&
                Array.isArray(meta.sentences) &&
                meta.sentences.length > 0
              ) {
                carryoverText = meta.sentences[meta.sentences.length - 1];
              }
            } catch {}
          }
        }
      }
    } catch {}

    // Translation lock
    const translationsDir = [
      'documents',
      page.documentId,
      'pages',
      `page-${page.pageNumber}`,
      'translations',
      language,
    ].join('/');
    const translateLock = path.posix.join(translationsDir, '.lock');
    let hasTranslateLock = false;
    try {
      const existing = await prisma.pageAudio.findFirst({
        where: {
          pageId,
          language,
          s3KeyTranslatedText: { not: null },
        },
      });
      if (existing) audio = existing;
      if (!audio) {
        if (!(await fileExistsFlexible(translateLock))) {
          await saveFile(translateLock, String(Date.now()), 'text/plain');
          hasTranslateLock = true;
        }
      }
    } catch {}

    if (!audio) {
      const res = await translatePdfPageWithGemini({
        fileBytes: pageBytes,
        pageNumber: page.pageNumber,
        targetLanguage: language,
        carryoverText,
      });

      const textPath = await saveTranslatedSentences(
        page.documentId,
        page.pageNumber,
        language,
        res.translatedSentences || [],
      );

      await saveTranslationMeta(page.documentId, page.pageNumber, language, {
        sentences: res.sentences ?? [],
        endsWithCompleteSentence: res.endsWithCompleteSentence,
        isChapterStart: res.isChapterStart,
        isSkippable: res.isSkippable ?? false,
        detectedHeading: res.detectedHeading,
        headingConfidence: res.headingConfidence,
        modelVersion: res.modelVersion,
      });

      // Cleanup page PDF after successful translation (saves ~40-50% storage)
      if (appConfig.cleanupPagePdfsAfterTranslation) {
        await deletePagePdf(page.documentId, page.pageNumber);
      }

      const modelVersion = res.modelVersion || 'v1';
      audio = await prisma.pageAudio.upsert({
        where: {
          pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
            pageId,
            language,
            voice,
            ttsEngine: 'gemini',
            ttsParamsHash: 'default',
            modelVersion,
          },
        },
        update: { s3KeyTranslatedText: textPath },
        create: {
          pageId,
          language,
          voice,
          ttsEngine: 'gemini',
          ttsParamsHash: 'default',
          modelVersion,
          s3KeyTranslatedText: textPath,
        },
      });

      await prisma.page.update({
        where: { id: page.id },
        data: {
          endsWithCompleteSentence: res.endsWithCompleteSentence,
          isChapterStart: res.isChapterStart ?? false,
          isSkippableByDefault: res.isSkippable ?? false,
          status: 'processing',
        },
      });

      if (res.isSkippable) {
        return NextResponse.json({
          ok: true,
          skip: true,
          status: 'ready',
        });
      }
    }
  }

  // Ensure audio row exists for desired voice
  if (audio && audio.voice !== voice) {
    const mv = audio.modelVersion || 'v1';
    audio = await prisma.pageAudio.upsert({
      where: {
        pageId_language_voice_ttsEngine_ttsParamsHash_modelVersion: {
          pageId,
          language,
          voice,
          ttsEngine: 'gemini',
          ttsParamsHash: 'default',
          modelVersion: mv,
        },
      },
      update: { s3KeyTranslatedText: audio.s3KeyTranslatedText ?? undefined },
      create: {
        pageId,
        language,
        voice,
        ttsEngine: 'gemini',
        ttsParamsHash: 'default',
        modelVersion: mv,
        s3KeyTranslatedText: audio.s3KeyTranslatedText,
      },
    });
  }

  if (!audio || !audio.s3KeyTranslatedText) {
    return NextResponse.json({ error: 'No translated text' }, { status: 400 });
  }

  // Load translated sentences
  let ttsSentences: string[] = [];
  let endsWithCompleteSentenceFlag: boolean = Boolean(
    page.endsWithCompleteSentence,
  );

  try {
    const raw = await readTextFlexible(audio.s3KeyTranslatedText, 'utf-8');
    const arr = JSON.parse(raw);
    if (Array.isArray(arr)) ttsSentences = arr.map((s) => String(s));
  } catch {}

  try {
    const metaDir = path.posix.dirname(audio.s3KeyTranslatedText);
    const metaPath = path.posix.join(metaDir, 'meta.json');
    const metaRaw = await readTextFlexible(metaPath, 'utf-8');
    const meta = JSON.parse(metaRaw);
    if (meta && typeof meta.endsWithCompleteSentence === 'boolean') {
      endsWithCompleteSentenceFlag = meta.endsWithCompleteSentence;
    }
  } catch {}

  if (!endsWithCompleteSentenceFlag && ttsSentences.length > 0) {
    ttsSentences = ttsSentences.slice(0, -1);
  }

  const ttsText = ttsSentences.join(' ').trim();

  if (!ttsText) {
    return NextResponse.json(
      { error: 'No text to synthesize' },
      { status: 400 },
    );
  }

  // Note: Plan limit already checked earlier at line 149 before translation

  // Map language to Google TTS language codes
  function mapLang(l?: string | null): string | undefined {
    switch ((l || '').toLowerCase()) {
      case 'en':
        return 'en-US';
      case 'vi':
        return 'vi-VN';
      case 'es':
        return 'es-ES';
      case 'fr':
        return 'fr-FR';
      case 'de':
        return 'de-DE';
      case 'ja':
        return 'ja-JP';
      case 'zh':
        return 'cmn-CN';
      case 'zh-cn':
        return 'cmn-CN';
      case 'zh-tw':
        return 'cmn-TW';
      default:
        return undefined;
    }
  }
  const languageCode = mapLang(page.document?.targetLanguage);

  // Simpler approach: Stream from Gemini Live but buffer everything,
  // convert to MP3, and return complete audio (eliminates chunking gaps without complex client integration)
  // With fallback to non-streaming method if streaming fails
  let mp3Data: Buffer;

  try {
    logger.log('[stream-tts] Starting synthesis for page', page.pageNumber);

    const allChunks: Buffer[] = [];
    let chunkCount = 0;
    let totalBytes = 0;
    let firstChunkReceived = false;
    let streamStartTime = Date.now();

    // First chunk timeout (30 seconds) - if no chunk arrives, fail fast
    const firstChunkTimeout = 30000;
    // Overall stream timeout (5 minutes) - enough time for long text
    const overallStreamTimeout = 300000;

    const streamPromise = (async () => {
      try {
        // Collect all audio chunks from Gemini Live stream
        for await (const audioChunk of streamTtsViaGenAiLive(
          ttsText,
          voice,
          languageCode,
        )) {
          if (!firstChunkReceived) {
            const timeToFirstChunk = Date.now() - streamStartTime;
            logger.log('[stream-tts] First chunk arrived', {
              timeMs: timeToFirstChunk,
              bytes: audioChunk.length,
            });
            firstChunkReceived = true;
          }

          chunkCount++;
          totalBytes += audioChunk.length;
          allChunks.push(audioChunk);

          if (chunkCount % 5 === 0) {
            // Log every 5th chunk to reduce noise
            logger.log('[stream-tts] Progress', {
              chunks: chunkCount,
              totalBytes,
            });
          }
        }

        logger.log('[stream-tts] Stream iteration complete', {
          totalChunks: chunkCount,
          totalBytes,
          durationMs: Date.now() - streamStartTime,
        });
      } catch (e) {
        logger.error('[stream-tts] Stream iteration error:', {
          error: (e as Error)?.message || e,
          stack: (e as Error)?.stack,
          chunksReceived: chunkCount,
        });
        throw e;
      }
    })();

    // First wait for first chunk with short timeout
    const firstChunkPromise = new Promise<void>((resolve, reject) => {
      const checkInterval = setInterval(() => {
        if (firstChunkReceived) {
          clearInterval(checkInterval);
          clearTimeout(timeoutId);
          resolve();
        }
      }, 100);

      const timeoutId = setTimeout(() => {
        clearInterval(checkInterval);
        if (!firstChunkReceived) {
          reject(
            new Error(
              'First chunk timeout - no audio received within 30 seconds',
            ),
          );
        }
      }, firstChunkTimeout);
    });

    // Wait for first chunk or timeout
    await Promise.race([firstChunkPromise, streamPromise]);

    // If we got first chunk, wait for stream completion with longer timeout
    if (firstChunkReceived) {
      logger.log('[stream-tts] First chunk received, waiting for completion');
      const overallTimeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () =>
            reject(
              new Error(
                `Overall stream timeout after ${chunkCount} chunks received`,
              ),
            ),
          overallStreamTimeout,
        ),
      );

      await Promise.race([streamPromise, overallTimeoutPromise]);
    }

    if (allChunks.length === 0) {
      throw new Error('Stream completed but no audio chunks were received');
    }

    logger.log('[stream-tts] Stream complete', {
      totalChunks: chunkCount,
      totalBytes,
      durationMs: Date.now() - streamStartTime,
    });

    // Combine all audio chunks
    // GenAI SDK can return MP3, WAV, or PCM depending on config
    const combinedData = Buffer.concat(allChunks);

    // Check if it's already MP3 (starts with ID3 or sync marker 0xFF 0xFB)
    if (
      combinedData.length > 3 &&
      ((combinedData[0] === 0x49 &&
        combinedData[1] === 0x44 &&
        combinedData[2] === 0x33) || // ID3
        (combinedData[0] === 0xff && (combinedData[1] & 0xe0) === 0xe0)) // MPEG sync
    ) {
      // Already MP3
      logger.log('[stream-tts] Audio is already MP3 format');
      mp3Data = combinedData;
    } else if (
      combinedData.length > 44 &&
      combinedData.toString('ascii', 0, 4) === 'RIFF' &&
      combinedData.toString('ascii', 8, 12) === 'WAVE'
    ) {
      // It's WAV, convert to MP3
      logger.log('[stream-tts] Audio is WAV, converting to MP3');
      const converted = await wavToMp3(combinedData);
      if (!converted) throw new Error('WAV to MP3 conversion failed');
      mp3Data = converted;
    } else {
      // Assume PCM, wrap to WAV then convert
      logger.log(
        '[stream-tts] Audio is PCM, wrapping to WAV and converting to MP3',
      );
      const sampleRate = 24000;
      const wavData = wrapPcmToWav(combinedData, sampleRate);
      const converted = await wavToMp3(wavData);
      if (!converted) throw new Error('PCM to MP3 conversion failed');
      mp3Data = converted;
    }
  } catch (streamError) {
    logger.error(
      '[stream-tts] Streaming failed, falling back to batch synthesis:',
      {
        error: (streamError as Error)?.message || streamError,
        stack: (streamError as Error)?.stack,
      },
    );

    // Fallback to non-streaming synthesis with comprehensive fallbacks
    try {
      logger.log('[stream-tts] Using fallback batch synthesis');
      const result = await synthesizeTtsGemini(ttsText, voice, languageCode);
      if (!result.audioBytes) {
        throw new Error('Fallback synthesis returned null audio bytes');
      }
      mp3Data = result.audioBytes;
      logger.log('[stream-tts] Fallback synthesis succeeded', {
        bytes: mp3Data.length,
      });
    } catch (fallbackError) {
      logger.error('[stream-tts] Fallback synthesis also failed:', {
        error: (fallbackError as Error)?.message || fallbackError,
        stack: (fallbackError as Error)?.stack,
      });
      throw fallbackError;
    }
  }

  // Continue with saving and returning the audio
  try {
    // Calculate duration (rough estimate based on text length)
    const wordCount = ttsText.split(/\s+/).length;
    const durationSec = Math.max(1, Math.round(wordCount / 2.5));

    // Save to storage for caching
    const audioDir = [
      'documents',
      page.documentId,
      'pages',
      `page-${page.pageNumber}`,
      'audio',
      language,
      voice,
    ].join('/');
    const mp3Path = path.posix.join(audioDir, 'audio.mp3');

    await saveFile(mp3Path, mp3Data, 'audio/mpeg');

    // Update database
    await prisma.pageAudio.update({
      where: { id: audio.id },
      data: {
        s3KeyAudioMp3: mp3Path,
        duration: durationSec,
      },
    });

    await prisma.page.update({
      where: { id: page.id },
      data: { status: 'ready' },
    });

    // NOTE: Do NOT track usage during generation - only track during playback or claim
    // This prevents double-counting when audio is generated and then document is claimed

    logger.log('[stream-tts] Audio ready', {
      page: page.pageNumber,
      durationSec,
      mp3Path,
      mp3Bytes: mp3Data.length,
    });

    // Return the complete MP3 audio
    return new Response(mp3Data, {
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': String(mp3Data.length),
        'Cache-Control': 'private, max-age=3600',
      },
    });
  } catch (error) {
    logger.error('[stream-tts] Synthesis error', error);
    return NextResponse.json(
      { error: 'TTS synthesis failed' },
      { status: 500 },
    );
  }
}

export const runtime = 'nodejs';
