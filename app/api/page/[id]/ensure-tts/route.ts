import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { logger } from '@/lib/logger';
import { appendAnonSidToUrlIfNeeded } from '@/lib/authz';
import {
  validatePageAccess,
  checkStreamingMode,
  getDbUserId,
  checkAnonymousLimits,
  checkPlanLimits,
} from '@/lib/api/page/authorization';
import {
  buildAudioPaths,
  checkExistingAudio,
  checkExistingChunks,
} from '@/lib/api/page/audio-cache';
import { documentQueue, JOB_TYPES } from '@/lib/queue/config';
import { checkTranslationExists } from '@/lib/api/page/state-checker';
import { prisma } from '@/lib/prisma';

/**
 * Ensure TTS audio exists for a page
 *
 * This endpoint handles on-demand audio generation for pages beyond the first page.
 * For the first page, audio is generated by the background job queue.
 *
 * Note: This route does NOT update document.status - that's only done during first page processing.
 */
export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id: pageId } = await params;

  // Step 1: Validate page access
  const accessResult = await validatePageAccess(pageId, req);
  if (!accessResult.success) {
    return accessResult.response;
  }
  const { page } = accessResult;

  // Step 2: Check if streaming mode is enabled
  const streamingResponse = await checkStreamingMode(pageId, req);
  if (streamingResponse) {
    return streamingResponse;
  }

  // Step 3: Get user information
  const language = page.document?.targetLanguage || 'en';
  const voice = page.document?.voice || 'female';
  const { userId: clerkUserId } = await auth();
  const dbUserId = await getDbUserId(clerkUserId);

  // Step 4: Check for existing complete audio
  const existingAudioResponse = await checkExistingAudio(
    pageId,
    language,
    voice,
    req,
  );
  if (existingAudioResponse) {
    return existingAudioResponse;
  }

  // Step 5: Build audio paths (needed for later checks)
  const paths = buildAudioPaths(
    page.documentId,
    page.pageNumber,
    language,
    voice,
  );

  // Step 6: Check anonymous user limits
  const anonymousLimitResponse = await checkAnonymousLimits(
    page.documentId,
    clerkUserId,
  );
  if (anonymousLimitResponse) {
    return anonymousLimitResponse;
  }

  // Step 7: Check plan limits before expensive operations
  const planLimitResponse = await checkPlanLimits(dbUserId);
  if (planLimitResponse) {
    return planLimitResponse;
  }

  // Step 8: Fast Path - Check for existing audio/chunks (file-based)
  const earlyChunksResponse = await checkExistingChunks(
    pageId,
    paths.chunksDir,
    paths.chunksMetaPath,
    paths.mp3Path,
    language,
    voice,
    req,
  );
  if (earlyChunksResponse) {
    logger.log('[ensure-tts] Returning existing chunks', {
      pageId,
      page: page.pageNumber,
    });
    return earlyChunksResponse;
  }

  // Check if page is skippable
  if (page.isSkippableByDefault) {
    return NextResponse.json({
      ok: true,
      skip: true,
      status: 'ready',
    });
  }

  // Step 9: Determine required processing
  const translationExists = await checkTranslationExists(page, language);
  logger.log('[ensure-tts] Translation check', {
    pageId,
    page: page.pageNumber,
    translationExists,
  });

  // Clean up stale database records if translation file doesn't exist
  if (!translationExists) {
    const staleAudio = await prisma.pageAudio.findFirst({
      where: {
        pageId,
        language,
        s3KeyTranslatedText: { not: null },
      },
    });

    if (staleAudio) {
      logger.warn(
        '[ensure-tts] Found stale translation reference in DB, clearing',
        {
          pageId,
          page: page.pageNumber,
          audioId: staleAudio.id,
          s3Key: staleAudio.s3KeyTranslatedText,
        },
      );

      // Clear stale references
      await prisma.pageAudio.update({
        where: { id: staleAudio.id },
        data: {
          s3KeyTranslatedText: null,
          s3KeyAudioMp3: null, // Also clear audio reference since we're re-processing
        },
      });
    }
  }

  // Debug logging
  logger.log('[ensure-tts] Pre-job-enqueue state', {
    pageId,
    page: page.pageNumber,
    translationExists,
    pageStatus: page.status,
    isSkippable: page.isSkippableByDefault,
  });

  // Step 10: Check for running or failed jobs
  const jobIdPrefix = translationExists
    ? `${JOB_TYPES.GENERATE_PAGE_AUDIO}-${pageId}`
    : `${JOB_TYPES.PROCESS_PAGE}-${pageId}`;

  // For job uniqueness, include language and voice in jobId
  const jobId = `${jobIdPrefix}-${language}-${voice}`;

  let existingJob;
  try {
    existingJob = await documentQueue.getJob(jobId);
  } catch (error) {
    logger.error('[ensure-tts] Error checking for existing job', {
      pageId,
      jobId,
      error,
    });
    // Continue to enqueue - queue might not be available
  }

  if (existingJob) {
    const state = await existingJob.getState();

    logger.log('[ensure-tts] Found existing job', {
      pageId,
      page: page.pageNumber,
      jobId,
      state,
    });

    if (state === 'active' || state === 'waiting' || state === 'delayed') {
      // Job is running or queued
      const progress = (await existingJob.progress) as
        | {
            stage?: string;
            percent?: number;
          }
        | undefined;

      logger.log('[ensure-tts] Job in progress, returning status', {
        pageId,
        page: page.pageNumber,
        state,
        stage: progress?.stage,
      });

      return NextResponse.json({
        ok: false,
        status: 'processing',
        processingStage:
          progress?.stage ||
          (translationExists ? 'generating_audio' : 'translating'),
        jobId,
        retry: true,
      });
    }

    if (state === 'failed') {
      // Job failed after retries - remove it and retry with fresh job
      logger.warn('[ensure-tts] Found failed job, retrying', {
        pageId,
        page: page.pageNumber,
        jobId,
        failedReason: existingJob.failedReason,
      });

      // Remove failed job to clear it
      await existingJob.remove();
      logger.info('[ensure-tts] Removed failed job, will re-enqueue', {
        jobId,
      });

      // Fall through to normal enqueueing logic below
      // This allows the new job to benefit from all recovery mechanisms
    }

    // Job completed - re-check for chunks (should exist now)
    logger.log('[ensure-tts] Job completed, re-checking for chunks', {
      pageId,
      page: page.pageNumber,
      jobId,
    });

    const completedChunksResponse = await checkExistingChunks(
      pageId,
      paths.chunksDir,
      paths.chunksMetaPath,
      paths.mp3Path,
      language,
      voice,
      req,
    );
    if (completedChunksResponse) {
      logger.log('[ensure-tts] Chunks found after job completion', {
        pageId,
        page: page.pageNumber,
      });
      return completedChunksResponse;
    }

    logger.warn('[ensure-tts] Job completed but no chunks found', {
      pageId,
      page: page.pageNumber,
      jobId,
    });

    // Remove old job to allow re-processing
    await existingJob.remove();
    logger.info(
      '[ensure-tts] Removed completed job with missing chunks, will re-enqueue',
      { jobId },
    );
  }

  // Step 11: Enqueue appropriate job
  const jobType = translationExists
    ? JOB_TYPES.GENERATE_PAGE_AUDIO
    : JOB_TYPES.PROCESS_PAGE;

  logger.log('[ensure-tts] Enqueueing job', {
    pageId,
    page: page.pageNumber,
    jobType,
    jobId,
    translationExists,
  });

  try {
    const job = await documentQueue.add(
      jobType,
      {
        docId: page.documentId,
        pageId,
        pageNumber: page.pageNumber,
        targetLanguage: language,
        voice,
      },
      {
        jobId,
        priority: 10, // Lower priority than first-page jobs
        removeOnComplete: {
          age: 24 * 3600, // Keep for 24 hours
        },
        removeOnFail: {
          age: 7 * 24 * 3600, // Keep failures for 7 days
        },
      },
    );

    logger.log('[ensure-tts] Job enqueued successfully', {
      pageId,
      page: page.pageNumber,
      jobId: job.id,
      jobType,
    });

    // Step 12: Return processing response
    return NextResponse.json({
      ok: false,
      status: 'processing',
      processingStage: translationExists ? 'generating_audio' : 'translating',
      jobId: job.id,
      retry: true,
    });
  } catch (error) {
    logger.error('[ensure-tts] Failed to enqueue job', {
      pageId,
      page: page.pageNumber,
      jobType,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Check if the error is due to queue/Redis not being available
    const errorMessage =
      error instanceof Error ? error.message.toLowerCase() : '';
    if (
      errorMessage.includes('redis') ||
      errorMessage.includes('connection') ||
      errorMessage.includes('econnrefused')
    ) {
      return NextResponse.json(
        {
          error:
            'Job queue unavailable. Please ensure Redis and worker are running.',
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 503 },
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to enqueue processing job',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}

export const runtime = 'nodejs';
