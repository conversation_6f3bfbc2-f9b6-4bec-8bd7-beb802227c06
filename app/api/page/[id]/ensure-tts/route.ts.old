import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { saveFile } from '@/lib/file-service';
import { appendAnonSidToUrlIfNeeded } from '@/lib/authz';
import {
  validatePageAccess,
  checkStreamingMode,
  getDbUserId,
  checkAnonymousLimits,
  checkPlanLimits,
} from '@/lib/api/page/authorization';
import {
  buildAudioPaths,
  checkExistingAudio,
  checkExistingChunks,
  recheckAudioGeneration,
  checkChunksDuringSynthesis,
} from '@/lib/api/page/audio-cache';
import {
  generateTranslation,
  ensureAudioForVoice,
} from '@/lib/api/page/translation-generator';
import { prepareTtsText } from '@/lib/api/page/tts-preparation';
import {
  mapLanguageCode,
  applyTwoTierChunkingStrategy,
  applyVbeeChunkingStrategy,
} from '@/lib/api/page/chunking';
import {
  synthesizeFirstChunk,
  runBackgroundSynthesis,
} from '@/lib/api/page/audio-synthesis';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id: pageId } = await params;

  // Step 1: Validate page access
  const accessResult = await validatePageAccess(pageId, req);
  if (!accessResult.success) {
    return accessResult.response;
  }
  const { page } = accessResult;

  // Step 2: Check if streaming mode is enabled
  const streamingResponse = await checkStreamingMode(pageId, req);
  if (streamingResponse) {
    return streamingResponse;
  }

  // Step 3: Get user information
  const language = page.document?.targetLanguage || 'en';
  const voice = page.document?.voice || 'female';
  const { userId: clerkUserId } = await auth();
  const dbUserId = await getDbUserId(clerkUserId);

  // Step 4: Check for existing complete audio
  const existingAudioResponse = await checkExistingAudio(
    pageId,
    language,
    voice,
    req,
  );
  if (existingAudioResponse) {
    return existingAudioResponse;
  }

  // Step 5: Build audio paths (needed for later checks)
  const paths = buildAudioPaths(
    page.documentId,
    page.pageNumber,
    language,
    voice,
  );

  // Step 6: Check anonymous user limits
  const anonymousLimitResponse = await checkAnonymousLimits(
    page.documentId,
    clerkUserId,
  );
  if (anonymousLimitResponse) {
    return anonymousLimitResponse;
  }

  // Step 7: Check plan limits before expensive operations
  const planLimitResponse = await checkPlanLimits(dbUserId);
  if (planLimitResponse) {
    return planLimitResponse;
  }

  // Step 7.5: Check early for existing chunks (before checking translation)
  // This ensures we don't get stuck on "Translating..." when audio is already being generated
  const earlyChunksResponse = await checkExistingChunks(
    pageId,
    paths.chunksDir,
    paths.chunksMetaPath,
    paths.mp3Path,
    language,
    voice,
    req,
  );
  if (earlyChunksResponse) {
    return earlyChunksResponse;
  }

  // Step 7.6: Check if translation exists - kick off translation in background if not
  const existingTranslation = await prisma.pageAudio.findFirst({
    where: {
      pageId,
      language,
      s3KeyTranslatedText: { not: null },
    },
  });

  if (!existingTranslation) {
    const { logger } = await import('@/lib/logger');
    logger.log('[ensure-tts] Translation not found, checking lock', {
      pageId,
      page: page.pageNumber,
    });

    // Check if translation is already in progress by checking lock file
    const { fileExistsFlexible } = await import('@/lib/file-service');
    const translationsDir = [
      'documents',
      page.documentId,
      'pages',
      `page-${page.pageNumber}`,
      'translations',
      language,
    ].join('/');
    const translateLock = `${translationsDir}/.lock`;
    const lockExists = await fileExistsFlexible(translateLock);

    if (!lockExists) {
      logger.log('[ensure-tts] Starting background translation', {
        pageId,
        page: page.pageNumber,
      });
      // Translation not started yet - kick off in background
      (async () => {
        try {
          await generateTranslation(page, language, voice);
          logger.log('[ensure-tts] Background translation completed', {
            pageId,
            page: page.pageNumber,
          });
        } catch (error) {
          logger.error('[ensure-tts] Background translation failed:', error);
        }
      })().catch(() => {});
    } else {
      logger.log('[ensure-tts] Translation already in progress', {
        pageId,
        page: page.pageNumber,
      });
    }

    // Return translating status (whether we started it or it's already in progress)
    return NextResponse.json({
      ok: false,
      status: 'processing',
      processingStage: 'translating',
      retry: true,
    });
  }

  const { logger } = await import('@/lib/logger');
  logger.log('[ensure-tts] Translation found, proceeding to audio', {
    pageId,
    page: page.pageNumber,
  });

  // Step 8: Get translation (exists from check above or previous request)
  const translationResult = await generateTranslation(page, language, voice);

  if (!translationResult) {
    return NextResponse.json({ error: 'Translation failed' }, { status: 500 });
  }

  let audio = translationResult.audio;

  // If page is skippable, return early
  if (translationResult.isSkippable) {
    return NextResponse.json({
      ok: true,
      skip: true,
      status: 'ready',
      processingStage: 'complete',
    });
  }

  // Step 9: Ensure audio row exists for desired voice
  if (audio.voice !== voice) {
    audio = await ensureAudioForVoice(pageId, language, voice, audio);
  }

  if (!audio || !audio.s3KeyTranslatedText) {
    return NextResponse.json({ error: 'No translated text' }, { status: 400 });
  }

  // Step 10: Re-check if another request already produced audio
  const recheckResponse = await recheckAudioGeneration(
    pageId,
    language,
    voice,
    paths.mp3Path,
    audio.id,
    req,
  );
  if (recheckResponse) {
    return recheckResponse;
  }

  // Step 11: Prepare TTS text
  const { ttsSentences } = await prepareTtsText(
    audio.s3KeyTranslatedText,
    Boolean(page.endsWithCompleteSentence),
  );

  // Step 12: Apply chunking strategy based on TTS provider
  const useVbee = !!process.env.VBEE_TOKEN;
  const { ttsChunks, chunkModels } = useVbee
    ? applyVbeeChunkingStrategy(ttsSentences)
    : applyTwoTierChunkingStrategy(ttsSentences);

  // Step 13: Map language code
  const languageCode = mapLanguageCode(page.document?.targetLanguage);

  // Step 14: Write chunks metadata
  try {
    await saveFile(
      paths.chunksMetaPath,
      JSON.stringify({ total: ttsChunks.length }, null, 2),
      'application/json',
    );
  } catch {}

  // Step 15: Check for existing chunks during synthesis
  const { response: chunksResponse } = await checkChunksDuringSynthesis(
    pageId,
    paths.chunksDir,
    paths.chunksMetaPath,
    paths.mp3Path,
    audio.id,
    req,
  );

  if (chunksResponse) {
    return chunksResponse;
  }

  // Step 16: Synthesize first chunk
  const { firstBytes, firstDurationSec, isAsync } = await synthesizeFirstChunk(
    ttsChunks,
    audio.voice,
    languageCode,
    chunkModels,
    page.pageNumber,
    language,
    page.documentId,
    paths.chunksDir,
  );

  const firstPath = `${paths.chunksDir}/chunk-1.mp3`;

  // Save first chunk only if it's synchronous (Gemini)
  if (firstBytes) {
    await saveFile(firstPath, firstBytes, 'audio/mpeg');
  }

  // Step 17: Kick off background synthesis for remaining chunks
  (async () => {
    await runBackgroundSynthesis(
      ttsChunks,
      chunkModels,
      audio.voice,
      languageCode,
      paths.chunksDir,
      paths.chunksMetaPath,
      audio.id,
      page.id,
      page.documentId,
      page.pageNumber,
      audio.language,
      firstDurationSec,
      dbUserId,
    );
  })().catch(() => {});

  // Step 18: If async (Vbee), wait for chunk 1 to be ready before responding
  if (isAsync) {
    const { fileExistsFlexible } = await import('@/lib/file-service');
    const { logger } = await import('@/lib/logger');

    logger.log('[ensure-tts] Waiting for Vbee chunk 1 via webhook...', {
      pageId,
      chunkPath: firstPath,
    });

    // Poll for chunk 1 to be ready (max 30 seconds)
    const maxWaitMs = 30000;
    const pollIntervalMs = 500;
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitMs) {
      if (await fileExistsFlexible(firstPath)) {
        logger.log('[ensure-tts] Chunk 1 ready from Vbee webhook', {
          pageId,
          waitedMs: Date.now() - startTime,
        });

        // Chunk 1 is ready! Return it to frontend
        return NextResponse.json({
          ok: true,
          chunked: true,
          total: ttsChunks.length,
          available: [1],
          urls: [
            await appendAnonSidToUrlIfNeeded(
              req,
              `/api/page/${pageId}/chunk/1`,
            ),
          ],
          status: 'processing',
          processingStage: 'generating_audio',
        });
      }

      // Wait before next check
      await new Promise((resolve) => setTimeout(resolve, pollIntervalMs));
    }

    // Timeout - chunk 1 didn't arrive in time
    logger.error('[ensure-tts] Timeout waiting for Vbee chunk 1', {
      pageId,
      waitedMs: maxWaitMs,
    });

    return NextResponse.json(
      {
        error: 'Timeout waiting for audio generation',
        status: 'timeout',
      },
      { status: 504 },
    );
  } else {
    // First chunk ready immediately (Gemini)
    return NextResponse.json({
      ok: true,
      chunked: true,
      total: ttsChunks.length,
      available: [1],
      urls: [
        await appendAnonSidToUrlIfNeeded(req, `/api/page/${pageId}/chunk/1`),
      ],
      status: 'processing',
      processingStage: 'generating_audio',
    });
  }
}

export const runtime = 'nodejs';
