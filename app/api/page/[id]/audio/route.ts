import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authorizeDocumentAccess } from '@/lib/authz';
import { fileExistsFlexible } from '@/lib/file-service';
import { appendAnonSidToUrlIfNeeded } from '@/lib/authz';
import { logger } from '@/lib/logger';

/**
 * GET /api/page/[id]/audio
 *
 * Retrieves audio chunks for a page (read-only, no processing)
 *
 * Returns:
 * - If ready: { ok: true, chunked: true, total, available: [1,2,3...], urls: [...] }
 * - If processing: { ok: false, status: 'processing', processingStage: '...' }
 * - If not started: { ok: false, status: 'not_started' }
 * - If skippable: { ok: true, skip: true }
 */
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  const { id: pageId } = await params;

  // Fetch page with document
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: {
      document: true,
    },
  });

  if (!page || !page.document) {
    return NextResponse.json({ error: 'Page not found' }, { status: 404 });
  }

  // Authorization check
  const allowed = await authorizeDocumentAccess(req, {
    id: page.documentId,
    userId: page.document.userId,
    sessionId: page.document.sessionId,
  });

  if (!allowed) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  }

  const language = page.document.targetLanguage || 'en';
  const voice = page.document.voice || 'female';

  // Check if page is skippable
  if (page.isSkippableByDefault) {
    return NextResponse.json({
      ok: true,
      skip: true,
      skippable: true,
    });
  }

  // Build paths
  const chunksDir = [
    'documents',
    page.documentId,
    'pages',
    `page-${page.pageNumber}`,
    'audio',
    language,
    voice,
    'chunks',
  ].join('/');

  const chunksMetaPath = `${chunksDir}/meta.json`;
  const mp3Path = [
    'documents',
    page.documentId,
    'pages',
    `page-${page.pageNumber}`,
    'audio',
    language,
    voice,
    'audio.mp3',
  ].join('/');

  // Check for complete audio (non-chunked)
  const hasCompleteAudio = await fileExistsFlexible(mp3Path);
  if (hasCompleteAudio) {
    const url = await appendAnonSidToUrlIfNeeded(
      req,
      `/api/page/${pageId}/stream`,
    );
    return NextResponse.json({
      ok: true,
      chunked: false,
      url,
      status: 'ready',
      processingStage: 'complete',
    });
  }

  // Check for chunked audio
  const hasChunksMeta = await fileExistsFlexible(chunksMetaPath);

  if (!hasChunksMeta) {
    // No audio exists yet - check processing status

    // Check if translation exists
    const translation = await prisma.pageAudio.findFirst({
      where: {
        pageId,
        language,
        s3KeyTranslatedText: { not: null },
      },
    });

    if (!translation) {
      // Check if this is the first page and document is still being processed
      if (page.pageNumber === 1) {
        const docStatus = page.document.status;
        let processingStage = 'processing_file';

        if (docStatus === 'translating_first_page') {
          processingStage = 'translating';
        } else if (docStatus === 'translated_first_page') {
          processingStage = 'generating_audio';
        } else if (
          docStatus === 'generating_audio_first_page' ||
          docStatus === 'generated_audio_first_page'
        ) {
          processingStage = 'generating_audio';
        }

        return NextResponse.json({
          ok: false,
          status: 'processing',
          processingStage,
        });
      }

      // Not first page and no translation - not started yet
      return NextResponse.json({
        ok: false,
        status: 'not_started',
        processingStage: 'not_started',
      });
    }

    // Translation exists but no audio - audio is being generated
    return NextResponse.json({
      ok: false,
      status: 'processing',
      processingStage: 'generating_audio',
    });
  }

  // Chunks metadata exists - check which chunks are available
  try {
    const { total } = JSON.parse(
      await (
        await import('@/lib/file-service')
      ).readTextFlexible(chunksMetaPath),
    );

    // Parallelize chunk existence checks for better performance
    const chunkIndices = Array.from({ length: total }, (_, i) => i + 1);
    const existsResults = await Promise.all(
      chunkIndices.map(async (i) => {
        const chunkPath = `${chunksDir}/chunk-${i}.mp3`;
        const exists = await fileExistsFlexible(chunkPath);
        return { index: i, exists };
      }),
    );

    const available: number[] = [];
    const urls: string[] = [];

    for (const { index, exists } of existsResults) {
      if (exists) {
        available.push(index);
        urls.push(
          await appendAnonSidToUrlIfNeeded(
            req,
            `/api/page/${pageId}/chunk/${index}`,
          ),
        );
      }
    }

    if (available.length === 0) {
      // Metadata exists but no chunks yet - audio is being generated
      return NextResponse.json({
        ok: false,
        status: 'processing',
        processingStage: 'generating_audio',
      });
    }

    // Return available chunks
    return NextResponse.json({
      ok: true,
      chunked: true,
      total,
      available,
      urls,
      status: available.length === total ? 'ready' : 'processing',
      processingStage:
        available.length === total ? 'complete' : 'generating_audio',
    });
  } catch (error) {
    logger.error('[audio] Error reading chunks metadata:', error);
    return NextResponse.json(
      { error: 'Failed to read audio metadata' },
      { status: 500 },
    );
  }
}
