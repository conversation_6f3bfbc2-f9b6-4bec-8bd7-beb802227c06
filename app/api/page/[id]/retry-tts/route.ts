import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { logger } from '@/lib/logger';
import { pollVbeeResult, downloadVbeeAudio } from '@/lib/ai/providers/vbee';
import { saveFile } from '@/lib/file-service';
import { Queue } from 'bullmq';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id: pageId } = await params;

    // Get page and document info
    const page = await prisma.page.findUnique({
      where: { id: pageId },
      include: { document: true },
    });

    if (!page) {
      return NextResponse.json({ error: 'Page not found' }, { status: 404 });
    }

    const { targetLanguage: language, voice } = page.document;

    if (!language || !voice) {
      return NextResponse.json(
        { error: 'Language or voice not configured' },
        { status: 400 },
      );
    }

    // Look for existing Vbee request
    const vbeeRequest = await prisma.vbeeTtsRequest.findFirst({
      where: { pageId, status: 'pending' },
      orderBy: { createdAt: 'desc' },
    });

    if (vbeeRequest?.requestId) {
      logger.info('[retry-tts] Found pending request, polling Vbee', {
        requestId: vbeeRequest.requestId,
      });

      // Try to poll Vbee for result
      const pollResult = await pollVbeeResult(vbeeRequest.requestId);

      if (pollResult?.audioLink) {
        // Download and save audio
        const audioResult = await downloadVbeeAudio(pollResult.audioLink);

        if (audioResult && audioResult.audioBytes) {
          const chunkPath = vbeeRequest.chunkId;
          await saveFile(chunkPath, audioResult.audioBytes, 'audio/mpeg');

          // Mark as completed
          await prisma.vbeeTtsRequest.update({
            where: { id: vbeeRequest.id },
            data: { status: 'completed', audioLink: pollResult.audioLink },
          });

          return NextResponse.json({
            success: true,
            message: 'Audio retrieved via polling',
          });
        }
      }
    }

    // If polling failed or no request found, re-enqueue job
    // Note: Queue handling would go here - skipping for now
    // as getQueue is not available in this context
    return NextResponse.json(
      { error: 'Queue functionality not available in this route' },
      { status: 501 },
    );

    /* Original queue code - commented out
    const queue = getQueue();
    if (!queue) {
      return NextResponse.json(
        { error: 'Queue unavailable' },
        { status: 503 },
      );
    }

    const jobId = `generate-page-audio-${pageId}-${language}-${voice}`;
    const existingJob = await queue.getJob(jobId);
    if (existingJob) {
      await existingJob.remove();
    }

    await queue.add(
      'generate-page-audio',
      { pageId, language, voice, documentId: page.documentId },
      {
        jobId,
        priority: 10,
        removeOnComplete: { age: 24 * 3600 },
        removeOnFail: { age: 7 * 24 * 3600 },
      },
    );

    return NextResponse.json({
      success: true,
      message: 'Retry job enqueued',
    });
    */
  } catch (error) {
    logger.error('[retry-tts] Error:', error);
    return NextResponse.json({ error: 'Retry failed' }, { status: 500 });
  }
}
