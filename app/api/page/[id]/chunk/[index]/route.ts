import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import path from 'path';
import { authorizeDocumentAccess } from '@/lib/authz';
import { getFileByKey, fileExistsFlexible } from '@/lib/file-service';

// HEAD method to check if chunk exists without downloading
export async function HEAD(
  req: Request,
  { params }: { params: Promise<{ id: string; index: string }> },
) {
  const { id: pageId, index } = await params;
  const idx = Number(index || '0');
  if (!Number.isFinite(idx) || idx <= 0)
    return new NextResponse(null, { status: 400 });
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: { document: true },
  });
  if (!page) return new NextResponse(null, { status: 404 });
  const allowed = await authorizeDocumentAccess(req, {
    id: page.documentId,
    userId: page.document?.userId || null,
    sessionId: page.document?.sessionId || null,
  });
  if (!allowed) return new NextResponse(null, { status: 403 });
  const language = page.document?.targetLanguage || 'en';
  const voice = page.document?.voice || 'female';
  const chunkPath = [
    'documents',
    page.documentId,
    'pages',
    `page-${page.pageNumber}`,
    'audio',
    language,
    voice,
    'chunks',
    `chunk-${idx}.mp3`,
  ].join('/');
  try {
    const exists = await fileExistsFlexible(chunkPath);
    if (exists) {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Content-Type': 'audio/mpeg',
          'Cache-Control': 'private, no-store',
        },
      });
    }
    return new NextResponse(null, { status: 404 });
  } catch {
    return new NextResponse(null, { status: 404 });
  }
}

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string; index: string }> },
) {
  const { id: pageId, index } = await params;
  const idx = Number(index || '0');
  if (!Number.isFinite(idx) || idx <= 0)
    return NextResponse.json({ error: 'Bad index' }, { status: 400 });
  const page = await prisma.page.findUnique({
    where: { id: pageId },
    include: { document: true },
  });
  if (!page) return NextResponse.json({ error: 'Not found' }, { status: 404 });
  const allowed = await authorizeDocumentAccess(req, {
    id: page.documentId,
    userId: page.document?.userId || null,
    sessionId: page.document?.sessionId || null,
  });
  if (!allowed)
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
  const language = page.document?.targetLanguage || 'en';
  const voice = page.document?.voice || 'female';
  const chunkPath = [
    'documents',
    page.documentId,
    'pages',
    `page-${page.pageNumber}`,
    'audio',
    language,
    voice,
    'chunks',
    `chunk-${idx}.mp3`,
  ].join('/');
  try {
    const bytes = await getFileByKey(chunkPath);
    const total = bytes.length;
    const range = req.headers.get('range');
    const send = (start: number, end: number, partial: boolean) => {
      const s = Math.max(0, Math.min(start || 0, total - 1));
      const e = Math.max(0, Math.min(end == null ? total - 1 : end, total - 1));
      const body = bytes.subarray(s, e + 1);
      const headers: Record<string, string> = {
        'Content-Type': 'audio/mpeg',
        'Accept-Ranges': 'bytes',
        'Content-Length': String(body.length),
        Vary: 'Cookie',
        'Cache-Control': 'private, no-store',
      };
      if (partial) headers['Content-Range'] = `bytes ${s}-${e}/${total}`;
      return new NextResponse(body, { status: partial ? 206 : 200, headers });
    };
    if (range && /^bytes=\d*-\d*$/.test(range)) {
      const [startStr, endStr] = range.replace(/bytes=/, '').split('-');
      const start = startStr ? parseInt(startStr, 10) : 0;
      const end = endStr ? parseInt(endStr, 10) : total - 1;
      return send(start, end, true);
    }
    return send(0, total - 1, false);
  } catch {
    return NextResponse.json({ error: 'Not found' }, { status: 404 });
  }
}

export const runtime = 'nodejs';
