'use client';

import { useState } from 'react';
import { useQ<PERSON>y, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Textarea,
  Divider,
  Chip,
  Avatar,
  <PERSON>ner,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Modal<PERSON><PERSON><PERSON>,
  useDisclosure,
} from '@heroui/react';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  createdAt: string;
  _count?: {
    posts: number;
    comments: number;
  };
}

interface Post {
  id: string;
  title: string;
  content: string;
  slug: string;
  published: boolean;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  _count?: {
    comments: number;
    tags: number;
  };
}

// API functions
const fetchUsers = async (): Promise<{ users: User[]; total: number }> => {
  const response = await fetch('/api/users');
  if (!response.ok) throw new Error('Failed to fetch users');
  const data = await response.json();
  return data.data;
};

const fetchPosts = async (): Promise<{ posts: Post[]; total: number }> => {
  const response = await fetch('/api/posts');
  if (!response.ok) throw new Error('Failed to fetch posts');
  const data = await response.json();
  return data.data;
};

const createUser = async (userData: { name: string; email: string; avatar?: string }) => {
  const response = await fetch('/api/users', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData),
  });
  if (!response.ok) throw new Error('Failed to create user');
  return response.json();
};

export default function ApiTest() {
  const queryClient = useQueryClient();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [newUser, setNewUser] = useState({ name: '', email: '', avatar: '' });

  // Queries
  const {
    data: usersData,
    isLoading: usersLoading,
    error: usersError,
  } = useQuery({
    queryKey: ['users'],
    queryFn: fetchUsers,
  });

  const {
    data: postsData,
    isLoading: postsLoading,
    error: postsError,
  } = useQuery({
    queryKey: ['posts'],
    queryFn: fetchPosts,
  });

  // Mutations
  const createUserMutation = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setNewUser({ name: '', email: '', avatar: '' });
      onClose();
    },
  });

  const handleCreateUser = () => {
    if (newUser.name && newUser.email) {
      createUserMutation.mutate(newUser);
    }
  };

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">API Dashboard</h1>
        <p className="text-lg text-gray-600">
          Manage users and posts with your Prisma-powered backend
        </p>
      </div>

      {/* Actions */}
      <div className="flex gap-4 justify-center">
        <Button color="primary" onPress={onOpen}>
          Create User
        </Button>
        <Button
          color="secondary"
          onPress={() => queryClient.invalidateQueries({ queryKey: ['users'] })}
        >
          Refresh Users
        </Button>
        <Button
          color="secondary"
          onPress={() => queryClient.invalidateQueries({ queryKey: ['posts'] })}
        >
          Refresh Posts
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

        {/* Users Section */}
        <Card>
          <CardHeader className="flex gap-3">
            <div className="flex flex-col">
              <p className="text-md font-semibold">Users</p>
              <p className="text-small text-default-500">
                {usersData?.total || 0} total users
              </p>
            </div>
          </CardHeader>
          <Divider />
          <CardBody>
            {usersLoading ? (
              <div className="flex justify-center p-4">
                <Spinner />
              </div>
            ) : usersError ? (
              <p className="text-danger">Error loading users</p>
            ) : (
              <div className="space-y-4">
                {usersData?.users.map((user) => (
                  <div key={user.id} className="flex items-center gap-3 p-3 border rounded-lg">
                    <Avatar
                      src={user.avatar}
                      name={user.name}
                      size="sm"
                    />
                    <div className="flex-1">
                      <p className="font-semibold">{user.name}</p>
                      <p className="text-small text-default-500">{user.email}</p>
                    </div>
                    <div className="flex gap-2">
                      <Chip size="sm" variant="flat">
                        {user._count?.posts || 0} posts
                      </Chip>
                      <Chip size="sm" variant="flat">
                        {user._count?.comments || 0} comments
                      </Chip>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>

        {/* Posts Section */}
        <Card>
          <CardHeader className="flex gap-3">
            <div className="flex flex-col">
              <p className="text-md font-semibold">Posts</p>
              <p className="text-small text-default-500">
                {postsData?.total || 0} total posts
              </p>
            </div>
          </CardHeader>
          <Divider />
          <CardBody>
            {postsLoading ? (
              <div className="flex justify-center p-4">
                <Spinner />
              </div>
            ) : postsError ? (
              <p className="text-danger">Error loading posts</p>
            ) : (
              <div className="space-y-4">
                {postsData?.posts.map((post) => (
                  <div key={post.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold">{post.title}</h3>
                      <Chip
                        size="sm"
                        color={post.published ? 'success' : 'warning'}
                        variant="flat"
                      >
                        {post.published ? 'Published' : 'Draft'}
                      </Chip>
                    </div>
                    <p className="text-small text-default-600 mb-3 line-clamp-2">
                      {post.content}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar
                          src={post.author.avatar}
                          name={post.author.name}
                          size="sm"
                        />
                        <span className="text-small">{post.author.name}</span>
                      </div>
                      <div className="flex gap-2">
                        <Chip size="sm" variant="flat">
                          {post._count?.comments || 0} comments
                        </Chip>
                        <Chip size="sm" variant="flat">
                          {post._count?.tags || 0} tags
                        </Chip>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* Create User Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">Create New User</ModalHeader>
          <ModalBody>
            <Input
              label="Name"
              placeholder="Enter user name"
              value={newUser.name}
              onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
            />
            <Input
              label="Email"
              placeholder="Enter email address"
              type="email"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
            />
            <Input
              label="Avatar URL (optional)"
              placeholder="Enter avatar URL"
              value={newUser.avatar}
              onChange={(e) => setNewUser({ ...newUser, avatar: e.target.value })}
            />
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleCreateUser}
              isLoading={createUserMutation.isPending}
            >
              Create User
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
}
