import { NextResponse } from 'next/server';

// Common response helpers
export class ApiResponse {
  static success(data: any, status: number = 200) {
    return NextResponse.json({
      success: true,
      data,
      timestamp: new Date().toISOString()
    }, { status });
  }

  static error(message: string, status: number = 400, details?: any) {
    return NextResponse.json({
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString()
    }, { status });
  }

  static notFound(resource: string = 'Resource') {
    return this.error(`${resource} not found`, 404);
  }

  static badRequest(message: string = 'Bad request') {
    return this.error(message, 400);
  }

  static unauthorized(message: string = 'Unauthorized') {
    return this.error(message, 401);
  }

  static forbidden(message: string = 'Forbidden') {
    return this.error(message, 403);
  }

  static conflict(message: string = 'Conflict') {
    return this.error(message, 409);
  }

  static internalError(message: string = 'Internal server error') {
    return this.error(message, 500);
  }
}

// Validation helpers
export class Validator {
  static isEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static isRequired(value: any): boolean {
    return value !== undefined && value !== null && value !== '';
  }

  static minLength(value: string, min: number): boolean {
    return value && value.length >= min;
  }

  static maxLength(value: string, max: number): boolean {
    return value && value.length <= max;
  }

  static validateUser(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.isRequired(data.name)) {
      errors.push('Name is required');
    } else if (!this.minLength(data.name, 2)) {
      errors.push('Name must be at least 2 characters long');
    }

    if (!this.isRequired(data.email)) {
      errors.push('Email is required');
    } else if (!this.isEmail(data.email)) {
      errors.push('Email must be a valid email address');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Utility functions
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function parseQueryParams(url: string) {
  const { searchParams } = new URL(url);
  const params: Record<string, string> = {};
  
  searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
}

export async function parseRequestBody(request: Request) {
  try {
    return await request.json();
  } catch (error) {
    throw new Error('Invalid JSON in request body');
  }
}
