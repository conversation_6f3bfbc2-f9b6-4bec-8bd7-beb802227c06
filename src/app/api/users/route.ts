import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ApiResponse, Validator } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    const take = limit ? parseInt(limit) : undefined;
    const skip = offset ? parseInt(offset) : undefined;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        take,
        skip,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          email: true,
          avatar: true,
          createdAt: true,
          _count: {
            select: {
              posts: true,
              comments: true,
            },
          },
        },
      }),
      prisma.user.count(),
    ]);

    return ApiResponse.success({
      users,
      total,
      pagination: {
        offset: skip || 0,
        limit: take || total,
        hasMore: skip !== undefined && take !== undefined ? skip + take < total : false,
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return ApiResponse.internalError('Failed to fetch users');
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, avatar } = body;

    // Validate input
    const validation = Validator.validateUser({ name, email });
    if (!validation.isValid) {
      return ApiResponse.badRequest('Validation failed', { errors: validation.errors });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return ApiResponse.conflict('User with this email already exists');
    }

    // Create new user
    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        avatar,
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        createdAt: true,
      },
    });

    return ApiResponse.success(
      {
        message: 'User created successfully',
        user: newUser,
      },
      201
    );
  } catch (error) {
    if (error instanceof Error && error.message.includes('JSON')) {
      return ApiResponse.badRequest('Invalid JSON in request body');
    }
    console.error('Error creating user:', error);
    return ApiResponse.internalError('Failed to create user');
  }
}
