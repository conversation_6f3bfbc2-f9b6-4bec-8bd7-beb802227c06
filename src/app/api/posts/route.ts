import { NextRequest } from 'next/server';
import { ApiResponse, Validator, parseQueryParams, parseRequestBody } from '@/lib/api-utils';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const params = parseQueryParams(request.url);
    const { limit, offset, author, published } = params;

    const take = limit ? parseInt(limit) : undefined;
    const skip = offset ? parseInt(offset) : undefined;

    // Build where clause
    const where: any = {};

    if (author) {
      where.author = {
        name: {
          contains: author,
          mode: 'insensitive',
        },
      };
    }

    if (published !== undefined) {
      where.published = published === 'true';
    }

    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        take,
        skip,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          title: true,
          content: true,
          slug: true,
          published: true,
          createdAt: true,
          updatedAt: true,
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          _count: {
            select: {
              comments: true,
              tags: true,
            },
          },
        },
      }),
      prisma.post.count({ where }),
    ]);

    return ApiResponse.success({
      posts,
      total,
      pagination: {
        offset: skip || 0,
        limit: take || total,
        hasMore: skip !== undefined && take !== undefined ? skip + take < total : false,
      },
    });
  } catch (error) {
    console.error('Error fetching posts:', error);
    return ApiResponse.internalError('Failed to fetch posts');
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await parseRequestBody(request);
    const { title, content, authorId, published = false } = body;

    // Validate required fields
    const errors: string[] = [];
    if (!Validator.isRequired(title)) errors.push('Title is required');
    if (!Validator.isRequired(content)) errors.push('Content is required');
    if (!Validator.isRequired(authorId)) errors.push('Author ID is required');

    if (!Validator.minLength(title, 3)) errors.push('Title must be at least 3 characters long');
    if (!Validator.minLength(content, 10)) errors.push('Content must be at least 10 characters long');

    if (errors.length > 0) {
      return ApiResponse.badRequest('Validation failed', { errors });
    }

    // Check if author exists
    const author = await prisma.user.findUnique({
      where: { id: authorId },
    });

    if (!author) {
      return ApiResponse.badRequest('Author not found');
    }

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug already exists
    const existingPost = await prisma.post.findUnique({
      where: { slug },
    });

    if (existingPost) {
      return ApiResponse.conflict('A post with this title already exists');
    }

    // Create new post
    const newPost = await prisma.post.create({
      data: {
        title,
        content,
        slug,
        published,
        authorId,
      },
      select: {
        id: true,
        title: true,
        content: true,
        slug: true,
        published: true,
        createdAt: true,
        author: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
    });

    return ApiResponse.success(
      {
        message: 'Post created successfully',
        post: newPost,
      },
      201
    );
  } catch (error) {
    if (error instanceof Error && error.message === 'Invalid JSON in request body') {
      return ApiResponse.badRequest('Invalid JSON in request body');
    }
    console.error('Error creating post:', error);
    return ApiResponse.internalError('Failed to create post');
  }
}
