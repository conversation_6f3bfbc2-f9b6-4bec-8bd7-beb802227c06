import Image from "next/image";
import ApiTest from "@/components/api-test";

export default function Home() {
  return (
    <div className="font-sans min-h-screen">
      {/* Header */}
      <header className="border-b bg-white/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Image
              className="dark:invert"
              src="/next.svg"
              alt="Next.js logo"
              width={120}
              height={25}
              priority
            />
            <span className="text-xl font-semibold">TransRead</span>
          </div>
          <div className="text-sm text-gray-600">
            Backend API Ready ✅
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">Welcome to TransRead</h1>
          <p className="text-lg text-gray-600 mb-6">
            Your Next.js application with a fully configured backend API is ready to go!
          </p>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-green-800 mb-2">✅ Backend Setup Complete</h2>
            <ul className="text-green-700 space-y-1">
              <li>• API Routes configured with TypeScript</li>
              <li>• CRUD operations for Users and Posts</li>
              <li>• Request validation and error handling</li>
              <li>• CORS middleware configured</li>
              <li>• Database utilities ready for integration</li>
            </ul>
          </div>
        </div>

        {/* API Test Component */}
        <ApiTest />
      </main>

      {/* Footer */}
      <footer className="border-t bg-gray-50 mt-16">
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="flex gap-6 flex-wrap items-center justify-center text-sm">
            <a
              className="flex items-center gap-2 hover:underline hover:underline-offset-4 text-gray-600 hover:text-gray-900"
              href="https://nextjs.org/docs/app/building-your-application/routing/route-handlers"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                aria-hidden
                src="/file.svg"
                alt="File icon"
                width={16}
                height={16}
              />
              API Routes Docs
            </a>
            <a
              className="flex items-center gap-2 hover:underline hover:underline-offset-4 text-gray-600 hover:text-gray-900"
              href="https://nextjs.org/docs"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                aria-hidden
                src="/window.svg"
                alt="Window icon"
                width={16}
                height={16}
              />
              Next.js Docs
            </a>
            <a
              className="flex items-center gap-2 hover:underline hover:underline-offset-4 text-gray-600 hover:text-gray-900"
              href="https://vercel.com/new"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Image
                aria-hidden
                src="/globe.svg"
                alt="Globe icon"
                width={16}
                height={16}
              />
              Deploy →
            </a>
          </div>
        </div>
      </footer>
    </div>
  );
}
