{"root": true, "env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended", "next/core-web-vitals", "next/typescript"], "plugins": ["@typescript-eslint"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-explicit-any": "warn", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn", {"vars": "all", "args": "after-used", "ignoreRestSiblings": false}], "prefer-const": "off", "@typescript-eslint/ban-ts-comment": "warn", "no-empty": "off"}}