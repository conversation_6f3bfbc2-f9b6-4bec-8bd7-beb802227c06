# Security Implementation Plan

## Objectives

- Anonymous: verify reCAP<PERSON>HA on upload, return a short‑lived, document‑scoped token; enforce 60s audio and limited requests; revoke on login/claim.
- Signed‑in: issue a short‑lived user token; usable across owned documents; limited requests; revoke on logout.
- Enforce across doc/page/audio routes with a compatibility rollout path.

## Token Design

- Type: HMAC‑signed JWT (HS256) with `ACCESS_TOKEN_SECRET`.
- Claims:
  - `jti`, `typ` = `anon_doc` | `user`, `sub` = `docId` or `userId`, optional `sid` (anon session),
  - `exp` (short‑lived), `scope` (e.g., `doc:read page:read audio:read`), optional `ipHash`/`uaHash` binding.
- Server‑side limits (tracked in DB): `maxRequests`, `usedRequests`, `maxSeconds`, `usedSeconds`.
  - Anonymous defaults: `maxSeconds=60`, `ttl=15m`, `maxRequests=50` (configurable).
  - Signed‑in defaults: larger `maxRequests`, same or longer TTL; global usage enforced elsewhere (billing).

## Data Model (Prisma)

- New `AccessToken` model (table `access_tokens`):
  - `id` (cuid, used as `jti`), `type` (`anon_doc` | `user`),
  - `docId?`, `userId?`, `sessionId?`, `expiresAt`, `revokedAt?`,
  - `maxRequests`, `usedRequests`, `maxSeconds`, `usedSeconds`, `lastUsedAt?`,
  - optional `ipHash`, `userAgentHash`,
  - timestamps.
- Indexes: `@@index([docId])`, `@@index([userId])`, `@@index([sessionId])`, unique `id`.

## Server Utilities

- New `lib/access-token.ts`:
  - `issueAnonDocToken(docId, sessionId, opts?)` → create DB row + sign JWT.
  - `issueUserToken(userId, opts?)` → create DB row + sign JWT.
  - `verifyToken(token)` → verify signature, return claims + DB row.
  - `consumeUsage(jti, { requests=1, secondsServed? })` → atomic increment; error on expired/revoked/exceeded.
  - `revokeBy({ docId?, userId?, sessionId? })` → mark matching tokens revoked.
  - `hashIpUa(ip, ua)` → stable hash for optional binding/logging.

## API Changes (Backend)

- `POST /api/upload` (app/api/upload/route.ts):
  - Already checks reCAPTCHA. On success + doc created, call `issueAnonDocToken(doc.id, anonId)` and include `{ token }` with `{ docId }` in the response.
- `POST /api/auth/token` (new):
  - Requires Clerk auth and reCAPTCHA from client; returns a signed user token via `issueUserToken(dbUserId)`.
- `GET /api/audio/[id]`:
  - Require token for anonymous callers via `Authorization: Bearer <token>` or `?at=...` (for `<audio>` src). Token must match the audio’s document.
  - Compute seconds served based on range and known duration (fallback bitrate); call `consumeUsage` per response.
  - Keep existing byte‑clamp for anonymous as defense‑in‑depth; DB counters are the source of truth.
- `GET /api/page/[id]/ensure-tts` and `GET /api/page/[id]/chunks`:
  - Accept token; verify against doc. During rollout accept token OR reCAPTCHA. When returning audio URLs, append `?at=<token>` for browser playback.
- `GET /api/page/[id]/chunk/[index]`:
  - Require valid token (doc match); increment `requests` via `consumeUsage`.
- `GET /api/doc/[id]`:
  - Accept token for anonymous access (must match doc); fallback to Clerk session during rollout.
- Claim/Logout:
  - `POST /api/doc/[id]/claim` and `POST /api/doc/claim-by-session` → call `revokeBy({ docId })` / `revokeBy({ sessionId })` after successful claim.
  - Optional `POST /api/auth/logout` → revoke user tokens for `userId`.

## Frontend Changes

- Token storage:
  - On anonymous upload: persist `{ docId, token }` in memory (and optional short‑lived cookie).
  - After login: call `POST /api/auth/token` with reCAPTCHA; persist user token.
- New `lib/access/client.ts`:
  - `getAuthHeaders()` → include `Authorization: Bearer <token>` for fetch (ensure‑tts, chunks, doc fetch).
  - `appendTokenToUrl(url)` → append `at=<token>` for `<audio>` tags.
- Update hooks:
  - `chunkPolling`, `usePrefetchOnPlay`, `useTimeUpdatePrefetch` → attach auth headers.
  - Use `appendTokenToUrl` for audio `src` returned by ensure‑tts.
- On login: switch to user token; optional background revoke of anon tokens.
- On logout: clear tokens; optional revoke call.

## Usage Limits & Enforcement

- Anonymous: `maxSeconds=60`, short TTL, request cap; deny on exceed (403/429) with clear reason for UI prompts.
- Signed‑in: generous request cap, short TTL; rely on plan/billing gates for global usage.
- Errors: consistent JSON payloads for gating (e.g., `{ error, code: 'token_expired' | 'limit_exceeded' }`).

## Invalidation

- On login/claim: revoke tokens by `sessionId` or `docId`.
- On logout: revoke tokens by `userId`.
- Auto‑expire: clean lazily on verification; optional periodic cleanup job.

## Configuration

- `.env.example` additions:
  - `ACCESS_TOKEN_SECRET=...`
  - `ACCESS_TOKEN_TTL_SECONDS=1800`
  - `ACCESS_TOKEN_ANON_MAX_SECONDS=60`
  - `ACCESS_TOKEN_ANON_MAX_REQUESTS=50`
- Ensure existing reCAPTCHA envs are set; support `enterprise` or `classic` modes (already implemented).

## Observability

- Structured logs for token issue/verify/consume/revoke including `jti`, `type`, `docId/userId`, masked `ipHash`.
- Metrics: token issued, verified, revoked, denials by reason, anon vs signed‑in breakdown.

## Rollout Strategy

- Phase 1 (compat): accept session OR token; log missing/invalid tokens; start returning tokens on upload; keep reCAPTCHA on chunk listings.
- Phase 2 (enforce anon): require token for anonymous on audio/ensure‑tts/chunks; Clerk session remains valid for signed‑in.
- Phase 3 (strict): remove sid‑only fallbacks; require token or Clerk on all content reads.

## Acceptance Criteria

- Anonymous upload requires reCAPTCHA; response returns `{ docId, token }`.
- Anonymous playback cannot exceed 60s cumulative across pages; additional requests are rejected or clamped.
- Anon token is bound to a single document; reuse on other docs fails with 403.
- After login/claim, previous anon token no longer works; signed‑in token works across owned docs.
- All relevant routes enforce token or Clerk session; no unauthenticated access to doc/page/audio.

