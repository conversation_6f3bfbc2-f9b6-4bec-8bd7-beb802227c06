# TransReed: PDF-to-Translated-Audio Web App — Unified Implementation Plan (v2.0)

## 1) Product Overview

- **Goal**: Users upload a PDF, pick target language + voice, then play **page-by-page translated audio** with TOC, low latency, intelligent prefetch, and seamless user experience.
- **Smart Processing**: Multiple processing modes (traditional pipeline + AI-first) with automatic fallback and quality assurance.
- **Smart Start**: Auto-detect and skip front matter, jump to first meaningful content (Preface/Introduction/Chapter 1).
- **Sentence boundaries**: each page’s processed text ends at a complete sentence; if a sentence spills, carry the rest to next page.
- **Flexible Business Model**: Time-based primary model with optional credit-based fallback.
- **Core Features**: play/pause, ±15s seek, speed control, chapter navigation, TOC browse, page progress, per-page and full-document downloads.

---

## 2) User Flow

0. **Visit** → Anonymous can use preview version with limit of free 5 minutes audio
1. **Upload PDF** → Choose target language, voice gender (male/female)
2. **Backend** stores original, creates `Document`, enqueues processing pipeline, returns `docId`
3. **Preview** shows doc meta: book title, author page count, TOC/chapters, processing status, `suggestedStartPage`
4. **Play** → App skips default-skippable front matter; ensures audio for start page; streams when ready
5. **Prefetch** → At 55% progress OR ≤10s remaining, FE calls `ensure(nextPage)`
6. **Navigate** → Continue page-by-page; users can seek via TOC/chapters; usage time counted from meaningful content
7. **Prompt Login** -> When listening time exceeds 5 minutes, prompt user to either sign up or login (Auth with Clerk). When user signs up, an extra 5 minutes is added to their free limit.
8. **Prompt Upgrade** -> When listening time exceeds 10 minutes, prompt user to upgrade to a paid plan

---

## 3) Architecture & Stack

### Core Stack

- **Frontend**: Next.js 14+ (App Router), Tailwind CSS, HeroUI, TanStack Query v5
- **Backend**: Next.js API routes (TypeScript, Node 18+)
- **Worker**: Node.js worker with BullMQ + Redis (multi-queue ready)
- **Storage**: AWS S3 (private buckets) + optional CloudFront CDN
- **Database**: PostgreSQL 15+ via Prisma ORM
- **Queue**: Redis 7+ + BullMQ
- **Auth**: Clerk (supports anonymous sessions)
- **AI**: Google Gemini 2.5 Pro + Gemini 2.5 Pro TTS, with OpenAI fallback
- **PDF**: qpdf/pdfcpu for splitting, pdfjs-dist for extraction
- **Audio**: FFmpeg for processing, normalization, and concatenation

### Infrastructure

- **Hosting**: Vercel (Next.js) + dedicated worker instances (Render/Railway/EC2)
- **CDN**: CloudFront for audio delivery with Range request support
- **Monitoring**: Structured logging + metrics (Datadog/New Relic/self-hosted)
- **Secrets**: Environment variables with proper rotation

---

## 4) Business Models & Usage Plans

### Primary: Time-Based Model (v2.0)

- **Anonymous**: 5:00 preview (no signup required)
- **Free Account**: +5:00 additional (10:00 total)
- **Starter Plan**: $5/month → 1 hour listening time
- **Pro Plan**: $19/month → 5 hours listening time
- **Premium Plan**: $49/month → 15 hours listening time
- **Usage Tracking**: Counted from `suggestedStartPage`, paused during processing delays
- **Overage**: Soft limit with upgrade prompts in user's chosen language/voice

### Billing Integration

- **Stripe Billing**: Subscriptions, invoices, usage-based billing
- **PayPal**: Alternative payment method with webhook integration
- **Tax**: Automatic tax calculation via Stripe Tax

---

## 5) Data Model (Prisma Schema)

### Users & Authentication

```prisma
model User {
  id                   String    @id @default(cuid())
  clerkUserId          String    @unique
  email                String?
  name                 String?
  planKey              String    @default("free")
  plan                 Plan      @relation(fields: [planKey], references: [key])
  subscriptionStatus   String    @default("inactive") // active, past_due, canceled
  stripeCustomerId     String?
  paypalPayerId        String?
  creditBalance        Int       @default(0)
  billingPeriodStart   DateTime?
  billingPeriodEnd     DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  documents            Document[]
  usage                 Usage[]
  creditLedger          CreditLedger[]
  paymentEvents         PaymentEvent[]

  @@map("users")
}

model Plan {
  key                   String    @id
  name                  String
  priceMonthlyCents     Int
  maxDocumentsPerCycle  Int?
  includedPageCredits   Int?
  includedListeningTime Int?      // (seconds) for time-based model
  description           String
  isActive              Boolean   @default(true)
  users                 User[]

  @@map("plans")
}
```

### Usage Tracking

```prisma
model Usage {
  id                   String    @id @default(cuid())
  userId               String
  cycleStart           DateTime
  cycleEnd             DateTime
  documentsProcessed   Int       @default(0)
  pagesProcessed       Int       @default(0)
  listeningTime        Int       @default(0) // (seconds) time-based tracking
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, cycleStart])
  @@map("usage")
}

model CreditLedger {
  id                   String    @id @default(cuid())
  userId               String
  change               Int       // positive = credit, negative = debit
  reason               String    // "purchase", "usage", "refund", "bonus"
  description          String?
  meta                 Json?     // additional context
  createdAt            DateTime  @default(now())

  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, createdAt])
  @@map("credit_ledger")
}

model PaymentEvent {
  id                   String    @id @default(cuid())
  userId               String
  provider             String    // "stripe" | "paypal"
  type                 String    // "subscription_created", "payment_succeeded", etc.
  amount               Int?      // cents
  currency             String?
  status               String
  externalId           String    // Stripe/PayPal transaction ID
  raw                  Json      // full webhook payload
  createdAt            DateTime  @default(now())

  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([provider, externalId])
  @@map("payment_events")
}
```

### Documents & Content

```prisma
model Document {
  id                    String     @id @default(cuid())
  userId                String?    // null for anonymous
  sessionId             String?    // for anonymous users
  originalFilename      String
  authors               String?    // comma-separated
  contentType           String
  sizeBytes             Int
  s3KeyOriginal         String
  pageCount             Int?
  hasFullAudio          Boolean    @default(false)
  targetLanguage        String?    // target language
  voice                 String?    // male/female
  status                String     @default("uploaded") // uploaded|splitting|processing|ready|failed
  suggestedStartPageId  String?
  tocSource             String?    // pdf_outline|ai_inferred|hybrid|none
  createdAt             DateTime   @default(now())
  updatedAt             DateTime   @updatedAt
  expiresAt             DateTime?  // for anonymous uploads

  user                  User?      @relation(fields: [userId], references: [id], onDelete: Cascade)
  suggestedStartPage    Page?      @relation("DocumentSuggestedStartPage", fields: [suggestedStartPageId], references: [id])
  pages                 Page[]     @relation("DocumentPages")
  chapters              Chapter[]
  headingDetections     PageHeadingDetection[]

  @@index([userId, createdAt])
  @@index([sessionId]) // for anonymous
  @@index([status])
  @@index([expiresAt]) // cleanup job
  @@index([suggestedStartPageId])
  @@map("documents")
}

model Page {
  id                       String     @id @default(cuid())
  documentId               String
  pageNumber               Int
  s3KeySinglePagePdf       String
  charCount                Int?
  charCountTranslated      Int?
  endsWithCompleteSentence Boolean    @default(true)
  isChapterStart           Boolean    @default(false)
  isSkippableByDefault     Boolean    @default(false)
  textSource               String?
  estimatedDuration        Int?
  status                   String     @default("pending") // pending|processing|ready|failed
  role                     String     @default("unknown") // front_matter|toc|content|appendix|index|unknown
  createdAt                DateTime   @default(now())
  updatedAt                DateTime   @updatedAt

  document                 Document   @relation("DocumentPages", fields: [documentId], references: [id], onDelete: Cascade)
  pageAudios               PageAudio[]
  headingDetections        PageHeadingDetection[]
  suggestedStartForDocuments Document[] @relation("DocumentSuggestedStartPage")
  chaptersStartingHere     Chapter[]  @relation("ChapterStartPage")
  chaptersEndingHere       Chapter[]  @relation("ChapterEndPage")

  @@unique([documentId, pageNumber])
  @@index([documentId, role])
  @@index([status])
  @@map("pages")
}

model Chapter {
  id                     String     @id @default(cuid())
  documentId             String
  name                   String
  startPageId            String
  endPageId              String?
  parentId               String?
  order                  Int
  level                  Int        @default(1)
  source                 String
  confidence             Float      @default(1.0)
  createdAt              DateTime   @default(now())

  document               Document   @relation(fields: [documentId], references: [id], onDelete: Cascade)
  parent                 Chapter?   @relation("ChapterHierarchy", fields: [parentId], references: [id])
  children               Chapter[]  @relation("ChapterHierarchy")
  startPage              Page       @relation("ChapterStartPage", fields: [startPageId], references: [id])
  endPage                Page?      @relation("ChapterEndPage", fields: [endPageId], references: [id])

  @@index([documentId, order])
  @@index([parentId])
  @@index([startPageId])
  @@index([endPageId])
  @@map("chapters")
}

model PageHeadingDetection {
  id                     String     @id @default(cuid())
  documentId             String
  pageId                 String
  language               String
  detectedHeading        String?
  headingConfidence      Float      @default(0.0)
  processingMode         String     // traditional|ai_first
  modelVersion           String?
  createdAt              DateTime   @default(now())

  document               Document   @relation(fields: [documentId], references: [id], onDelete: Cascade)
  page                   Page       @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@index([pageId, language])
  @@index([documentId])
  @@map("page_heading_detections")
}
```

### Audio & Translation

```prisma
model PageAudio {
  id                   String    @id @default(cuid())
  pageId               String
  language             String    // ISO 639-1 (en, vi, etc.)
  voice                String    // male|female
  ttsEngine            String    // gemini|openai|elevenlabs
  ttsParamsHash        String    // hash of TTS parameters
  translatedTextHash   String?
  modelVersion         String    // AI model version for caching
  s3KeyTranslatedText  String?
  s3KeyAudioMp3        String?
  duration             Int?      // seconds
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  page                 Page      @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@unique([pageId, language, voice, ttsEngine, ttsParamsHash, modelVersion])
  @@index([pageId, language, voice])
  @@map("page_audios")
}
```

---

## 6) S3 Storage Structure

### File Organization

```
users/{userId|anonymous-{sessionId}}/documents/{documentId}/
├── original.pdf
├── toc.json
├── meta.json
├── pages/
│   └── {pageId}/
│       ├── raw.pdf                    # Primary AI input
│       ├── image.png                  # Fallback/thumbnail (optional)
│       ├── text-raw.txt              # Legacy pipeline
│       ├── text-processed.txt        # Legacy pipeline
│       ├── meta.json                 # Page metadata
│       ├── translations/
│       │   └── {language}/
│       │       ├── text.txt          # Translated content
│       │       └── meta.json         # Translation metadata
│       └── audio/
│           └── {language}/
│               └── {voice}/
│                   ├── {audioKey}.mp3
│                   └── meta.json
├── audio/
│   ├── full/
│   │   └── {language}/
│   │       └── {voice}/
│   │           ├── complete.mp3      # Full document audio
│   │           └── meta.json
└── cache/
    └── thumbnails/                   # Generated thumbnails
        ├── cover.jpg
        └── preview.gif               # Animated preview
```

### S3 Bucket Policies

- **Private buckets** with signed URL access
- **CORS** configured for browser uploads
- **Lifecycle policies** for cleanup (anonymous: 24h, free: 30 days, paid: configurable)
- **Versioning** disabled (cost optimization)
- **Storage classes**: Standard → IA → Glacier for long-term retention

---

## 7) PDF Processing Pipeline

### A) Upload & Initialization

- POST upload (multipart) stores original to S3; create `Document(status='splitting')`; enqueue `split-document`.
- If file metadata contains table of contents info, save to `Chapter` and set `Document.tocSource = 'pdf_outline'`.

### B) PDF Splitting & Metadata Extraction

- Split document into pdf files of single page, upload to S3

### C) AI Text Extract+Translate (+ Chapter Detection) — **Primary (PDF‑First)**

- **Inputs**: `pages/{pageId}/raw.pdf` (preferred), optionally the tail from page N−1 for first sentence joining.
- **Parameters**: `temperature=0`, pinned `modelVersion`, versioned prompt `translationPromptVersion`.
- **Prompt**: translate page and detect chapter start/title based on keywords + typographic cues. Output **strict JSON** as in §8.
- **Persistence**: save `translated_text` and page meta flags; compute `translatedTextHash`.
- Detect if the page is skippable by default (front matter, TOC, etc.) and set `isSkippableByDefault` flag.
- Detect if the page is chapter start and set `isChapterStart` flag. Update to `Chapter` if `Document.tocSource = 'ai_inferred'`.

### D) AI Text-to-Speech

- Check if `pageAudio` already exists for this `pageId` + `language` + `voice` + `ttsEngine` + `ttsParamsHash` + `modelVersion`. If so, return existing audio url.
- Retrieve `translatedSentences` from C; synthesize to audio; upload to S3; save to `pageAudio`
- If page starts with an incomplete sentence, prepend with carryover from previous page.
- If page ends with an incomplete sentence, ignore it and carry over to next page. Page boundaries are **always** at complete sentences.
- Return the audio url.

### E) Full Audio Download

- When user downloads full audio, check if the Document hasFullAudio and complete audio file already exists in S3. If so, return existing audio url.
- Otherwise, concatenate all `pageAudio` for this document; upload to S3; set `Document.hasFullAudio = true`

---

## 8) On-Demand Text Extract + Translate + Chapter Detection

Prompt AI to return result in specific JSON format:

```
You are a professional translator. Analyze this PDF page and provide a JSON response with the following structure:

(Prepend this carryover text to the beginning of the page before translation: "${carryoverText}")

{
  "sentences": ["...", "..."],
  "translatedSentences": ["...", "..."],
  "endsWithCompleteSentence": true,
  "isChapterStart": false,
  "isSkippable": false,
  "detectedHeading": null,
  "headingConfidence": 0.0
}

Instruction:
- Preserve formatting and structure
- "sentences": Split the original text into an array of sentences. Preserve the original sentence structure and punctuation.
- "translatedSentences": Translate all text content to ${targetLanguage}. Ensure the output is clean, no annotations or metadata
- "endsWithCompleteSentence": true if last sentence ends with a period, question mark, or exclamation point; otherwise false
- "isChapterStart": true if this page appears to begin a new section/chapter; try keywords like "Chapter", "Section", roman numerals, numbered headings, and typographic cues like large/bold title, centered heading, whitespace above/below. Do not rely solely on font size or weight. Consider section headers, large titles
- "isSkippable": true if this page is front matter, table of contents, or other non-content (e.g. "Contents", "Preface", "Acknowledgments", "Introduction", "About the Author", "Copyright", "Index", etc.)
- "detectedHeading": Extract the chapter/section title if found
- "headingConfidence": Rate 0.0-1.0 based on typography and content clues
- Handle page ${pageNumber} context appropriately
- Return ONLY valid JSON, no other text.
```

---

## 9) On-Demand Audio Generation

- Use Gemini `gemini-2.5-pro-preview-tts` to synthesize audio from text with a style prompt to tune delivery (gender: male/female, natural pace, brief pauses, no extra commentary).
- The text may be prepended with carryover from previous page if it ends with an incomplete sentence
- The text does not include the last incomplete sentence if the page ends with an incomplete sentence
