# Queue-Based Asynchronous Processing Implementation

## Overview

The upload and document processing flow has been refactored to use a BullMQ queue system with Redis. This provides better scalability, reliability, and user experience through asynchronous background processing.

## Architecture

### Flow Diagram

```
Frontend Upload → API Upload → Enqueue Job → Return Immediately
                                    ↓
                            [Redis Queue]
                                    ↓
                            Worker Process
                                    ↓
                    ┌───────────────┼───────────────┐
                    ↓               ↓               ↓
            Process Document  Translate Page  Generate Audio
                    ↓               ↓               ↓
            Update Status    Update Status   Update Status
```

### Document Status Flow

```
uploaded
  ↓ (process-document job)
  ↓ PDF splitting
  ↓
translating_first_page
  ↓ (translate-first-page job)
  ↓ Gemini translation
  ↓
translated_first_page
  ↓ (generate-audio-first-page job)
  ↓ TTS synthesis
  ↓
generating_audio_first_page
  ↓ First chunk ready
  ↓
generated_audio_first_page (READY TO PLAY!)
```

## Components

### 1. Queue Infrastructure

**File**: `lib/queue/config.ts`
- Redis connection setup
- Queue configuration with retry logic
- Job options (attempts, backoff, removal policies)

**File**: `lib/queue/jobs.ts`
- TypeScript interfaces for job payloads
- Document status constants
- Job type definitions

### 2. Job Handlers

**File**: `lib/jobs/process-document.ts`
- Loads original PDF from storage
- Splits into single-page PDFs using pdf-lib
- Saves each page to storage
- Updates Page records with s3KeySinglePagePdf
- Enqueues `translate-first-page` job

**File**: `lib/jobs/translate-first-page.ts`
- Updates status to `translating_first_page`
- Processes pages in batches (5 pages at a time)
- Finds first non-skippable page using Promise.any
- Translates page using Gemini
- Saves translation and metadata
- Updates status to `translated_first_page`
- Sets suggestedStartPageId
- Enqueues `generate-audio-first-page` job

**File**: `lib/jobs/generate-audio-first-page.ts`
- Updates status to `generating_audio_first_page`
- Prepares TTS text from translation
- Applies chunking strategy (Vbee or Gemini)
- Synthesizes first chunk
- Saves first chunk to storage
- Updates status to `generated_audio_first_page`
- Continues remaining chunks in background

### 3. Worker Process

**File**: `workers/document-processor.ts`
- Consumes jobs from the `document-processing` queue
- Routes jobs to appropriate handlers
- Handles job completion, failures, and errors
- Graceful shutdown on SIGTERM/SIGINT
- Configurable concurrency (default: 5 jobs)

### 4. API Endpoints

**Modified**: `app/api/upload/route.ts`
- Enqueues `process-document` job instead of synchronous processing
- Returns immediately with docId
- Removed `primeFirstMeaningfulStart` call

**Modified**: `app/api/page/[id]/ensure-tts/route.ts`
- Handles on-demand audio generation for pages 2+
- Does NOT update document.status
- Keeps chunked audio approach

**Enhanced**: `app/api/doc/[id]/route.ts`
- Added `firstPageAudioReady` flag
- Checks if chunk-1.mp3 exists for first page
- Used by frontend for status polling

### 5. Frontend Integration

**Modified**: `app/doc/[id]/hooks/useInteractiveCore.tsx`
- Polls `/api/doc/[id]` to check document.status
- Handles new status values (uploaded, translating_first_page, etc.)
- Updates processingStage based on status
- Shows processing until `generated_audio_first_page` AND `firstPageAudioReady`

## Setup Instructions

### 1. Install Dependencies

Already installed via npm:
```bash
npm install bullmq ioredis
```

### 2. Configure Redis

Add to `.env`:
```bash
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=  # optional
```

### 3. Start Redis Server

Using Docker:
```bash
docker run -d -p 6379:6379 redis:7-alpine
```

Or install locally:
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis
```

### 4. Start Worker Process

In development:
```bash
npm run worker:dev
```

In production:
```bash
npm run worker
```

### 5. Start Next.js App

```bash
npm run dev
```

## Usage

### Upload Flow

1. User uploads PDF via frontend
2. Backend receives file, creates Document with status='uploaded'
3. Enqueues `process-document` job
4. Returns `{ docId }` immediately
5. Frontend shows loading screen with status polling

### Status Polling

Frontend polls `/api/doc/[id]` every 1.5 seconds and displays:
- `uploaded` → "Processing document..."
- `translating_first_page` → "Translating first page..."
- `translated_first_page` → "Translation complete..."
- `generating_audio_first_page` → "Generating audio..."
- `generated_audio_first_page` + `firstPageAudioReady` → "Ready!" (start playing)

### On-Demand Pages

Pages beyond the first are processed on-demand when user navigates:
1. Frontend calls `/api/page/[id]/ensure-tts`
2. Backend checks cache, generates if needed
3. Returns chunked audio URLs
4. Does NOT update document.status

## Monitoring

### Check Queue Status

You can monitor the queue using Bull Board or Redis CLI:

```bash
# Redis CLI
redis-cli
> KEYS bull:document-processing:*
> HGETALL bull:document-processing:jobs
```

### Worker Logs

The worker outputs detailed logs:
```
[worker] Processing job: process-document (job-id)
[process-document] Starting job { docId: '...' }
[process-document] PDF loaded { docId: '...', pageCount: 250 }
```

### Job Failures

Failed jobs are kept for 7 days with full error details:
- Check worker logs for immediate errors
- Failed jobs can be retried (3 attempts with exponential backoff)
- Completed jobs are removed after 24 hours

## Production Deployment

### Environment Variables

Ensure all required env vars are set in production:
```bash
DATABASE_URL=postgresql://...
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
GEMINI_API_KEY=your-api-key
```

### Worker Deployment

Deploy worker as a separate service:
- **Vercel**: Cannot run long-lived workers (deploy elsewhere)
- **Render**: Use "Background Worker" service type
- **Railway**: Deploy as separate service
- **AWS EC2/ECS**: Run as systemd service or container

### Scaling

- **Horizontal**: Run multiple worker instances
- **Vertical**: Increase worker concurrency (5-20 recommended)
- **Redis**: Use Redis Cluster for high availability

## Troubleshooting

### Worker Not Processing Jobs

1. Check Redis connection:
   ```bash
   redis-cli ping
   ```

2. Verify worker is running:
   ```bash
   ps aux | grep document-processor
   ```

3. Check worker logs for errors

### Jobs Stuck in Queue

1. Check job status in Redis:
   ```bash
   redis-cli HGETALL bull:document-processing:job-id
   ```

2. Manually retry failed job via Bull Board

3. Clear stuck jobs:
   ```bash
   redis-cli DEL bull:document-processing:failed
   ```

### Frontend Stuck on "Processing..."

1. Check document.status in database:
   ```sql
   SELECT id, status FROM documents WHERE id = 'doc-id';
   ```

2. Check if worker crashed during processing

3. Manually update status if needed:
   ```sql
   UPDATE documents SET status = 'uploaded' WHERE id = 'doc-id';
   ```

4. Retry by re-enqueuing job

## Migration Notes

### From Old System

The old `primeFirstMeaningfulStart` approach has been replaced:
- **Old**: Synchronous priming in upload API (fire-and-forget)
- **New**: Queue-based background jobs with status tracking

### Backward Compatibility

- On-demand page processing (ensure-tts) remains unchanged
- Existing documents work as before
- No database migration required (status is flexible string field)

## Files Created/Modified

### Created
- `lib/queue/config.ts` - Queue configuration
- `lib/queue/jobs.ts` - Job type definitions
- `lib/jobs/process-document.ts` - PDF splitting job
- `lib/jobs/translate-first-page.ts` - Translation job
- `lib/jobs/generate-audio-first-page.ts` - TTS job
- `workers/document-processor.ts` - Worker process

### Modified
- `app/api/upload/route.ts` - Enqueue jobs instead of sync processing
- `app/api/page/[id]/ensure-tts/route.ts` - Reimplemented for on-demand
- `app/api/doc/[id]/route.ts` - Added firstPageAudioReady flag
- `app/doc/[id]/hooks/useInteractiveCore.tsx` - Handle new statuses
- `.env.example` - Added Redis configuration
- `package.json` - Added worker scripts

## Next Steps

1. Set up monitoring dashboard (Bull Board)
2. Configure alerts for failed jobs
3. Set up Redis persistence/backup
4. Implement job metrics and analytics
5. Add job priority for premium users
6. Consider adding more queue types (e.g., batch translation)
