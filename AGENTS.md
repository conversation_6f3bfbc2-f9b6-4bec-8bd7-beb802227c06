# Repository Guidelines

## Project Structure & Modules

- `app/`: Next.js 14 App Router pages and routes.
- `app/api/`: API route handlers (upload, audio, doc, health).
- `lib/`: shared utilities (DB, S3, file service, AI helpers).
- `prisma/`: Prisma schema and migrations.
- `scripts/`: setup and data utilities (e.g., `setup-db.js`, `import-docs.js`).
- `public/`: static assets. `data/`: local document store for dev.
- `types/`: shared TypeScript types.

## Build, Run, and Development

- `npm run dev`: start the dev server at `http://localhost:3000`.
- `npm run build`: generate Prisma client, then build Next.js.
- `npm start`: run the production build.
- `npm run setup:db`: create/prepare Postgres (Docker optional) and run Prisma.
- `npm run db:migrate` | `db:push`: apply schema changes.
- `npm run data:import-docs` / `data:index-artifacts`: index local PDFs under `data/documents/<docId>/`.
- Docker: `./docker-build.sh` then `docker-compose up -d` (see `docker-compose*.yml`).

## Coding Style & Conventions

- Language: TypeScript + React Server/Client Components.
- Indentation: 2 spaces; keep lines focused and small components.
- Strings: single quotes; include semicolons; prefer `async/await`.
- Naming: `camelCase` for vars/functions, `PascalCase` for components/types, kebab-case for files in `app/` routes.
- Imports: absolute from app root when possible; group by lib → local.
- Linting: ESLint via `npm run lint` (`eslint-config-next`, TS rules). Use `npm run lint:fix` before PRs.

## Testing Guidelines

- No formal test suite yet. For new code, add lightweight integration tests where feasible (e.g., Playwright or Next API handler tests) and place as `*.test.ts` beside the unit under test.
- Aim for smoke coverage of critical routes: upload, doc view, audio retrieval.

## Commit & Pull Requests

- Commits: imperative, concise subject (e.g., "Add docker", "Implement clerk"). Squash noisy WIP.
- PRs must include: clear summary, linked issue (if any), steps to verify, and screenshots for UI changes.
- Keep PRs focused; note any follow-ups explicitly.

## Security & Configuration

- Env files: copy `.env.example` to `.env` or `.env.production`. Do not commit secrets.
- Required envs vary by feature: `DATABASE_URL`, Clerk keys, AWS S3 (`AWS_REGION`, `S3_BUCKET_NAME`, `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`).
- Local dev can run without S3; production prefers S3 uploads (see `README.md`).
