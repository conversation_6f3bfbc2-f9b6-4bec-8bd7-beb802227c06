# Quick Guide: Switching to Streaming TTS

This guide shows you how to switch between chunked and streaming TTS modes.

## Switching Modes

Edit `lib/config.ts`:

```typescript
export const appConfig: AppConfig = {
  // ... other config

  // OPTION 1: Use streaming mode (real-time, no gaps)
  ttsDeliveryMode: 'streaming',

  // OPTION 2: Use chunked mode (traditional, has gaps)
  // ttsDeliveryMode: 'chunked',
};
```

That's it! The system automatically adapts based on this setting.

## How It Works

### When `ttsDeliveryMode: 'streaming'`

1. Client calls `/api/page/{id}/ensure-tts`
2. Server returns `{ streaming: true, streamUrl: "..." }`
3. Client fetches from `streamUrl` and streams audio in real-time
4. Audio starts playing within 1-2 seconds
5. No gaps between chunks

**Backend logs you'll see:**
```
[ensure-tts] Streaming mode enabled, using stream-tts endpoint
[tts-stream] connecting... { model: '...', voice: '<PERSON><PERSON><PERSON><PERSON>' }
[tts-stream] opened
[tts-stream] sending text { chars: 1234 }
[tts-stream] yielding chunk { chunk: 1, bytes: 4096 }
[tts-stream] yielding chunk { chunk: 2, bytes: 4096 }
...
[tts-stream] turn complete
[tts-stream] complete { totalChunks: 15 }
```

### When `ttsDeliveryMode: 'chunked'` (or undefined)

1. Client calls `/api/page/{id}/ensure-tts`
2. Server synthesizes first chunk, returns `{ chunked: true, urls: [...] }`
3. Client plays first chunk
4. Server synthesizes remaining chunks in background
5. Client polls for next chunks

**Backend logs you'll see:**
```
<><><> TTS GenAI SDK request= { "model": "...", ... }
[tts] GenAI SDK success { audioBytes: 12345, format: 'mp3' }
[tts] chunk ready { idx: 1, of: 5 }
...
```

## Using the Universal Helper

The `ensurePageAudio()` function automatically detects and handles both modes:

```typescript
import { ensurePageAudio } from '@/app/doc/[id]/hooks/utils/ensureAudio';

// Works with both modes!
const result = await ensurePageAudio(pageId, anonSid);

console.log('Audio URL:', result.url);
console.log('Is streaming?', result.isStreaming);

// Clean up if needed
if (result.cleanup) {
  result.cleanup();
}
```

Or use the React hook:

```typescript
import { useEnsureAudio } from '@/app/doc/[id]/hooks/utils/ensureAudio';

function AudioPlayer({ pageId, anonSid }) {
  const { audioUrl, isLoading, error, isStreaming, loadAudio } = useEnsureAudio(pageId, anonSid);

  return (
    <div>
      <button onClick={loadAudio} disabled={isLoading}>
        {isLoading ? 'Loading...' : 'Play'}
      </button>

      {audioUrl && (
        <div>
          <audio src={audioUrl} controls autoPlay />
          <p>{isStreaming ? 'Streamed audio' : 'Chunked audio'}</p>
        </div>
      )}

      {error && <p>Error: {error.message}</p>}
    </div>
  );
}
```

## Comparing the Modes

| Feature | Streaming | Chunked |
|---------|-----------|---------|
| Time to first audio | 1-2 seconds | 5-10 seconds |
| Gaps between chunks | None | 2-5 seconds |
| Backend complexity | Lower | Higher |
| Browser support | Modern browsers | All browsers |
| Caching | Background | Inline |
| Resume support | Limited | Full |

## Troubleshooting

### Issue: Still seeing chunked logs

**Cause**: Config change not picked up
**Solution**:
1. Check `lib/config.ts` - ensure `ttsDeliveryMode: 'streaming'`
2. Restart dev server: `npm run dev`
3. Clear browser cache

### Issue: Stream not starting

**Cause**: Gemini API key missing
**Solution**:
1. Check `.env` has `GEMINI_API_KEY=...`
2. Verify key is valid
3. Check logs for `[tts-stream]` errors

### Issue: Choppy playback

**Cause**: Network too slow
**Solution**:
1. Check network connection
2. Try reducing playback speed
3. Fall back to chunked mode temporarily

## Testing

Test both modes work:

```bash
# Terminal 1: Start dev server
npm run dev

# Terminal 2: Test streaming mode
curl -X POST http://localhost:3000/api/page/{pageId}/ensure-tts \
  -H "Content-Type: application/json"

# Expected response with streaming:
# { "ok": true, "streaming": true, "streamUrl": "/api/page/.../stream-tts" }

# Expected response with chunked:
# { "ok": true, "chunked": true, "urls": [...] }
```

## Rollback

If you need to rollback to chunked mode:

```typescript
// lib/config.ts
export const appConfig: AppConfig = {
  // ... other config
  ttsDeliveryMode: 'chunked', // or comment out this line
};
```

No other changes needed - the system automatically falls back.

## Performance Monitoring

Add logging to track which mode is being used:

```typescript
// In your audio player component
useEffect(() => {
  if (audioUrl) {
    console.log('[audio-player]', {
      mode: isStreaming ? 'streaming' : 'chunked',
      pageId,
      timestamp: Date.now(),
    });
  }
}, [audioUrl, isStreaming, pageId]);
```

Monitor these metrics:
- Time to first audio
- Total playback time
- Gap duration (should be 0 for streaming)
- Error rate
- User engagement (completion rate)
