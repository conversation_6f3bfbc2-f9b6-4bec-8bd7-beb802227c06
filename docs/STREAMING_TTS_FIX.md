# Streaming TTS Fix - Summary

## Problem

The streaming TTS implementation was timing out without receiving any audio chunks, causing the system to always fall back to batch synthesis:

```
[tts-stream] Starting stream { model: 'gemini-2.5-pro-preview-tts', voice: '<PERSON><PERSON><PERSON><PERSON>', textLength: 3141 }
[stream-tts] Streaming failed, falling back to batch synthesis: { error: 'Stream timeout' }
```

## Root Cause Analysis

After extensive investigation and research, we discovered the **fundamental issue**:

### **`generateContentStream` is NOT supported for TTS models**

According to [Google's official documentation](https://ai.google.dev/gemini-api/docs/speech-generation), the TTS models (`gemini-2.5-pro-preview-tts`) **only support non-streaming `generateContent()`**. The documentation shows:

- ✅ `generateContent()` - **Supported** for TTS (returns complete audio)
- ❌ `generateContentStream()` - **NOT documented/supported** for TTS models
- ✅ Live API (WebSocket) - **Supported** for real-time bidirectional streaming

**What was happening:**
1. The code called `genai.models.generateContentStream()` with a TTS model
2. The SDK call **hung indefinitely** (never returned, never errored)
3. After 30 seconds, our timeout kicked in and fell back to working non-streaming method
4. The fallback used `synthesizeTtsGemini()` which calls `generateContent()` (non-streaming) ✅

### Secondary Issues Found

1. **No timeout on stream creation**: If the SDK call hangs, we waited forever
2. **Poor error handling**: Errors weren't being logged with sufficient detail
3. **Missing diagnostics**: Hard to determine where the failure occurred

## Fixes Implemented

### 1. Enhanced Error Handling in `streamTtsViaGenAiSdk` (`lib/ai/providers/gemini.ts`)

**Changes:**
- Added comprehensive error messages with stack traces
- Added validation for required configuration (API key, voice name)
- Added detailed logging at each step of the streaming process
- Added diagnostic logging to show stream creation details
- Added check to ensure at least one chunk is received before completing

**Key improvements:**
```typescript
// Before: Silent failures
if (!apiKey) {
  console.error('[tts-stream] No GEMINI_API_KEY found');
  return;
}

// After: Explicit errors
if (!apiKey) {
  const err = new Error('No GEMINI_API_KEY found');
  console.error('[tts-stream]', err.message);
  throw err;
}
```

### 2. Improved Timeout Strategy in `stream-tts` Route (`app/api/page/[id]/stream-tts/route.ts`)

**Changes:**
- **Two-tier timeout approach**:
  - First chunk timeout: 30 seconds (fail fast if stream not working)
  - Overall stream timeout: 5 minutes (enough time for long texts)
- **Progress tracking**: Logs every 5th chunk to reduce noise while still showing progress
- **Better error context**: Includes chunk count, bytes received, and timestamps in error logs

**Key improvements:**
```typescript
// First chunk timeout - detect if stream is working
const firstChunkTimeout = 30000;  // 30 seconds

// Overall timeout - for long text processing
const overallStreamTimeout = 300000;  // 5 minutes

// Wait for first chunk with short timeout
await Promise.race([firstChunkPromise, streamPromise]);

// If first chunk received, wait for completion with longer timeout
if (firstChunkReceived) {
  await Promise.race([streamPromise, overallTimeoutPromise]);
}
```

### 3. Diagnostic Test Script (`scripts/test-streaming-tts.ts`)

Created a standalone test script to help diagnose streaming TTS issues:

```bash
npx tsx scripts/test-streaming-tts.ts
```

The script tests:
- Non-streaming TTS (baseline to verify API works)
- Streaming TTS with detailed progress logging
- Audio format detection
- Timeout handling

## Verification Steps

To verify the fixes work:

### 1. Run the test script

```bash
npx tsx scripts/test-streaming-tts.ts
```

Expected output:
```
=== Testing Streaming TTS ===

--- Test 1: Non-streaming TTS (ttsViaGenAiSdk) ---
✓ Non-streaming TTS succeeded: { audioBytes: 52341, durationSec: 12, ... }

--- Test 2: Streaming TTS (streamTtsViaGenAiSdk) ---
  ✓ First chunk received after 1234ms
  Chunk 1: { bytes: 8192, totalBytes: 8192 }
  Chunk 2: { bytes: 8192, totalBytes: 16384 }
  ...
✓ Streaming TTS succeeded: { totalChunks: 7, totalBytes: 52341, ... }
```

### 2. Check application logs

When streaming TTS is triggered, you should now see:

```
[tts-stream] Starting stream { model: 'gemini-2.5-pro-preview-tts', voice: 'Zephyr', ... }
[tts-stream] Calling generateContentStream with config: { ... }
[tts-stream] Stream created successfully { genType: 'object', hasStream: true, ... }
[tts-stream] Starting to iterate stream { hasStream: true, ... }
[tts-stream] First chunk received { chunkType: 'object', hasCandidate: true, ... }
[tts-stream] First chunk arrived { timeMs: 1234, bytes: 8192 }
[tts-stream] Progress { chunks: 5, totalBytes: 40960 }
[tts-stream] Stream iteration complete { totalChunks: 7, totalBytes: 52341, durationMs: 2456 }
[tts-stream] Audio is already MP3 format
[stream-tts] Audio ready { page: 1, durationSec: 12, mp3Bytes: 52341 }
```

### 3. If streaming still fails

The logs will now provide detailed information:

```
[tts-stream] generateContentStream failed: {
  error: 'API key invalid',
  stack: '...',
  model: 'gemini-2.5-pro-preview-tts',
  ...
}
```

Or:

```
[tts-stream] Error reading stream: {
  error: 'Stream error after 0 chunks: ...',
  chunksReceived: 0,
  firstChunkReceived: false,
  ...
}
```

## Fallback Behavior

If streaming fails, the system will automatically fall back to batch synthesis using `synthesizeTtsGemini`, which tries multiple providers in order:

1. Google GenAI Live (WebSocket-based)
2. **Google GenAI SDK (non-streaming)** ← Most likely fallback
3. Generative Language API (REST)
4. GCP TTS with OAuth
5. GCP TTS Classic
6. OpenAI TTS (if `OPENAI_API_KEY` is configured)

The fallback is comprehensive and should always succeed if at least one TTS provider is configured correctly.

## Common Issues and Solutions

### Issue 1: "No GEMINI_API_KEY found"

**Solution**: Ensure `GEMINI_API_KEY` is set in your environment:
```bash
export GEMINI_API_KEY="your-api-key-here"
```

### Issue 2: "No voice name configured for {voice}"

**Solution**: Verify voice configuration in `lib/config.ts`:
```typescript
geminiTtsVoiceNameFemale: 'Zephyr',
geminiTtsVoiceNameMale: 'Puck',
```

### Issue 3: "First chunk timeout - no audio received within 30 seconds"

**Possible causes:**
- API key doesn't have access to the TTS model
- Model name is incorrect
- Network connectivity issues
- Google API service is down

**Solution**: Run the test script to isolate the issue, check API permissions, and verify model name

### Issue 4: Stream completes but no chunks received

**Solution**: This indicates the model returned a response but without audio data. Check:
- Model supports audio generation (`gemini-2.5-pro-preview-tts`)
- Configuration includes `responseModalities: ['audio']`
- API key has proper permissions

## Configuration

**UPDATED** TTS configuration in `lib/config.ts`:

```typescript
export const appConfig: AppConfig = {
  geminiTtsMethod: 'genai_sdk',  // Uses non-streaming generateContent (WORKS ✅)
  geminiTtsVoiceNameFemale: 'Zephyr',
  geminiTtsVoiceNameMale: 'Puck',
  ttsDeliveryMode: 'chunked',  // CHANGED from 'streaming' to 'chunked' (RECOMMENDED ✅)
  gcpTtsModel: 'gemini-2.5-pro-preview-tts',
  // ...
};
```

### Delivery Mode Explanation

- **`'streaming'`**: Attempts to use `/api/page/[id]/stream-tts` endpoint with `generateContentStream`
  - ❌ **Does NOT work** - generateContentStream not supported for TTS models
  - Falls back to batch synthesis after timeout (wasted 30 seconds)

- **`'chunked'`**: Uses `/api/page/[id]/ensure-tts` endpoint with `generateContent` (non-streaming)
  - ✅ **Works reliably** - uses officially supported non-streaming method
  - Returns complete audio immediately
  - **Recommended setting**

### If You Want True Streaming

If you need true real-time streaming audio (not just fast generation), use the **Live API** instead:

```typescript
// In lib/config.ts
geminiTtsMethod: 'genai_live',  // Uses WebSocket-based Live API
```

This uses `streamTtsViaGenAiLive()` which:
- Connects via WebSocket
- Returns true streaming audio chunks
- More complex, but actually works for streaming

**Trade-offs:**
- ✅ True streaming (chunks arrive in real-time)
- ✅ Lower latency for long texts
- ❌ More complex (WebSocket connection)
- ❌ Requires handling bufferutil native modules
- ❌ More potential failure points

## Next Steps

1. **Test the fixes**: Run `npx tsx scripts/test-streaming-tts.ts`
2. **Monitor logs**: Check for detailed streaming logs in production
3. **Verify fallback**: Ensure batch synthesis works if streaming fails
4. **Report issues**: If streaming still doesn't work, the enhanced logging will show exactly where it's failing

## Files Modified

1. `lib/ai/providers/gemini.ts` - Enhanced `streamTtsViaGenAiSdk` with better error handling
2. `app/api/page/[id]/stream-tts/route.ts` - Improved timeout strategy and error logging
3. `scripts/test-streaming-tts.ts` - New diagnostic test script (created)
4. `docs/STREAMING_TTS_FIX.md` - This documentation (created)

## API Reference

### streamTtsViaGenAiSdk

```typescript
async function* streamTtsViaGenAiSdk(
  text: string,
  voice: string,
  languageCode?: string
): AsyncGenerator<Buffer, void, unknown>
```

Streams TTS audio chunks from Google GenAI SDK.

**Throws:**
- `Error` if API key is missing
- `Error` if voice name is not configured
- `Error` if stream creation fails
- `Error` if no chunks are received

**Yields:** Audio chunks as `Buffer` objects

### Stream-TTS Endpoint

```
GET /api/page/[id]/stream-tts
```

Returns complete MP3 audio for a page, using streaming generation with fallback to batch synthesis.

**Response:**
- `200` with `audio/mpeg` content
- `403` if unauthorized or quota exceeded
- `500` if both streaming and fallback synthesis fail
