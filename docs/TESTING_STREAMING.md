# Testing Streaming TTS

Quick guide to test the streaming TTS implementation.

## 1. Enable Streaming Mode

Edit `lib/config.ts`:

```typescript
export const appConfig: AppConfig = {
  // ... other settings ...
  ttsDeliveryMode: 'streaming', // Change to 'streaming'
};
```

## 2. Restart Dev Server

```bash
npm run dev
```

## 3. Upload a Test Document

1. Go to http://localhost:3000
2. Upload a PDF (2-3 pages recommended)
3. Wait for processing to complete
4. Navigate to the document view page

## 4. Check Logs

Open browser console and backend terminal to see logs:

### Backend Terminal (Expected Logs)

**When ensure-tts is called:**
```
[ensure-tts] Streaming mode enabled, using stream-tts endpoint
```

**When streaming starts:**
```
[stream-tts] Starting stream for page 1
[tts-stream] connecting... { model: 'models/gemini-2.5-flash-native-audio-preview-09-2025', voice: '<PERSON><PERSON><PERSON><PERSON>' }
[tts-stream] opened
[tts-stream] sending text { chars: 1234 }
[tts-stream] yielding chunk { chunk: 1, bytes: 4096 }
[tts-stream] yielding chunk { chunk: 2, bytes: 4096 }
...
[tts-stream] turn complete
[tts-stream] complete { totalChunks: 15 }
[stream-tts] Stream complete { totalChunks: 15, totalBytes: 61440 }
[stream-tts] Audio cached { page: 1, durationSec: 12, mp3Path: '...' }
```

**On subsequent plays (cached):**
```
[stream-tts] Audio already cached, returning URL
```

### Browser Console (Expected Logs)

**First play:**
```
[ensure-audio] Requesting audio for page page-xyz
[ensure-audio] Using streaming mode /api/page/page-xyz/stream-tts
[streaming-audio] Starting stream from /api/page/page-xyz/stream-tts
[streaming-audio] First chunk received, converting to audio...
[streaming-audio] Playing audio buffer { duration: 2.5, chunks: 1 }
[streaming-audio] Stream complete { totalBytes: 61440 }
[streaming-audio] Created audio URL { totalBytes: 61440, wavSize: 123000 }
[ensure-audio] Stream complete, audio ready: blob:http://localhost:3000/...
```

**Second play (cached):**
```
[ensure-audio] Requesting audio for page page-xyz
[ensure-audio] Using streaming mode /api/page/page-xyz/stream-tts
[streaming-audio] Starting stream from /api/page/page-xyz/stream-tts
[streaming-audio] Using cached audio /api/audio/audio-xyz
[ensure-audio] Audio already complete: /api/audio/audio-xyz
```

## 5. Verify Behavior

### ✅ Expected Behavior (Streaming Mode)

- Audio starts playing within **1-2 seconds** of clicking play
- No gaps or pauses during playback
- Continuous smooth audio
- Browser console shows `[streaming-audio]` logs
- Backend shows `[tts-stream]` logs
- Second play is instant (uses cached MP3)

### ❌ Old Behavior (Chunked Mode)

For comparison, if you switch back to chunked mode:

```typescript
// lib/config.ts
ttsDeliveryMode: 'chunked',
```

You'll see:
- Audio takes **5-10 seconds** to start
- Noticeable **2-5 second gaps** between chunks
- Backend shows `<><><> TTS GenAI SDK` logs
- Console shows chunk-based prefetching

## 6. Test Edge Cases

### Test 1: Page Already Processed

1. Play a page once (streaming)
2. Refresh page
3. Play same page again
4. Should use cached audio instantly

**Expected:** No streaming, immediate playback from cached MP3

### Test 2: Network Interruption

1. Start playing a page
2. Open DevTools → Network tab
3. Throttle to "Slow 3G"
4. Click play on new page

**Expected:** May buffer slightly but should still stream

### Test 3: Multiple Pages

1. Play page 1 (streams)
2. Skip to page 2 (streams)
3. Go back to page 1 (cached)

**Expected:** Pages 1 & 2 stream on first play, instant on repeat

### Test 4: Anonymous User Limit

1. Play audio without logging in
2. Consume ~60 seconds (free trial limit)
3. Try to play more

**Expected:** Server blocks streaming with `requireLogin: true`

## 7. Compare Performance

### Measure Latency

Add this to your player component:

```typescript
const startTime = Date.now();

await ensurePageAudio(pageId, anonSid);

const latency = Date.now() - startTime;
console.log('[perf] Time to audio:', latency, 'ms');

// Streaming: 1000-2000ms
// Chunked: 5000-10000ms
```

### Measure Gaps

For chunked mode, track gap time:

```typescript
audioElement.addEventListener('waiting', () => {
  console.log('[perf] Buffering...');
  gapStart = Date.now();
});

audioElement.addEventListener('playing', () => {
  if (gapStart) {
    const gapMs = Date.now() - gapStart;
    console.log('[perf] Gap duration:', gapMs, 'ms');
  }
});

// Streaming: 0ms gaps
// Chunked: 2000-5000ms gaps
```

## 8. Debugging

### Issue: Still seeing chunked behavior

**Check:**
```bash
# Verify config
grep -n "ttsDeliveryMode" lib/config.ts

# Should show:
# ttsDeliveryMode: 'streaming',
```

**Restart server:**
```bash
# Kill old process
pkill -f "next dev"

# Start fresh
npm run dev
```

### Issue: No logs appearing

**Check:**
```bash
# Verify log level
export DEBUG=*
npm run dev

# Or check console.log/debug calls
grep -r "console.log" lib/ai/providers/gemini.ts
```

### Issue: Audio not playing

**Check browser console:**
- Look for errors about ReadableStream support
- Check for CORS errors
- Verify Web Audio API is available

**Check backend logs:**
- Look for `[tts-stream] failed:` errors
- Verify GEMINI_API_KEY is set
- Check for rate limiting errors

## 9. Rollback Test

Switch back to chunked mode:

```typescript
// lib/config.ts
ttsDeliveryMode: 'chunked', // Back to original
```

Restart server and verify old behavior returns:
- Chunked audio
- Gaps between chunks
- `<><><> TTS GenAI SDK` logs

## 10. Production Checklist

Before deploying streaming to production:

- [ ] Test with 10+ different PDFs
- [ ] Test on slow networks (3G)
- [ ] Test on mobile browsers (iOS Safari, Chrome Android)
- [ ] Verify anonymous user limits work
- [ ] Verify signed-in user limits work
- [ ] Check cache hit rate (should be >80% after initial play)
- [ ] Monitor error rates (should be <1%)
- [ ] Compare user engagement metrics (completion rate)
- [ ] Verify fallback to chunked works if streaming fails

## 11. Success Metrics

Track these metrics to measure success:

```typescript
// Add to your analytics
analytics.track('audio_play', {
  mode: isStreaming ? 'streaming' : 'chunked',
  latency: timeToFirstAudio,
  duration: totalDuration,
  gaps: gapCount,
  gapDuration: totalGapMs,
  cached: wasCached,
});
```

**Target metrics:**
- Time to first audio: <2s (streaming) vs 5-10s (chunked)
- Gap duration: 0ms (streaming) vs 2-5s (chunked)
- Cache hit rate: >80%
- Error rate: <1%
- Completion rate: >90%
