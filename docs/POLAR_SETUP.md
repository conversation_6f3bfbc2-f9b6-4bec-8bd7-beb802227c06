# Polar.sh Checkout Setup Guide

This guide explains how to configure Polar.sh payment integration for TransReed.

## Overview

TransReed uses [Polar.sh](https://polar.sh) as its payment infrastructure. Polar provides a simple checkout experience with built-in subscription management.

## Prerequisites

1. A Polar.sh account ([Sign up here](https://polar.sh))
2. Configured products and prices in your Polar dashboard
3. Polar Access Token

## Setup Steps

### 0. Seed Plans in Database

Before setting up Polar, ensure the subscription plans exist in your database:

```bash
npx tsx scripts/seed-plans.ts
```

This creates the following plans:
- **Free**: $0/month - 5 minutes
- **Starter**: $5/month - 1 hour
- **Pro**: $19/month - 5 hours
- **Premium**: $49/month - 15 hours

### 1. Create Products in Polar Dashboard

1. Log in to your [Polar Dashboard](https://polar.sh/dashboard)
2. Navigate to **Products**
3. Create three subscription products matching our pricing tiers:
   - **Starter**: $5/month - 1 hour listening time
   - **Pro**: $19/month - 5 hours listening time
   - **Premium**: $49/month - 15 hours listening time

4. For each product, create a **Price** and note the **Product ID** (format: `prod_xxxxx`) or **Price ID** (format: `priceId_xxxxx`)

### 2. Get Your Polar Access Token

1. Go to [Polar Settings](https://polar.sh/settings)
2. Navigate to **API** or **Access Tokens**
3. Create a new access token with checkout permissions
4. Copy the token (format: `polar_at_xxxxx`)

### 3. Configure Environment Variables

Add the following to your `.env` or `.env.local` file:

```bash
# Polar Access Token (required)
POLAR_ACCESS_TOKEN=polar_at_your_token_here

# Server mode: 'sandbox' for testing, 'production' for live
POLAR_SERVER=sandbox

# Product IDs (or Price IDs) for each tier (copy from Polar dashboard)
# Can use either Product ID (prod_xxx) or Price ID (priceId_xxx)
NEXT_PUBLIC_POLAR_PRODUCT_ID_STARTER=prod_starter_xxx
NEXT_PUBLIC_POLAR_PRODUCT_ID_PRO=prod_pro_xxx
NEXT_PUBLIC_POLAR_PRODUCT_ID_PREMIUM=prod_premium_xxx
```

### 4. Configure Success URL

The success URL is automatically configured based on `NEXT_PUBLIC_BASE_URL`:
- **Development**: `http://localhost:3000/success?checkout_id={CHECKOUT_ID}`
- **Production**: `https://your-domain.com/success?checkout_id={CHECKOUT_ID}`

Make sure `NEXT_PUBLIC_BASE_URL` is set correctly in your environment:

```bash
# Development
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Production
NEXT_PUBLIC_BASE_URL=https://transreed.com
```

## How It Works

### Checkout Flow

1. **User clicks "Get Started"** in PricingModal
2. **Frontend** redirects to `/api/polar/checkout?products={productId}`
3. **API Route** (`app/api/polar/checkout/route.ts`):
   - Authenticates user via Clerk
   - Fetches user details (email)
   - Pre-fills customer information
   - Redirects to Polar hosted checkout
4. **User completes payment** on Polar checkout page
5. **Polar redirects** back to `/success?checkout_id={CHECKOUT_ID}`
6. **Success page** displays confirmation and order details

**Note**: Polar expects the query parameter to be named `products`, not `productId`. You can pass either a product ID or a price ID as the value.

### Customer Linking

The checkout route automatically links Polar customers to your users:
- `customerEmail`: Pre-filled from Clerk user email
- `customerExternalId`: Set to Clerk `userId` for linking subscriptions

This ensures that when you receive webhooks from Polar, you can identify which user in your database the subscription belongs to.

## Testing

### Sandbox Mode

Use `POLAR_SERVER=sandbox` to test payments without real charges:

1. Set `POLAR_SERVER=sandbox` in `.env`
2. Use test card numbers provided by Polar
3. Verify the flow works end-to-end
4. Check the success page displays correctly

### Production Mode

When ready to go live:

1. Set `POLAR_SERVER=production` (or remove the variable)
2. Update all environment variables with production values
3. Test with a small real transaction
4. Monitor the success page and webhooks

## Plan Update Flow

The app updates user plans through two mechanisms:

### 1. Automatic Update on Success Page (Primary)

When a user completes checkout and returns to the success page:
- The success page calls `/api/polar/verify-checkout` with the checkout ID
- The API verifies the checkout with Polar and updates the user's plan in the database
- This happens automatically without requiring webhook setup

### 2. Webhooks (Recommended for Production)

For production deployments, set up Polar webhooks to handle subscription events:

1. In Polar Dashboard, go to **Webhooks**
2. Add webhook URL: `https://your-domain.com/api/polar/webhook`
3. Select events to subscribe to:
   - **Subscription events** (recommended):
     - `subscription.created` - New subscription established
     - `subscription.updated` - Subscription details changed
     - `subscription.active` - Subscription becomes active
     - `subscription.canceled` - Subscription cancellation requested
     - `subscription.uncanceled` - Canceled subscription restored
     - `subscription.revoked` - Subscription definitively ends
   - **Order events** (for renewals):
     - `order.created` - Tracks renewals and one-time payments
     - `order.paid` - Payment successfully processed
   - **Checkout events** (optional):
     - `checkout.created` - Checkout initiated
     - `checkout.updated` - Checkout details modified
4. Copy the webhook secret and add to `.env`:
   ```bash
   POLAR_WEBHOOK_SECRET=your_webhook_secret_here
   ```

The webhook handler (`app/api/polar/webhook/route.ts`) automatically:
- Verifies webhook signatures using Polar SDK for security
- Updates user plan when subscription is created or modified
- Handles subscription lifecycle (activation, cancellation, renewal)
- Creates usage tracking records for billing cycles
- Logs all payment events for audit trail
- Automatically downgrades to free plan when subscription is revoked

## Webhook Event Reference

### Subscription Lifecycle Events

#### subscription.created
Triggered when a new subscription is established (may not be active yet).

**Handler actions:**
- Updates user's plan to the subscribed tier
- Sets billing period dates
- Creates usage tracking record
- Logs payment event

#### subscription.active
Triggered when subscription becomes active (first payment processed).

**Handler actions:**
- Activates subscription status
- Updates billing period
- Logs activation event

#### subscription.updated
Catch-all event for subscription changes (plan upgrades/downgrades, renewal).

**Handler actions:**
- Updates plan and billing details
- Synchronizes subscription status
- Logs update event

#### subscription.canceled
Triggered when user cancels subscription.

**Handler actions:**
- If `cancel_at_period_end`: Keeps subscription active until period end
- If immediate: Sets status to canceled
- Logs cancellation event

#### subscription.uncanceled
Triggered when user reverses cancellation.

**Handler actions:**
- Reactivates subscription
- Logs uncancellation event

#### subscription.revoked
Triggered when subscription definitively ends (access revoked).

**Handler actions:**
- Downgrades user to free plan
- Sets status to inactive
- Clears billing period
- Logs revocation event

### Order Events

#### order.created
Triggered for all payment events (new subscription, renewal, upgrade).

**Handler actions:**
- Logs order with billing reason
- If renewal (`billing_reason: 'subscription_cycle'`):
  - Creates new usage cycle for the billing period

#### order.paid
Triggered when payment is successfully processed.

**Handler actions:**
- Logs successful payment

### Checkout Events

#### checkout.created
Triggered when checkout is initiated.

**Handler actions:**
- Logs checkout creation for tracking

#### checkout.updated
Triggered when checkout status changes.

**Handler actions:**
- If status is `confirmed` or `succeeded`:
  - Logs successful checkout completion

## Troubleshooting

### "Unauthorized" Error

- Verify `POLAR_ACCESS_TOKEN` is set correctly
- Check that the user is logged in (Clerk authentication required)
- Ensure the token has not expired

### Checkout Doesn't Open

- Check browser console for errors
- Verify price IDs are correct
- Ensure `NEXT_PUBLIC_BASE_URL` is set
- Check that `@polar-sh/nextjs` package is installed

### Success Page Not Working

- Verify `NEXT_PUBLIC_BASE_URL` matches your domain
- Check that the success route exists at `/success`
- Ensure the checkout_id parameter is being passed

### Plan Not Updating After Checkout

If your plan doesn't update after completing checkout:

1. **Check browser console** on the success page for errors
2. **Verify environment variables**:
   - `POLAR_ACCESS_TOKEN` is set correctly
   - Product IDs match between `.env` and Polar dashboard
3. **Check server logs** for API errors:
   ```bash
   # Look for errors from /api/polar/verify-checkout
   npm run dev
   ```
4. **Manually verify** the checkout via API:
   - The success page should automatically call `/api/polar/verify-checkout`
   - Check Network tab in browser DevTools for the API response
5. **Database connection**: Ensure `DATABASE_URL` is correct and database is accessible

## Support

- [Polar Documentation](https://polar.sh/docs)
- [Polar Discord](https://discord.gg/polar)
- [Polar GitHub](https://github.com/polarsource/polar)

## Related Files

- **Checkout Route**: `app/api/polar/checkout/route.ts`
- **Pricing Modal**: `app/(protected)/components/PricingModal.tsx`
- **Success Page**: `app/(protected)/success/page.tsx`
- **Environment Config**: `.env.example`
